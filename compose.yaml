services:
  app:
    image: xxxxxxx.dkr.ecr.eu-central-1.amazonaws.com/audiofile-repo:20250816075924
    container_name: audiovaultapp
    restart: always
    ports:
      - 8081:8081
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
    networks:
      - audio_network
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=8081
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=dev
      - DB_PASSWORD=Ahadho13
      - DB_DBNAME=audio_management_dev
      - DB_DIALECT=postgres
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_123
      - JWT_SECRET=your-super-secret-jwt-key-for-development-only-change-in-production
      - JWT_EXPIRES_IN=24h
    env_file:
      - .env

  postgres:
    image: postgres:15
    ports:
      - 5433:5432
    container_name: audio_vault_db
    environment:
      - POSTGRES_USER=dev
      - POSTGRES_PASSWORD=Ahadho13
      - POSTGRES_DB=audio_management_dev
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - audio_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: audio_vault_redis
    restart: always
    command: redis-server --appendonly yes --requirepass redis_password_123
    volumes:
      - redis:/data
    ports:
      - 6379:6379
    networks:
      - audio_network

networks:
  audio_network:
    driver: bridge

volumes:
  postgres:
  redis:
  uploads:
  logs:
