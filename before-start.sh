#!/bin/bash
set -euo pipefail

# --- Docker Permission Fix Start ---
# Ensure the docker group exists
if ! getent group docker >/dev/null; then
    echo "Creating docker group..."
    sudo groupadd docker
fi

# Add the current user (CodeDeploy runs as root or a system user) to the docker group
echo "Adding user $USER to docker group..."
sudo usermod -aG docker $USER

# Optional: Apply docker group in  current shell (may not persist across CodeDeploy sessions)
newgrp docker <<EONG
echo "Running docker group shell to apply permissions..."
EONG

# Test Docker access (optional)
docker ps || echo "Still no Docker access. You may need to restart the session."
# --- Docker Permission Fix End ---

TARGET_DIR="/var/AudioFile-App"

echo "Ensuring ${TARGET_DIR} is clean and ready..."

# Delete directory if it exists
if [ -d "${TARGET_DIR}" ]; then
    echo "Removing existing directory and contents..."
    sudo rm -rf "${TARGET_DIR}"
fi

# Create directory (whether it existed before or not)
echo "Creating fresh directory..."
sudo mkdir -p "${TARGET_DIR}"

# Ensure correct permissions (optional, adjust as needed)
sudo chmod 755 "${TARGET_DIR}"

echo "Directory ${TARGET_DIR} is now ready for deployment."
