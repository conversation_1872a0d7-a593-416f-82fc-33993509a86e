# API Documentation

## Base URL
```
http://localhost:3000/api
```

## Authentication
All API endpoints (except login) require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Authentication

#### POST /auth/login
Login with username and password.

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

> **Note:** Test credentials are configured via environment variables. Check the `.env` file for current test credentials.

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt-token-here",
  "user": {
    "id": 1,
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

#### POST /auth/logout
Logout current user.

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

#### GET /auth/me
Get current user information.

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### Files

#### GET /files
List audio files with pagination and filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search term for file names
- `sortBy` (optional): Sort field (name, size, lastModified)
- `sortOrder` (optional): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "key": "audio/song.mp3",
        "name": "song.mp3",
        "size": 5242880,
        "lastModified": "2024-01-15T10:30:00Z",
        "extension": "mp3"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 100,
      "itemsPerPage": 20,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

#### GET /files/:key/download
Generate signed URL for file download.

**Response:**
```json
{
  "success": true,
  "downloadUrl": "https://s3.amazonaws.com/bucket/file.mp3?signed-url-params"
}
```

#### GET /files/:key/stream
Generate signed URL for audio streaming.

**Response:**
```json
{
  "success": true,
  "streamUrl": "https://s3.amazonaws.com/bucket/file.mp3?signed-url-params"
}
```

#### POST /files/bulk-download
Generate signed URLs for multiple files.

**Request Body:**
```json
{
  "fileKeys": ["audio/song1.mp3", "audio/song2.mp3"]
}
```

**Response:**
```json
{
  "success": true,
  "downloadUrls": [
    {
      "key": "audio/song1.mp3",
      "url": "https://s3.amazonaws.com/bucket/song1.mp3?signed-url-params",
      "success": true
    },
    {
      "key": "audio/song2.mp3",
      "url": "https://s3.amazonaws.com/bucket/song2.mp3?signed-url-params",
      "success": true
    }
  ]
}
```

### Users

#### GET /users
Get all users (admin only).

**Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### GET /users/:id
Get user by ID.

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

## Error Responses

All error responses follow this format:
```json
{
  "success": false,
  "message": "Error description",
  "details": "Additional error details (optional)"
}
```

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error
