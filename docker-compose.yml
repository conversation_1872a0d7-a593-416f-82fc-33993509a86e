name: Audio_File_Management
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: audio_vault_app
    restart: always
    ports:
      - ${PORT}:${PORT}
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
    networks:
      - audio_network
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    environment:
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DBNAME=${DB_DBNAME}
      - DB_DIALECT=${DB_DIALECT}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}


  postgres:
    image: postgres:15
    ports:
      - ${POSTGRES_EXTERNAL_PORT}:5432
    container_name: audio_vault_db
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - audio_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: audio_vault_redis
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis:/data
    ports:
      - ${REDIS_EXTERNAL_PORT}:6379
    networks:
      - audio_network

networks:
  audio_network:
    driver: bridge

volumes:
  postgres:
  redis:
  uploads:
  logs:
