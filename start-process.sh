#!/bin/bash

set -euo pipefail

# Detect environment from deployment group
detect_environment() {
    local environment="PROD"  # Default
    
    # Try multiple methods to detect deployment group/environment
    if [[ -n "${DEPLOYMENT_GROUP_NAME:-}" ]]; then
        log "Detected deployment group from CodeDeploy: $DEPLOYMENT_GROUP_NAME"
        if [[ "$DEPLOYMENT_GROUP_NAME" == *"UAT"* ]]; then
            environment="UAT"
        elif [[ "$DEPLOYMENT_GROUP_NAME" == *"Prod"* ]]; then
            environment="PROD"
        fi
    elif [[ -n "${CODEDEPLOY_DEPLOYMENT_GROUP_NAME:-}" ]]; then
        log "Detected deployment group from CodeDeploy: $CODEDEPLOY_DEPLOYMENT_GROUP_NAME"
        if [[ "$CODEDEPLOY_DEPLOYMENT_GROUP_NAME" == *"UAT"* ]]; then
            environment="UAT"
        elif [[ "$CODEDEPLOY_DEPLOYMENT_GROUP_NAME" == *"Prod"* ]]; then
            environment="PROD"
        fi
    elif [[ -n "${AWS_CODEDEPLOY_DEPLOYMENT_GROUP_NAME:-}" ]]; then
        log "Detected deployment group from CodeDeploy: $AWS_CODEDEPLOY_DEPLOYMENT_GROUP_NAME"
        if [[ "$AWS_CODEDEPLOY_DEPLOYMENT_GROUP_NAME" == *"UAT"* ]]; then
            environment="UAT"
        elif [[ "$AWS_CODEDEPLOY_DEPLOYMENT_GROUP_NAME" == *"Prod"* ]]; then
            environment="PROD"
        fi
    else
        log "⚠️ No deployment group detected, using default environment: $environment"
        log "Available environment variables:"
        env | grep -i deploy || log "No deployment-related environment variables found"
    fi
    
    echo "$environment"
}

# Configuration
TARGET_DIR="/var/AudioFile-App/deployment"
AWS_REGION="eu-central-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REPO_URL="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/audiofile-repo"

# Detect environment and set secret name accordingly
ENVIRONMENT=$(detect_environment)
SECRET_NAME="audiofile-deployment-${ENVIRONMENT}"
SNS_TOPIC_ARN="arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:DevSecOps-AudioFile-Alert"

log() { echo "[$(date +'%H:%M:%S')] $1"; }

notify_failure() {
    echo "❌ ERROR: $1" >&2
    local db_info=""
    if [[ -f "$TARGET_DIR/.env" ]]; then
        local db_host=$(grep "^DATABASE_HOST=" "$TARGET_DIR/.env" | cut -d'=' -f2- | tr -d '"' 2>/dev/null || echo "unknown")
        local db_name=$(grep "^DATABASE_NAME=" "$TARGET_DIR/.env" | cut -d'=' -f2- | tr -d '"' 2>/dev/null || echo "unknown")
        db_info="Database: ${db_name} on ${db_host}"
    fi
    
    aws sns publish --topic-arn "$SNS_TOPIC_ARN" --subject "❌ AudioFile ${ENVIRONMENT} Deployment Failed" \
        --message "Environment: ${ENVIRONMENT}
Secret Used: ${SECRET_NAME}
${db_info}

Error: $1
Time: $(date)" --region "$AWS_REGION" 2>/dev/null || true
    exit 1
}

setup_directory() {
    log "Setting up deployment directory for ${ENVIRONMENT} environment..."
    
    # Debug current environment
    log "Current user: $(whoami)"
    log "Current directory: $(pwd)"
    log "Target directory: $TARGET_DIR"
    log "Environment: $ENVIRONMENT"
    log "Secret name: $SECRET_NAME"
    
    # Check if target directory already exists
    if [[ -d "$TARGET_DIR" ]]; then
        log "✅ Target directory already exists"
        
        # Check if compose.yaml exists in target directory
        if [[ -f "$TARGET_DIR/compose.yaml" ]]; then
            log "✅ compose.yaml found in target directory"
        else
            notify_failure "compose.yaml not found in $TARGET_DIR"
        fi
    else
        # Create directory if it doesn't exist
        if ! sudo -n true 2>/dev/null; then
            mkdir -p "$TARGET_DIR" 2>/dev/null || notify_failure "Failed to create directory $TARGET_DIR"
        else
            sudo mkdir -p "$TARGET_DIR" || notify_failure "Failed to create directory $TARGET_DIR"
            sudo chown -R $(whoami):$(whoami) "$TARGET_DIR" || log "Failed to change ownership, continuing..."
        fi
    fi
    
    # List files in target directory for debugging
    log "Files in target directory:"
    ls -la "$TARGET_DIR" || notify_failure "Cannot access target directory"
    
    log "✅ Directory setup completed"
}
ENVIRONMENT=$(detect_environment)

# Set environment-specific secret name
case "$ENVIRONMENT" in
    "UAT")
        SECRET_NAME="audio-manager-uat"
        ;;
    "PROD")
        SECRET_NAME="audio-manager-prod"
        ;;
esac
load_secrets() {
    log "Loading secrets from ${SECRET_NAME} and creating .env file..."
    
    # Store original AWS identity (should be EC2 instance role)
    local original_identity=$(aws sts get-caller-identity --query 'Arn' --output text 2>/dev/null)
    log "Using EC2 instance role: $original_identity"
    
    local secrets=$(aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" \
        --region "$AWS_REGION" --query SecretString --output text) || \
        notify_failure "Failed to retrieve secrets from $SECRET_NAME"
    
    # Backup existing .env file if it exists
    if [[ -f "$TARGET_DIR/.env" ]]; then
        cp "$TARGET_DIR/.env" "$TARGET_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
        log "✅ Backed up existing .env file"
    fi
    
    # Create .env file from secrets (include AWS credentials for Django app)
    local env_content=""
    if command -v python3 >/dev/null 2>&1; then
        env_content=$(echo "$secrets" | python3 -c "
import sys, json, re
data = json.load(sys.stdin)
for key, value in data.items():
    # Include all variables (Django app needs AWS credentials)
    # Escape special characters in values
    escaped_value = str(value).replace('\"', '\\\"')
    # Quote values that contain special characters
    if re.search(r'[#!@$%^&*(){}|<>?\s]', str(value)):
        print(f'{key}=\"{escaped_value}\"')
    else:
        print(f'{key}={value}')
")
    elif command -v python >/dev/null 2>&1; then
        env_content=$(echo "$secrets" | python -c "
import sys, json, re
data = json.load(sys.stdin)
for key, value in data.items():
    # Include all variables (Django app needs AWS credentials)
    # Escape special characters in values
    escaped_value = str(value).replace('\"', '\\\"')
    # Quote values that contain special characters
    if re.search(r'[#!@\$%\^&\*\(\)\{\}\|<>\?\s]', str(value)):
        print key + '=\"' + escaped_value + '\"'
    else:
        print key + '=' + str(value)
")
    else
        notify_failure "Neither python3 nor python found - cannot parse secrets JSON"
    fi
    
    # Add environment indicator to .env file
    env_content="# Environment: ${ENVIRONMENT}
# Generated: $(date)
${env_content}"
    
    # Write .env file with proper permissions handling
    if [[ -w "$TARGET_DIR" ]]; then
        echo "$env_content" > "$TARGET_DIR/.env"
    else
        echo "$env_content" | sudo tee "$TARGET_DIR/.env" > /dev/null
        sudo chown $(whoami):$(whoami) "$TARGET_DIR/.env" 2>/dev/null || true
    fi
    
    # Verify we're still using the EC2 instance role
    local current_identity=$(aws sts get-caller-identity --query 'Arn' --output text 2>/dev/null)
    log "Confirmed using EC2 instance role: $current_identity"
    
    # Show .env file stats
    local env_vars=$(wc -l < "$TARGET_DIR/.env" 2>/dev/null || echo "0")
    log "✅ Created .env file for ${ENVIRONMENT} with $env_vars variables"
}


update_compose() {
    log "Getting latest image and updating compose file for ${ENVIRONMENT}..."
    
    # First try to get image from imagemanifest.json (created by build process)
    local IMAGE_URI=""
    if [[ -f "$TARGET_DIR/imagemanifest.json" ]]; then
        log "Reading image URI from imagemanifest.json..."
        if command -v python3 >/dev/null 2>&1; then
            IMAGE_URI=$(python3 -c "import json; print(json.load(open('$TARGET_DIR/imagemanifest.json'))['ImageURI'])" 2>/dev/null)
        elif command -v python >/dev/null 2>&1; then
            IMAGE_URI=$(python -c "import json; print(json.load(open('$TARGET_DIR/imagemanifest.json'))['ImageURI'])" 2>/dev/null)
        fi
        
        if [[ -n "$IMAGE_URI" ]]; then
            log "✅ Found image URI from manifest: $IMAGE_URI"
        else
            log "⚠️ Could not parse imagemanifest.json, trying ECR query..."
        fi
    fi
    
    # Fallback to ECR query if manifest not available or failed
    if [[ -z "$IMAGE_URI" ]]; then
        log "Querying ECR for latest image..."
        local latest_tag=$(aws ecr describe-images --repository-name "audiofile-repo" \
            --region "$AWS_REGION" \
            --query "sort_by(imageDetails[?imageTags!=null], &imagePushedAt)[-1].imageTags[0]" \
            --output text 2>/dev/null | tr -d '\n\r')
        
        if [[ -n "$latest_tag" && "$latest_tag" != "None" ]]; then
            IMAGE_URI="$ECR_REPO_URL:$latest_tag"
            log "✅ Found latest tag from ECR: $latest_tag"
        else
            notify_failure "No images found in ECR and no valid imagemanifest.json"
        fi
    fi
    
    local COMPOSE_PATH="$TARGET_DIR/compose.yaml"
    log "Updating compose file with: $IMAGE_URI"
    
    # Update ECR image lines directly (works with both sudo and regular permissions)
    if [[ -w "$COMPOSE_PATH" ]]; then
        sed -i -E "s|(^[[:space:]]*image:[[:space:]]*)['\"]?[^'\"]*\.dkr\.ecr\.[^'\"]*['\"]?|\1$IMAGE_URI|g" \
            "$COMPOSE_PATH" || notify_failure "Failed to update compose file"
    else
        sudo sed -i -E "s|(^[[:space:]]*image:[[:space:]]*)['\"]?[^'\"]*\.dkr\.ecr\.[^'\"]*['\"]?|\1$IMAGE_URI|g" \
            "$COMPOSE_PATH" || notify_failure "Failed to update compose file"
    fi
    
    # Extract tag for verification
    local tag=$(echo "$IMAGE_URI" | sed 's/.*://')
    
    # Verify update
    if grep -q "$tag" "$COMPOSE_PATH"; then
        log "✅ Compose file updated successfully with image: $IMAGE_URI"
    else
        notify_failure "Failed to verify compose file update"
    fi
}
deploy() {
    log "Deploying containers for ${ENVIRONMENT} environment..."
    cd "$TARGET_DIR"
    
    # Try ECR login but don't fail if it doesn't work
    log "Attempting ECR authentication..."
    if aws ecr get-login-password --region "$AWS_REGION" 2>/dev/null | \
        docker login --username AWS --password-stdin "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com" 2>/dev/null; then
        log "✅ ECR authentication successful"
    else
        log "⚠️ ECR authentication failed - proceeding with existing Docker credentials"
        log "Note: You may need to add ecr:GetAuthorizationToken permission to your IAM role/user"
    fi
    
    # More thorough container cleanup
    log "Cleaning up existing containers..."
    
    # First try docker compose down
    docker compose down --remove-orphans --volumes 2>/dev/null || true
    
    # Then force remove specific containers if they still exist
    local containers=("audio_vault_app" "audio_vault_db" "audio_vault_redis")
    for container in "${containers[@]}"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "^${container}$"; then
            log "Removing existing container: $container"
            docker rm -f "$container" 2>/dev/null || true
        fi
    done
    
    # Wait a moment for cleanup to complete
    sleep 2
    
    # Verify cleanup
    local remaining_containers=$(docker ps -a --filter "name=audio_vault" --format "{{.Names}}" | wc -l)
    if [ "$remaining_containers" -gt 0 ]; then
        log "⚠️ Some audio_vault containers still exist:"
        docker ps -a --filter "name=audio_vault" --format "table {{.Names}}\t{{.Status}}"
        log "Force removing all audio_vault containers..."
        docker ps -a --filter "name=audio_vault" --format "{{.Names}}" | xargs -r docker rm -f
    fi
    
    # Try to pull images
    log "Attempting to pull images..."
    if docker compose pull 2>/dev/null; then
        log "✅ Images pulled successfully"
    else
        log "⚠️ Failed to pull some images - proceeding with existing local images"
        log "Checking available images:"
        docker images | grep audiofile || log "No audiofile images found locally"
    fi
    
    # Start containers with explicit recreation
    log "Starting containers for ${ENVIRONMENT}..."
    if docker compose up -d --force-recreate; then
        log "✅ Containers started"
    else
        log "❌ Failed to start containers, showing logs:"
        docker compose logs --tail=30 2>/dev/null || true
        
        # Show any remaining conflicting containers
        log "Checking for container name conflicts..."
        docker ps -a --filter "name=audio_vault" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
        
        notify_failure "Failed to start containers"
    fi
    
    # Wait and check health
    log "Waiting for containers to initialize..."
    sleep 30
    
    local running=$(docker compose ps --services --filter "status=running" 2>/dev/null | wc -l)
    local total=$(docker compose config --services 2>/dev/null | wc -l)
    
    if [ "$running" -eq "$total" ]; then
        log "✅ All $total services running for ${ENVIRONMENT}"
        docker compose ps
        
        # Test application health
        log "Testing application health..."
        sleep 10
        if curl -f http://localhost:8081/health/ >/dev/null 2>&1; then
            log "✅ ${ENVIRONMENT} application health check passed"
            
            # Get database info for success notification
            local db_host=$(grep "^DATABASE_HOST=" "$TARGET_DIR/.env" | cut -d'=' -f2- | tr -d '"' 2>/dev/null || echo "unknown")
            local db_name=$(grep "^DATABASE_NAME=" "$TARGET_DIR/.env" | cut -d'=' -f2- | tr -d '"' 2>/dev/null || echo "unknown")
            
            aws sns publish --topic-arn "$SNS_TOPIC_ARN" --subject "✅ AudioFile ${ENVIRONMENT} Deployment Successful" \
                --message "Environment: ${ENVIRONMENT}
Secret Used: ${SECRET_NAME}
Database: ${db_name} on ${db_host}

Deployment completed successfully for ${ENVIRONMENT}
Time: $(date)

Services Status:
$(docker compose ps --format 'table {{.Name}}\t{{.Status}}' 2>/dev/null || echo 'Status unavailable')" --region "$AWS_REGION" 2>/dev/null || true
        else
            log "⚠️ ${ENVIRONMENT} application health check failed - check container logs"
        fi
    else
        log "❌ Only $running of $total services running for ${ENVIRONMENT}"
        log "Container status:"
        docker compose ps 2>/dev/null || true
        
        # Show which services are failing
        log "Failed services:"
        docker compose ps --format "table {{.Name}}\t{{.Status}}" | grep -v "Up" || log "Could not determine failed services"
        
        log "Recent logs from failed services:"
        # Get logs from containers that are not running
        for service in $(docker compose config --services); do
            if ! docker compose ps "$service" | grep -q "Up"; then
                log "=== Logs from $service ==="
                docker compose logs --tail=10 "$service" 2>/dev/null || log "No logs available for $service"
            fi
        done
        
        notify_failure "Deployment failed: Only $running of $total services running"
    fi
}

# Main execution
case "${1:-}" in
    "stop") 
        cd "$TARGET_DIR" && docker compose down 
        log "✅ Containers stopped"
        ;;
    "restart") 
        cd "$TARGET_DIR" && docker compose restart 
        log "✅ Containers restarted"
        ;;
    "logs") 
        cd "$TARGET_DIR" && docker compose logs -f 
        ;;
    "status") 
        cd "$TARGET_DIR" && docker compose ps 
        ;;
    "update-env")
        log "🔄 Updating environment variables for ${ENVIRONMENT}..."
        setup_directory
        load_secrets
        log "✅ Environment updated for ${ENVIRONMENT}! Run './start-process.sh restart' to apply changes."
        ;;
    "health")
        log "🔍 Checking application health..."
        cd "$TARGET_DIR"
        docker compose ps
        echo ""
        curl -f http://localhost:8081/health/ && echo "✅ Application is healthy" || echo "❌ Application health check failed"
        ;;
    *)
        log "🚀 Starting AudioFile deployment for ${ENVIRONMENT} environment..."
        setup_directory
        load_secrets
        update_compose
        deploy
        log "🎉 ${ENVIRONMENT} deployment completed!"
        ;;
esac