#!/usr/bin/env node

const axios = require('axios');

async function testSync() {
  try {
    console.log('🔐 Logging in as Fanikiwa admin...');
    
    // Login as Fanikiwa admin
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful!');
    
    console.log('🔄 Starting file sync...');
    
    // Trigger file sync
    const syncResponse = await axios.post('http://localhost:8080/api/files/sync', {}, {
      headers: {
        'Authorization': `<PERSON><PERSON> ${token}`
      }
    });
    
    console.log('✅ Sync completed!');
    console.log('📊 Results:', syncResponse.data);
    
    console.log('📁 Fetching file list...');
    
    // Get file list
    const filesResponse = await axios.get('http://localhost:8080/api/files?page=1&limit=20', {
      headers: {
        'Authorization': `Bear<PERSON> ${token}`
      }
    });
    
    console.log('📋 Files found:', filesResponse.data.data.files.length);
    console.log('🎵 Audio files:');
    filesResponse.data.data.files.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.name} (${file.key})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testSync();
