#!/bin/bash
set -euo pipefail
trap 'echo "❌ Error on line $LINENO. Exiting..."; exit 1' ERR

APP_NAME="audio_vault_app"
AWS_REGION="eu-central-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
SNS_TOPIC_ARN="arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:DevSecOps-AudioFile-Alert"

echo "🔍 Checking container state for: $APP_NAME"

container_id=$(docker ps -aqf "name=^${APP_NAME}$")

if [[ -z "$container_id" ]]; then
    echo "🛑 No container found for $APP_NAME"
    aws sns publish \
        --topic-arn "$SNS_TOPIC_ARN" \
        --subject "🛑 Container Not Found - $APP_NAME" \
        --message "No container found with name: $APP_NAME" \
        --region "$AWS_REGION" || true
    exit 1
fi

status=$(docker inspect --format='{{.State.Status}}' "$container_id")
logs=$(docker logs --tail 20 "$container_id" 2>/dev/null || echo "No logs available")

aws sns publish \
    --topic-arn "$SNS_TOPIC_ARN" \
    --subject "📦 Container Status: $status - $APP_NAME" \
    --message "Container '$APP_NAME' has status: $status\n\nRecent logs:\n$logs" \
    --region "$AWS_REGION" || true

echo "📤 Alert sent for status: $status"
