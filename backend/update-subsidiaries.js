#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

// Import models
const { Subsidiary, User, Role, UserRole } = require('./src/models');

async function updateSubsidiaries() {
  try {
    console.log('Updating subsidiaries to active status...');
    
    // Update subsidiaries to active
    const subsidiariesToActivate = ['PREMIERKE', 'PLATINUMUG', 'PREMIERUG'];
    
    for (const code of subsidiariesToActivate) {
      const [updatedRows] = await Subsidiary.update(
        { is_active: true },
        { where: { code } }
      );
      
      if (updatedRows > 0) {
        console.log(`Activated subsidiary: ${code}`);
      } else {
        console.log(`Subsidiary ${code} not found or already active`);
      }
    }
    
    console.log('Creating admin users for newly activated subsidiaries...');
    
    // Get active subsidiaries
    const subsidiaries = await Subsidiary.findAll({ where: { is_active: true } });
    const adminRole = await Role.findOne({ where: { code: 'ADMIN' } });
    
    if (!adminRole) {
      console.error('Admin role not found');
      return false;
    }
    
    for (const subsidiary of subsidiaries) {
      const adminEmail = `admin@${subsidiary.code.toLowerCase()}.com`;
      const [user, created] = await User.findOrCreate({
        where: { email: adminEmail },
        defaults: {
          username: `admin_${subsidiary.code.toLowerCase()}`,
          email: adminEmail,
          password_hash: 'admin123', // Will be hashed by the model hook
          first_name: 'System',
          last_name: 'Administrator',
          subsidiary_id: subsidiary.id,
          is_active: true
        }
      });
      
      if (created) {
        // Assign admin role
        await UserRole.findOrCreate({
          where: { user_id: user.id, role_id: adminRole.id },
          defaults: {
            user_id: user.id,
            role_id: adminRole.id,
            assigned_at: new Date(),
            is_active: true
          }
        });
        
        console.log(`Created admin user for ${subsidiary.name}: ${user.email}`);
      } else {
        console.log(`Admin user already exists for ${subsidiary.name}: ${user.email}`);
      }
    }
    
    console.log('Subsidiary update completed successfully!');
    return true;
  } catch (error) {
    console.error('Error updating subsidiaries:', error.message);
    return false;
  }
}

// Run the update
updateSubsidiaries().then((success) => {
  process.exit(success ? 0 : 1);
});
