const { Subsidiary, Role, User, UserRole } = require('../models');
const bcrypt = require('bcryptjs');

// Default subsidiaries with their AWS configurations
const getDefaultSubsidiaries = () => [
  {
    name: 'Spectrum Zambia',
    code: 'SPECTRUMZM',
    description: 'Spectrum Zambia telecommunications subsidiary',
    aws_access_key_id: process.env.SPECTRUM_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.SPECTRUM_AWS_SECRET_ACCESS_KEY,
    aws_region: process.env.SPECTRUM_AWS_REGION || 'eu-central-1',
    s3_bucket_name: process.env.SPECTRUM_S3_BUCKET,
    timezone: 'Africa/Lusaka',
    is_active: true
  },
  {
    name: 'Premier Fanikiwa',
    code: 'PREMIERFK',
    description: 'Premier Fanikiwa financial services subsidiary',
    aws_access_key_id: process.env.FANIKIWA_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.FANIKIWA_AWS_SECRET_ACCESS_KEY,
    aws_region: process.env.FANIKIWA_AWS_REGION || 'eu-central-1',
    s3_bucket_name: process.env.FANIKIWA_S3_BUCKET,
    timezone: 'Africa/Nairobi',
    is_active: true
  },
  {
    name: 'Platinum Kenya',
    code: 'PLATINUMKE',
    description: 'Platinum Kenya financial services subsidiary',
    aws_access_key_id: process.env.PLATINUM_KE_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.PLATINUM_KE_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PLATINUM_KE_S3_BUCKET,
    timezone: 'Africa/Nairobi',
    is_active: true
  },
  {
    name: 'Premier Kenya',
    code: 'PREMIERKE',
    description: 'Premier Kenya financial services subsidiary',
    aws_access_key_id: process.env.PREMIER_KE_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.PREMIER_KE_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PREMIER_KE_S3_BUCKET,
    timezone: 'Africa/Nairobi',
    is_active: true
  },
  {
    name: 'Momentum Credit',
    code: 'MOMENTUM',
    description: 'Momentum Credit financial services subsidiary',
    aws_access_key_id: process.env.MOMENTUM_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.MOMENTUM_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.MOMENTUM_S3_BUCKET,
    timezone: 'Africa/Nairobi',
    is_active: true // Activated - credentials provided
  },
  {
    name: 'Platinum Tanzania',
    code: 'PLATINUMTZ',
    description: 'Platinum Tanzania financial services subsidiary',
    aws_access_key_id: process.env.PLATINUM_TZ_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.PLATINUM_TZ_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PLATINUM_TZ_S3_BUCKET,
    timezone: 'Africa/Dar_es_Salaam',
    is_active: true
  },
  {
    name: 'Platinum Uganda',
    code: 'PLATINUMUG',
    description: 'Platinum Uganda financial services subsidiary',
    aws_access_key_id: process.env.PLATINUM_UG_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.PLATINUM_UG_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PLATINUM_UG_S3_BUCKET,
    timezone: 'Africa/Kampala',
    is_active: true
  },
  {
    name: 'Premier Uganda',
    code: 'PREMIERUG',
    description: 'Premier Uganda financial services subsidiary',
    aws_access_key_id: process.env.PREMIER_UG_AWS_ACCESS_KEY_ID,
    aws_secret_access_key: process.env.PREMIER_UG_AWS_SECRET_ACCESS_KEY,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PREMIER_UG_S3_BUCKET,
    timezone: 'Africa/Kampala',
    is_active: true
  },
  {
    name: 'Premier South Africa',
    code: 'PREMIERZA',
    description: 'Premier South Africa financial services subsidiary',
    aws_access_key_id: process.env.PREMIER_ZA_AWS_ACCESS_KEY_ID || null,
    aws_secret_access_key: process.env.PREMIER_ZA_AWS_SECRET_ACCESS_KEY || null,
    aws_region: 'eu-central-1',
    s3_bucket_name: process.env.PREMIER_ZA_S3_BUCKET || 'premiersouthafricarecordings',
    timezone: 'Africa/Johannesburg',
    is_active: false
  }
];

// Seed subsidiaries
const seedSubsidiaries = async () => {
  try {
    console.log('Seeding subsidiaries...');
    const subsidiaries = getDefaultSubsidiaries();
    
    for (const subsidiaryData of subsidiaries) {
      const [subsidiary, created] = await Subsidiary.findOrCreate({
        where: { code: subsidiaryData.code },
        defaults: subsidiaryData
      });
      
      if (created) {
        console.log(`Created subsidiary: ${subsidiary.name} (${subsidiary.code})`);
      } else {
        console.log(`Subsidiary already exists: ${subsidiary.name} (${subsidiary.code})`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to seed subsidiaries:', error.message);
    return false;
  }
};

// Seed default admin users for each active subsidiary
const seedDefaultUsers = async () => {
  try {
    console.log('🌱 Seeding default admin users...');
    
    // Get active subsidiaries
    const subsidiaries = await Subsidiary.findAll({ where: { is_active: true } });
    const adminRole = await Role.findOne({ where: { code: 'ADMIN' } });
    
    if (!adminRole) {
      console.error('Admin role not found. Please seed roles first.');
      return false;
    }
    
    for (const subsidiary of subsidiaries) {
      const adminEmail = `admin@${subsidiary.code.toLowerCase()}.com`;
      const [user, created] = await User.findOrCreate({
        where: { email: adminEmail },
        defaults: {
          username: `admin_${subsidiary.code.toLowerCase()}`,
          email: adminEmail,
          password_hash: 'admin123', // Will be hashed by the model hook
          first_name: 'System',
          last_name: 'Administrator',
          subsidiary_id: subsidiary.id,
          is_active: true
        }
      });
      
      if (created) { 
        // Assign admin role
        await UserRole.findOrCreate({
          where: { user_id: user.id, role_id: adminRole.id },
          defaults: {
            user_id: user.id,
            role_id: adminRole.id,
            assigned_at: new Date(),
            is_active: true
          }
        });
        
        console.log(`Created admin user for ${subsidiary.name}: ${user.email}`);
      } else {
        console.log(`Admin user already exists for ${subsidiary.name}: ${user.email}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to seed default users:', error.message);
    return false;
  }
};

// Main seeder function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Seed roles first
    await Role.seedDefaultRoles();
    
    // Seed subsidiaries
    await seedSubsidiaries();
    
    // Seed default admin users
    await seedDefaultUsers();
    
    console.log('Database seeding completed successfully!');
    return true;
  } catch (error) {
    console.error('Database seeding failed:', error.message);
    return false;
  }
};

module.exports = {
  seedDatabase,
  seedSubsidiaries,
  seedDefaultUsers,
  getDefaultSubsidiaries
};
