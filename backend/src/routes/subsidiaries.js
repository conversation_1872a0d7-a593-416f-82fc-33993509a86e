const express = require('express');
const router = express.Router();
const { Subsidiary } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { AuditLog } = require('../models');

/**
 * @route GET /api/subsidiaries/active
 * @desc Get all active subsidiaries (public endpoint for login)
 * @access Public
 */
router.get('/active', async (req, res) => {
  try {
    const subsidiaries = await Subsidiary.findAll({
      where: { is_active: true },
      attributes: ['id', 'name', 'code', 'description'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: subsidiaries
    });
  } catch (error) {
    console.error('Failed to fetch active subsidiaries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subsidiaries'
    });
  }
});

/**
 * @route GET /api/subsidiaries
 * @desc Get all subsidiaries (admin only)
 * @access Private - Admin
 */
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const subsidiaries = await Subsidiary.findAll({
      order: [['name', 'ASC']]
    });

    // Log the action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'VIEW_SUBSIDIARIES',
      'subsidiary',
      null,
      { count: subsidiaries.length }
    );

    res.json({
      success: true,
      data: subsidiaries
    });
  } catch (error) {
    console.error('Failed to fetch subsidiaries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subsidiaries'
    });
  }
});

/**
 * @route GET /api/subsidiaries/:id
 * @desc Get subsidiary by ID
 * @access Private - Admin
 */
router.get('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const subsidiary = await Subsidiary.findByPk(id);
    
    if (!subsidiary) {
      return res.status(404).json({
        success: false,
        message: 'Subsidiary not found'
      });
    }

    // Log the action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'VIEW_SUBSIDIARY',
      'subsidiary',
      id,
      { subsidiaryName: subsidiary.name }
    );

    res.json({
      success: true,
      data: subsidiary
    });
  } catch (error) {
    console.error('Failed to fetch subsidiary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subsidiary'
    });
  }
});

/**
 * @route POST /api/subsidiaries
 * @desc Create new subsidiary (super admin only)
 * @access Private - Super Admin
 */
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      name,
      code,
      description,
      aws_access_key_id,
      aws_secret_access_key,
      aws_region,
      s3_bucket_name,
      timezone,
      settings
    } = req.body;

    // Check if code already exists
    const existingSubsidiary = await Subsidiary.findOne({ where: { code } });
    if (existingSubsidiary) {
      return res.status(400).json({
        success: false,
        message: 'Subsidiary code already exists'
      });
    }

    const subsidiary = await Subsidiary.create({
      name,
      code: code.toUpperCase(),
      description,
      aws_access_key_id,
      aws_secret_access_key,
      aws_region: aws_region || 'eu-central-1',
      s3_bucket_name,
      timezone: timezone || 'UTC',
      settings: settings || {},
      is_active: true
    });

    // Log the action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'CREATE_SUBSIDIARY',
      'subsidiary',
      subsidiary.id.toString(),
      { name, code }
    );

    res.status(201).json({
      success: true,
      message: 'Subsidiary created successfully',
      data: subsidiary
    });
  } catch (error) {
    console.error('Failed to create subsidiary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subsidiary'
    });
  }
});

/**
 * @route PUT /api/subsidiaries/:id
 * @desc Update subsidiary
 * @access Private - Admin
 */
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const subsidiary = await Subsidiary.findByPk(id);
    if (!subsidiary) {
      return res.status(404).json({
        success: false,
        message: 'Subsidiary not found'
      });
    }

    // If updating code, check for duplicates
    if (updateData.code && updateData.code !== subsidiary.code) {
      const existingSubsidiary = await Subsidiary.findOne({ 
        where: { code: updateData.code.toUpperCase() } 
      });
      if (existingSubsidiary) {
        return res.status(400).json({
          success: false,
          message: 'Subsidiary code already exists'
        });
      }
      updateData.code = updateData.code.toUpperCase();
    }

    await subsidiary.update(updateData);

    // Log the action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'UPDATE_SUBSIDIARY',
      'subsidiary',
      id,
      { changes: updateData }
    );

    res.json({
      success: true,
      message: 'Subsidiary updated successfully',
      data: subsidiary
    });
  } catch (error) {
    console.error('Failed to update subsidiary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subsidiary'
    });
  }
});

/**
 * @route DELETE /api/subsidiaries/:id
 * @desc Delete subsidiary (soft delete)
 * @access Private - Super Admin
 */
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const subsidiary = await Subsidiary.findByPk(id);
    if (!subsidiary) {
      return res.status(404).json({
        success: false,
        message: 'Subsidiary not found'
      });
    }

    // Soft delete by setting is_active to false
    await subsidiary.update({ is_active: false });

    // Log the action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'DELETE_SUBSIDIARY',
      'subsidiary',
      id,
      { subsidiaryName: subsidiary.name }
    );

    res.json({
      success: true,
      message: 'Subsidiary deactivated successfully'
    });
  } catch (error) {
    console.error('Failed to delete subsidiary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete subsidiary'
    });
  }
});

module.exports = router;
