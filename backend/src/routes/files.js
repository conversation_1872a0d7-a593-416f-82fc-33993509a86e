const express = require('express');
const { query, param, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { verifyS3Auth, validateFileUpload } = require('../middleware/s3Auth');
const { rateLimits, validateFileAccess } = require('../middleware/security');
const fileService = require('../services/fileService');
const analyticsService = require('../services/analyticsService');
const auditService = require('../services/auditService');
const { AuditLog } = require('../models');
const multer = require('multer');

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  }
});

const router = express.Router();

/**
 * GET /api/files/cache-status
 * Check if file cache is ready for the user's subsidiary
 */
router.get('/cache-status', authenticateToken, verifyS3Auth, async (req, res, next) => {
  try {
    const subsidiaryId = req.user?.subsidiaryId || req.user?.subsidiary_id;

    if (!subsidiaryId) {
      console.error('No subsidiary ID found in user object:', req.user);
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    const isReady = fileService.isCacheValid(subsidiaryId);

    // Get cached files regardless of validity to show actual count
    const cachedFiles = fileService.fileCache.get(subsidiaryId);

    console.log(`Cache status check for subsidiary ${subsidiaryId}: isReady=${isReady}, cachedFiles=${cachedFiles?.length || 0}`);

    res.json({
      success: true,
      data: {
        cacheReady: isReady,
        totalFiles: cachedFiles?.length || 0,
        subsidiaryId: subsidiaryId
      }
    });
  } catch (error) {
    console.error('Error checking cache status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check cache status',
      error: error.message
    });
  }
});

/**
 * POST /api/files/sync
 * Force refresh file cache from S3
 */
router.post('/sync', authenticateToken, verifyS3Auth, async (req, res, next) => {
  try {
    const subsidiaryId = req.user.subsidiary_id;
    const result = await fileService.syncFiles(subsidiaryId);

    // Log the sync action
    await AuditLog.create({
      user_id: req.user.id,
      subsidiary_id: subsidiaryId,
      action: 'SYNC_FILES',
      resource_type: 'files',
      details: { totalFiles: result.totalFiles },
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      status: result.success ? 'success' : 'failure',
      error_message: result.error || null
    });

    res.json({
      success: result.success,
      message: result.message,
      data: {
        totalFiles: result.totalFiles
      }
    });
  } catch (error) {
    console.error('Error syncing files:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync files',
      error: error.message
    });
  }
});

/**
 * GET /api/files/stats
 * Get dashboard statistics
 */
router.get('/stats', authenticateToken, verifyS3Auth, async (req, res, next) => {
  try {
    const stats = await fileService.getStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/recent
 * Get recently accessed files
 */
router.get('/recent', authenticateToken, verifyS3Auth, [
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { limit = 10 } = req.query;
    const recentFiles = await fileService.getRecentFiles(parseInt(limit));

    res.json({
      success: true,
      data: recentFiles
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files
 * List audio files from S3 bucket with advanced filtering
 */
router.get('/', authenticateToken, verifyS3Auth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('sortBy').optional().isIn(['name', 'size', 'lastModified']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc'),
  query('fileTypes').optional().isString().withMessage('File types must be a string'),
  query('minSize').optional().isInt({ min: 0 }).withMessage('Min size must be a positive integer'),
  query('maxSize').optional().isInt({ min: 0 }).withMessage('Max size must be a positive integer'),
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search = '',
      sortBy = 'name',
      sortOrder = 'asc',
      fileTypes = '',
      minSize,
      maxSize,
      startDate,
      endDate
    } = req.query;

    // Parse file types from comma-separated string
    const fileTypesArray = fileTypes ? fileTypes.split(',').map(type => type.trim().toLowerCase()) : [];

    // Parse size range
    const sizeRange = {
      min: minSize ? parseInt(minSize) : null,
      max: maxSize ? parseInt(maxSize) : null
    };

    // Parse date range
    const dateRange = {
      start: startDate || null,
      end: endDate || null
    };

    // Log search/filter query if search term or filters are applied
    const hasSearchOrFilters = search || fileTypes || minSize || maxSize || startDate || endDate;
    if (hasSearchOrFilters) {
      await auditService.logSearchEvent(
        search ? auditService.eventTypes.SEARCH.SEARCH_QUERY : auditService.eventTypes.SEARCH.FILTER_APPLY,
        req.user.id,
        req.user.subsidiary_id,
        {
          search_term: search,
          filters: {
            file_types: fileTypesArray,
            size_range: sizeRange,
            date_range: dateRange,
            sort_by: sortBy,
            sort_order: sortOrder
          },
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit)
          }
        },
        {}, // Results will be added after the query
        req
      );
    }

    // Use user's subsidiary ID for multi-tenant file access
    const result = await fileService.listFiles(req.user.subsidiary_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sortBy,
      sortOrder,
      fileTypes: fileTypesArray,
      sizeRange,
      dateRange
    });

    // Log S3 bucket access for file listing
    await auditService.logS3Access(
      auditService.eventTypes.S3_ACCESS.BUCKET_LIST,
      req.user.id,
      req.user.subsidiary_id,
      {
        query_parameters: {
          page: parseInt(page),
          limit: parseInt(limit),
          search,
          sortBy,
          sortOrder,
          fileTypes: fileTypesArray,
          sizeRange,
          dateRange
        },
        results: {
          total_files: result.totalFiles,
          returned_files: result.files.length,
          total_pages: result.totalPages,
          current_page: result.currentPage
        }
      },
      req
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/:key/download
 * Generate signed URL for file download with tracking
 */
router.get('/:key/download', rateLimits.downloads, authenticateToken, validateFileAccess, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { key } = req.params;
    const userId = req.user?.id;
    const subsidiaryId = req.user?.subsidiary_id;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    if (!subsidiaryId) {
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    // Use subsidiary-specific download URL generation
    const downloadResult = await fileService.generateSubsidiaryDownloadUrl(
      subsidiaryId,
      key,
      userId,
      'single',
      userAgent,
      ipAddress
    );

    // Track file download analytics
    const fileName = decodeURIComponent(key.split('/').pop());
    // Note: We don't have file size here, so we'll track it as 0 for now
    // In a real implementation, you'd fetch the file metadata first
    analyticsService.trackFileDownload(userId, key, fileName, 0);

    // Log single file download event
    await auditService.logDownloadEvent(
      auditService.eventTypes.DOWNLOAD.SINGLE_DOWNLOAD,
      userId,
      subsidiaryId,
      key,
      {
        file_name: fileName,
        file_key: key,
        download_url_generated: true,
        expires_at: downloadResult.expires,
        download_type: downloadResult.downloadType,
        user_agent: userAgent,
        ip_address: ipAddress
      },
      req,
      'success'
    );

    res.json({
      success: true,
      downloadUrl: downloadResult.url,
      expires: downloadResult.expires,
      downloadType: downloadResult.downloadType
    });
  } catch (error) {
    if (error.statusCode === 429) {
      return res.status(429).json({
        success: false,
        message: error.message,
        details: error.details
      });
    }
    next(error);
  }
});

/**
 * GET /api/files/:key/stream-url
 * Generate signed URL for file streaming (for audio player)
 */
router.get('/:key/stream-url', rateLimits.downloads, authenticateToken, validateFileAccess, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { key } = req.params;
    const userId = req.user?.id;
    const subsidiaryId = req.user?.subsidiary_id;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    if (!subsidiaryId) {
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    // Use subsidiary-specific stream URL generation (no attachment headers)
    const streamResult = await fileService.generateSubsidiaryDownloadUrl(
      subsidiaryId,
      key,
      userId,
      'stream', // This will prevent attachment headers from being added
      userAgent,
      ipAddress
    );

    // Track file play analytics
    const fileName = decodeURIComponent(key.split('/').pop());
    analyticsService.trackFilePlay(userId, key, fileName);

    // Log audio playback event
    await auditService.logPlaybackEvent(
      auditService.eventTypes.PLAYBACK.STREAM_START,
      userId,
      subsidiaryId,
      key,
      fileName,
      {
        stream_url_generated: true,
        expires_at: streamResult.expires,
        download_type: streamResult.downloadType,
        user_agent: userAgent,
        ip_address: ipAddress
      },
      req
    );

    res.json({
      success: true,
      streamUrl: streamResult.url,
      expires: streamResult.expires,
      downloadType: streamResult.downloadType
    });
  } catch (error) {
    if (error.statusCode === 429) {
      return res.status(429).json({
        success: false,
        message: error.message,
        details: error.details
      });
    }
    next(error);
  }
});

/**
 * GET /api/files/:key/stream
 * Stream audio file directly through backend (avoids CORS issues)
 */
router.get('/:key/stream', validateFileAccess, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    // Check for token in query parameter or header
    const token = req.query.token || req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication token required'
      });
    }

    // Verify the token manually and set user
    const jwt = require('jsonwebtoken');
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded; // Set user for analytics tracking
    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid authentication token'
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { key } = req.params;

    // Decode the URL-encoded file key
    const decodedKey = decodeURIComponent(key);

    // Track file play analytics
    const fileName = decodedKey.split('/').pop();
    analyticsService.trackFilePlay(req.user.id, decodedKey, fileName);

    // Log direct audio playback event
    await auditService.logPlaybackEvent(
      auditService.eventTypes.PLAYBACK.AUDIO_PLAY,
      req.user.id,
      req.user.subsidiary_id,
      decodedKey,
      fileName,
      {
        direct_stream: true,
        streaming_method: 'backend_proxy'
      },
      req
    );

    // Stream the file directly through the backend
    await fileService.streamFile(decodedKey, res, req.user.subsidiary_id);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/files/bulk-download
 * Generate signed URLs for multiple files with enhanced tracking
 */
router.post('/bulk-download', rateLimits.downloads, authenticateToken, async (req, res, next) => {
  try {
    const { fileKeys } = req.body;

    if (!Array.isArray(fileKeys) || fileKeys.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'File keys array is required'
      });
    }

    // Limit bulk download size
    const maxBulkFiles = 50;
    if (fileKeys.length > maxBulkFiles) {
      return res.status(400).json({
        success: false,
        message: `Bulk download limited to ${maxBulkFiles} files. Use ZIP download for larger batches.`
      });
    }

    const userId = req.user?.id;
    const subsidiaryId = req.user?.subsidiary_id;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    if (!subsidiaryId) {
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    const downloadUrls = await fileService.generateSubsidiaryBulkDownloadUrls(
      subsidiaryId,
      fileKeys,
      userId,
      userAgent,
      ipAddress
    );

    const successfulUrls = downloadUrls.filter(d => d.success).length;
    const failedUrls = downloadUrls.filter(d => !d.success).length;

    // Log bulk download event
    await auditService.logDownloadEvent(
      auditService.eventTypes.DOWNLOAD.BULK_DOWNLOAD,
      userId,
      subsidiaryId,
      `bulk_${fileKeys.length}_files`,
      {
        requested_files: fileKeys,
        total_files: fileKeys.length,
        successful_urls: successfulUrls,
        failed_urls: failedUrls,
        download_urls_generated: successfulUrls,
        user_agent: userAgent,
        ip_address: ipAddress
      },
      req,
      failedUrls > 0 ? 'warning' : 'success'
    );

    res.json({
      success: true,
      downloadUrls,
      totalFiles: fileKeys.length,
      successfulUrls,
      failedUrls
    });
  } catch (error) {
    if (error.statusCode === 429) {
      return res.status(429).json({
        success: false,
        message: error.message,
        details: error.details
      });
    }
    next(error);
  }
});

/**
 * POST /api/files/bulk-download-zip
 * Create and download a zip archive of multiple files
 */
router.post('/bulk-download-zip', rateLimits.downloads, authenticateToken, async (req, res, next) => {
  try {
    const { fileKeys, archiveName = 'audio-files.zip' } = req.body;

    if (!Array.isArray(fileKeys) || fileKeys.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'File keys array is required'
      });
    }

    // Limit the number of files in a single ZIP to prevent memory issues
    const maxZipFiles = 100;
    if (fileKeys.length > maxZipFiles) {
      return res.status(400).json({
        success: false,
        message: `ZIP download limited to ${maxZipFiles} files. Please select fewer files.`
      });
    }

    const userId = req.user?.id;
    const subsidiaryId = req.user?.subsidiary_id;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    if (!subsidiaryId) {
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    console.log(`Starting ZIP download for ${fileKeys.length} files by user ${userId} (subsidiary: ${subsidiaryId})`);

    // Track ZIP download
    if (userId) {
      const downloadTrackingService = require('../services/downloadTrackingService');
      await downloadTrackingService.trackDownload(
        userId,
        `zip:${fileKeys.length}_files`,
        'zip',
        userAgent,
        ipAddress
      );
    }

    // Log ZIP download event
    await auditService.logDownloadEvent(
      auditService.eventTypes.DOWNLOAD.ZIP_DOWNLOAD,
      userId,
      subsidiaryId,
      `zip_${archiveName}`,
      {
        archive_name: archiveName,
        requested_files: fileKeys,
        total_files: fileKeys.length,
        zip_creation_started: true,
        user_agent: userAgent,
        ip_address: ipAddress
      },
      req,
      'success'
    );

    // Set response headers for zip download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${archiveName}"`);
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // Set a longer timeout for ZIP creation
    req.setTimeout(300000); // 5 minutes
    res.setTimeout(300000); // 5 minutes

    // Add connection keep-alive headers
    res.setHeader('X-Accel-Buffering', 'no'); // Disable nginx buffering if present

    // Handle client disconnect
    req.on('close', () => {
      console.log(`Client disconnected during ZIP download for user ${userId}`);
    });

    // Stream the zip archive directly to the response
    await fileService.createSubsidiaryZipArchive(subsidiaryId, fileKeys, res);

    console.log(`ZIP download completed successfully for user ${userId}`);
  } catch (error) {
    console.error('Bulk zip download error:', error);

    // Only send error response if headers haven't been sent yet
    if (!res.headersSent) {
      // Determine error type and provide appropriate response
      let statusCode = 500;
      let message = 'Failed to create ZIP archive';

      if (error.code === 'ENOENT') {
        statusCode = 404;
        message = 'One or more files not found';
      } else if (error.code === 'ECONNRESET' || error.code === 'EPIPE') {
        statusCode = 499; // Client closed connection
        message = 'Client disconnected during download';
      } else if (error.message?.includes('timeout')) {
        statusCode = 408;
        message = 'ZIP creation timed out';
      }

      res.status(statusCode).json({
        success: false,
        message,
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    } else {
      // Headers already sent, just log the error
      console.error(`ZIP download failed after headers sent for user ${userId}:`, error.message);
    }
  }
});

/**
 * GET /api/files/download-stats
 * Get download statistics for the current user
 */
router.get('/download-stats', authenticateToken, [
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('Days must be between 1 and 365')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user?.id;
    const days = parseInt(req.query.days) || 30;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const stats = await fileService.getDownloadStats(userId, days);

    res.json({
      success: true,
      stats,
      period: `${days} days`
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/search-suggestions
 * Get search suggestions based on query
 */
router.get('/search-suggestions', authenticateToken, verifyS3Auth, [
  query('q').optional().isString().withMessage('Query must be a string'),
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { q = '', limit = 10 } = req.query;
    const suggestions = await fileService.getSearchSuggestions(q, parseInt(limit));

    // Log search suggestions request
    if (q) { // Only log if there's an actual query
      await auditService.logSearchEvent(
        auditService.eventTypes.SEARCH.SEARCH_SUGGESTIONS,
        req.user.id,
        req.user.subsidiary_id,
        {
          search_query: q,
          limit: parseInt(limit)
        },
        {
          suggestions_count: suggestions.length
        },
        req
      );
    }

    res.json({
      success: true,
      suggestions,
      query: q
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/file-statistics
 * Get comprehensive file statistics
 */
router.get('/file-statistics', authenticateToken, verifyS3Auth, async (req, res, next) => {
  try {
    const statistics = await fileService.getFileStatistics();

    res.json({
      success: true,
      statistics
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/:key/metadata
 * Get detailed metadata for a specific file
 */
router.get('/:key/metadata', authenticateToken, verifyS3Auth, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    console.log('Metadata request received for key:', req.params.key);

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const fileKey = decodeURIComponent(req.params.key);
    const subsidiaryId = req.user?.subsidiary_id;

    console.log('Decoded file key:', fileKey);
    console.log('Subsidiary ID:', subsidiaryId);

    if (!subsidiaryId) {
      return res.status(400).json({
        success: false,
        message: 'No subsidiary ID found for user'
      });
    }

    const metadata = await fileService.getFileMetadata(fileKey, subsidiaryId);
    console.log('Retrieved metadata:', metadata ? 'Success' : 'Not found');

    if (!metadata) {
      console.log('File not found:', fileKey);
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Log S3 metadata retrieval
    await auditService.logS3Access(
      auditService.eventTypes.S3_ACCESS.FILE_METADATA,
      req.user.id,
      req.user.subsidiary_id,
      {
        file_key: fileKey,
        file_name: metadata.name,
        file_size: metadata.size,
        file_type: metadata.type,
        last_modified: metadata.lastModified
      },
      req
    );

    console.log('Sending metadata response');
    res.json({
      success: true,
      metadata
    });
  } catch (error) {
    console.error('Error in metadata route:', error);
    next(error);
  }
});

/**
 * GET /api/files/debug
 * Debug endpoint to list all S3 objects (for development)
 */
router.get('/debug', authenticateToken, verifyS3Auth, async (req, res, next) => {
  try {
    const { s3, S3_CONFIG } = require('../config/aws');

    const params = {
      Bucket: S3_CONFIG.bucketName,
      MaxKeys: 100
    };

    const data = await s3.listObjectsV2(params).promise();

    const files = data.Contents?.map(file => ({
      key: file.Key,
      size: file.Size,
      lastModified: file.LastModified,
      etag: file.ETag
    })) || [];

    res.json({
      success: true,
      bucket: S3_CONFIG.bucketName,
      region: S3_CONFIG.region,
      totalFiles: files.length,
      files: files
    });
  } catch (error) {
    console.error('Debug error:', error);
    next(error);
  }
});

/**
 * POST /api/files/upload
 * Upload audio file to S3
 */
router.post('/upload', authenticateToken, verifyS3Auth, upload.single('audioFile'), validateFileUpload, async (req, res, next) => {
  try {
    const { file, user } = req;
    const metadata = {
      title: req.body.title || file.originalname,
      description: req.body.description || '',
      tags: req.body.tags || ''
    };

    const uploadResult = await fileService.uploadFile(file, user.id, metadata);

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      file: uploadResult
    });
  } catch (error) {
    console.error('File upload error:', error);
    next(error);
  }
});

/**
 * GET /api/files/s3-status
 * Check S3 connection status
 */
router.get('/s3-status', authenticateToken, async (req, res, next) => {
  try {
    const { testS3Connection } = require('../config/aws');
    const status = await testS3Connection();

    res.json({
      success: true,
      s3Status: status
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/files/sync
 * Force refresh/sync files from S3 (recursively scan all folders)
 */
router.post('/sync', authenticateToken, async (req, res, next) => {
  try {
    const subsidiaryId = req.user.subsidiary_id;

    console.log(`Starting file sync for subsidiary ${subsidiaryId}...`);

    // Force a fresh file listing with recursive folder scanning
    const result = await fileService.listFiles(subsidiaryId, {
      page: 1,
      limit: 1000, // Get more files during sync
      search: '',
      sortBy: 'name',
      sortOrder: 'asc'
    });

    console.log(`File sync completed for subsidiary ${subsidiaryId}. Found ${result.totalFiles} files.`);

    res.json({
      success: true,
      message: 'File sync completed successfully',
      data: {
        totalFiles: result.totalFiles,
        totalPages: result.totalPages,
        filesFound: result.files.length,
        subsidiaryId: subsidiaryId
      }
    });
  } catch (error) {
    console.error('File sync error:', error);
    next(error);
  }
});

module.exports = router;
