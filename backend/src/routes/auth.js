const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { User, Role, Subsidiary, AuditLog } = require('../models');
const analyticsService = require('../services/analyticsService');
const auditService = require('../services/auditService');

const router = express.Router();

// Helper function to get user with roles and subsidiary
const getUserWithDetails = async (email) => {
  return await User.findOne({
    where: { email },
    include: [
      {
        model: Subsidiary,
        as: 'subsidiary',
        attributes: ['id', 'name', 'code', 'timezone', 'is_active']
      },
      {
        model: Role,
        as: 'roles',
        attributes: ['id', 'name', 'code', 'permissions', 'hierarchy_level'],
        through: {
          attributes: ['assigned_at'],
          where: { is_active: true }
        }
      }
    ]
  });
};



/**
 * POST /api/auth/login
 * User login with multi-tenant support
 */
router.post('/login', [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
  body('subsidiaryId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid subsidiary ID is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password, subsidiaryId } = req.body;

    // Security logging
    const loginAttempt = {
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };

    // Find user with subsidiary and roles
    const user = await getUserWithDetails(email);
    if (!user) {
      console.warn('Failed login attempt - user not found:', loginAttempt);

      // Enhanced audit logging for failed login - user not found
      await auditService.logAuthEvent(
        auditService.eventTypes.AUTH.LOGIN_FAILURE,
        null,
        null,
        {
          email,
          reason: 'User not found',
          attempt_details: loginAttempt
        },
        req,
        'failure',
        'User not found'
      );

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if subsidiary is active
    if (!user.subsidiary.is_active) {
      console.warn('Failed login attempt - inactive subsidiary:', { ...loginAttempt, userId: user.id });

      await auditService.logAuthEvent(
        auditService.eventTypes.AUTH.LOGIN_FAILURE,
        user.id,
        user.subsidiary_id,
        {
          email,
          reason: 'Subsidiary inactive',
          subsidiary_code: user.subsidiary.code,
          attempt_details: loginAttempt
        },
        req,
        'failure',
        'Subsidiary inactive'
      );

      return res.status(403).json({
        success: false,
        message: 'Your organization account is currently inactive'
      });
    }

    // Validate subsidiary access if subsidiaryId is provided
    if (subsidiaryId && user.subsidiary_id !== parseInt(subsidiaryId)) {
      console.warn('Failed login attempt - subsidiary mismatch:', {
        ...loginAttempt,
        userId: user.id,
        userSubsidiaryId: user.subsidiary_id,
        requestedSubsidiaryId: subsidiaryId
      });

      await auditService.logAuthEvent(
        auditService.eventTypes.AUTH.LOGIN_FAILURE,
        user.id,
        user.subsidiary_id,
        {
          email,
          reason: 'Subsidiary access denied',
          user_subsidiary_id: user.subsidiary_id,
          requested_subsidiary_id: subsidiaryId,
          attempt_details: loginAttempt
        },
        req,
        'failure',
        'Subsidiary access denied'
      );

      return res.status(403).json({
        success: false,
        message: 'You do not have access to the selected subsidiary'
      });
    }

    // Check password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      console.warn('Failed login attempt - invalid password:', { ...loginAttempt, userId: user.id });

      await auditService.logAuthEvent(
        auditService.eventTypes.AUTH.LOGIN_FAILURE,
        user.id,
        user.subsidiary_id,
        {
          email,
          reason: 'Invalid password',
          subsidiary_code: user.subsidiary.code,
          attempt_details: loginAttempt
        },
        req,
        'failure',
        'Invalid password'
      );

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login time on successful login
    await user.updateLastLogin();

    // Successful login logging
    console.info('Successful login:', {
      ...loginAttempt,
      userId: user.id,
      subsidiary: user.subsidiary.code,
      roles: user.roles.map(r => r.code)
    });

    // Collect user permissions from all roles
    const permissions = new Set();
    user.roles.forEach(role => {
      role.permissions.forEach(permission => {
        permissions.add(permission);
      });
    });

    // Generate JWT token with comprehensive user data
    const tokenPayload = {
      id: user.id,
      email: user.email,
      username: user.username,
      subsidiary_id: user.subsidiary_id,
      subsidiary_code: user.subsidiary.code,
      roles: user.roles.map(role => ({
        id: role.id,
        code: role.code,
        name: role.name,
        hierarchy_level: role.hierarchy_level
      })),
      permissions: Array.from(permissions)
    };

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Track login analytics
    analyticsService.trackLogin(
      user.id,
      req.get('User-Agent') || '',
      req.ip || req.connection.remoteAddress
    );

    // Enhanced audit logging for successful login
    await auditService.logAuthEvent(
      auditService.eventTypes.AUTH.LOGIN_SUCCESS,
      user.id,
      user.subsidiary_id,
      {
        email: user.email,
        subsidiary_code: user.subsidiary.code,
        subsidiary_name: user.subsidiary.name,
        roles: user.roles.map(r => ({ code: r.code, name: r.name })),
        permissions: Array.from(permissions),
        login_details: loginAttempt,
        requires_password_reset: !user.password_changed_at
      },
      req,
      'success'
    );

    // Check if password reset is required
    const requiresPasswordReset = !user.password_changed_at;

    res.json({
      success: true,
      message: 'Login successful',
      token,
      requiresPasswordReset,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: user.getFullName(),
        subsidiary: {
          id: user.subsidiary.id,
          name: user.subsidiary.name,
          code: user.subsidiary.code,
          timezone: user.subsidiary.timezone
        },
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          code: role.code,
          hierarchyLevel: role.hierarchy_level
        })),
        permissions: Array.from(permissions),
        lastLoginAt: user.last_login_at,
        requiresPasswordReset
      }
    });
  } catch (error) {
    console.error('Login error:', error.message);
    next(error);
  }
});

/**
 * POST /api/auth/logout
 * User logout (client-side token removal)
 */
router.post('/logout', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

/**
 * POST /api/auth/reset-password
 * Reset password for first-time login
 */
router.post('/reset-password', [
  authenticateToken,
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must be at least 8 characters with uppercase, lowercase, and number')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Get user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify current password
    const isValidPassword = await user.validatePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    await user.update({
      password_hash: newPassword, // Will be hashed by model hook
      password_changed_at: new Date()
    });

    // Log the action
    await AuditLog.logUserAction(
      userId,
      user.subsidiary_id,
      'PASSWORD_RESET',
      'user',
      userId.toString(),
      { forced: !user.password_changed_at },
      req
    );

    res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Password reset error:', error.message);
    next(error);
  }
});

/**
 * GET /api/auth/me
 * Get current user information
 */
router.get('/me', authenticateToken, async (req, res, next) => {
  try {
    const user = await getUserWithDetails(req.user.email);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Collect permissions
    const permissions = new Set();
    user.roles.forEach(role => {
      role.permissions.forEach(permission => {
        permissions.add(permission);
      });
    });

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: user.getFullName(),
        phone: user.phone,
        subsidiary: {
          id: user.subsidiary.id,
          name: user.subsidiary.name,
          code: user.subsidiary.code,
          timezone: user.subsidiary.timezone
        },
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          code: role.code,
          hierarchyLevel: role.hierarchy_level,
          permissions: role.permissions
        })),
        permissions: Array.from(permissions),
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/auth/logout
 * User logout (mainly for logging purposes)
 */
router.post('/logout', authenticateToken, async (req, res, next) => {
  try {
    // Log logout action
    await AuditLog.logUserAction(
      req.user.id,
      req.user.subsidiary_id,
      'LOGOUT',
      'auth',
      req.user.id.toString(),
      {},
      req
    );

    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/auth/change-password
 * Change user password
 */
router.post('/change-password', [
  authenticateToken,
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Get user from database
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify current password
    const isValidPassword = await user.validatePassword(currentPassword);
    if (!isValidPassword) {
      await AuditLog.logFailure(
        user.id,
        user.subsidiary_id,
        'CHANGE_PASSWORD',
        'auth',
        user.id.toString(),
        'Invalid current password',
        req
      );

      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    await user.update({ password_hash: newPassword });

    // Log successful password change
    await AuditLog.logUserAction(
      user.id,
      user.subsidiary_id,
      'CHANGE_PASSWORD',
      'auth',
      user.id.toString(),
      {},
      req
    );

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
