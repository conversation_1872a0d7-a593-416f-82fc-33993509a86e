const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { requireRole } = require('../middleware/roleAuth');
const auditService = require('../services/auditService');
const { AuditLog, User, Subsidiary } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

/**
 * GET /api/audit/logs
 * Get audit logs with filtering and pagination
 * Requires admin or compliance officer role
 */
router.get('/logs', authenticateToken, requireRole(['ADMIN', 'COMPLIANCE']), [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('userId').optional().isInt().withMessage('User ID must be an integer'),
  query('subsidiaryId').optional().isInt().withMessage('Subsidiary ID must be an integer'),
  query('action').optional().isString().withMessage('Action must be a string'),
  query('resourceType').optional().isString().withMessage('Resource type must be a string'),
  query('status').optional().isIn(['success', 'failure', 'warning']).withMessage('Status must be success, failure, or warning'),
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date'),
  query('search').optional().isString().withMessage('Search must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 50,
      userId,
      subsidiaryId,
      action,
      resourceType,
      status,
      startDate,
      endDate,
      search
    } = req.query;

    // Build filter object
    const filters = {
      page: parseInt(page),
      limit: parseInt(limit)
    };

    if (userId) filters.userId = parseInt(userId);
    if (subsidiaryId) filters.subsidiaryId = parseInt(subsidiaryId);
    if (action) filters.action = action;
    if (resourceType) filters.resourceType = resourceType;
    if (status) filters.status = status;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    // For non-admin users, restrict to their subsidiary
    if (req.user.roles.some(role => role.code === 'ADMIN')) {
      // Admin can see all logs
    } else {
      // Compliance officers can only see their subsidiary's logs
      filters.subsidiaryId = req.user.subsidiary_id;
    }

    const result = await auditService.getAuditLogs(filters);

    // Log the audit log access
    await auditService.logSystemEvent(
      'AUDIT_LOG_ACCESS',
      {
        accessed_by: req.user.id,
        filters_applied: filters,
        results_returned: result.logs.length
      },
      req.user.id,
      req.user.subsidiary_id
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/audit/summary
 * Get audit summary statistics
 */
router.get('/summary', authenticateToken, requireRole(['ADMIN', 'COMPLIANCE']), [
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date'),
  query('subsidiaryId').optional().isInt().withMessage('Subsidiary ID must be an integer')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, subsidiaryId } = req.query;

    // Build where clause
    const whereClause = {};
    
    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at[Op.gte] = new Date(startDate);
      if (endDate) whereClause.created_at[Op.lte] = new Date(endDate);
    }

    // For non-admin users, restrict to their subsidiary
    if (req.user.roles.some(role => role.code === 'ADMIN')) {
      if (subsidiaryId) whereClause.subsidiary_id = parseInt(subsidiaryId);
    } else {
      whereClause.subsidiary_id = req.user.subsidiary_id;
    }

    // Get summary statistics
    const [
      totalLogs,
      authEvents,
      fileEvents,
      downloadEvents,
      searchEvents,
      failureEvents,
      recentActivity
    ] = await Promise.all([
      // Total logs count
      AuditLog.count({ where: whereClause }),
      
      // Authentication events
      AuditLog.count({
        where: {
          ...whereClause,
          resource_type: 'auth'
        }
      }),
      
      // File events
      AuditLog.count({
        where: {
          ...whereClause,
          resource_type: 'file'
        }
      }),
      
      // Download events
      AuditLog.count({
        where: {
          ...whereClause,
          resource_type: 'download'
        }
      }),
      
      // Search events
      AuditLog.count({
        where: {
          ...whereClause,
          resource_type: 'search'
        }
      }),
      
      // Failure events
      AuditLog.count({
        where: {
          ...whereClause,
          status: 'failure'
        }
      }),
      
      // Recent activity (last 24 hours)
      AuditLog.count({
        where: {
          ...whereClause,
          created_at: {
            [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      })
    ]);

    // Get top actions
    const topActions = await AuditLog.findAll({
      where: whereClause,
      attributes: [
        'action',
        [AuditLog.sequelize.fn('COUNT', AuditLog.sequelize.col('action')), 'count']
      ],
      group: ['action'],
      order: [[AuditLog.sequelize.literal('count'), 'DESC']],
      limit: 10,
      raw: true
    });

    // Get top users
    const topUsers = await AuditLog.findAll({
      where: {
        ...whereClause,
        user_id: { [Op.not]: null }
      },
      attributes: [
        'user_id',
        [AuditLog.sequelize.fn('COUNT', AuditLog.sequelize.col('user_id')), 'count']
      ],
      include: [{
        model: User,
        as: 'user',
        attributes: ['username', 'email', 'first_name', 'last_name']
      }],
      group: ['user_id', 'user.id'],
      order: [[AuditLog.sequelize.literal('count'), 'DESC']],
      limit: 10
    });

    const summary = {
      totalLogs,
      eventCounts: {
        authentication: authEvents,
        fileOperations: fileEvents,
        downloads: downloadEvents,
        searches: searchEvents,
        failures: failureEvents
      },
      recentActivity,
      topActions: topActions.map(item => ({
        action: item.action,
        count: parseInt(item.count)
      })),
      topUsers: topUsers.map(item => ({
        userId: item.user_id,
        username: item.user?.username,
        email: item.user?.email,
        fullName: `${item.user?.first_name || ''} ${item.user?.last_name || ''}`.trim(),
        count: parseInt(item.dataValues.count)
      }))
    };

    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/audit/export
 * Export audit logs to CSV
 */
router.get('/export', authenticateToken, requireRole(['ADMIN', 'COMPLIANCE']), [
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date'),
  query('format').optional().isIn(['csv', 'json']).withMessage('Format must be csv or json')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, format = 'csv' } = req.query;

    // Build where clause
    const whereClause = {};
    
    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at[Op.gte] = new Date(startDate);
      if (endDate) whereClause.created_at[Op.lte] = new Date(endDate);
    }

    // For non-admin users, restrict to their subsidiary
    if (!req.user.roles.some(role => role.code === 'ADMIN')) {
      whereClause.subsidiary_id = req.user.subsidiary_id;
    }

    // Get audit logs for export
    const logs = await AuditLog.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'email', 'first_name', 'last_name']
        },
        {
          model: Subsidiary,
          as: 'subsidiary',
          attributes: ['name', 'code']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 10000 // Limit export to prevent memory issues
    });

    // Log the export action
    await auditService.logSystemEvent(
      'AUDIT_LOG_EXPORT',
      {
        exported_by: req.user.id,
        export_format: format,
        records_exported: logs.length,
        date_range: { startDate, endDate }
      },
      req.user.id,
      req.user.subsidiary_id
    );

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.json"`);
      res.json(logs);
    } else {
      // CSV format
      const csv = require('csv-stringify');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`);

      const csvData = logs.map(log => ({
        id: log.id,
        timestamp: log.created_at,
        user: log.user ? `${log.user.first_name} ${log.user.last_name} (${log.user.email})` : 'System',
        subsidiary: log.subsidiary ? `${log.subsidiary.name} (${log.subsidiary.code})` : 'N/A',
        action: log.action,
        resource_type: log.resource_type,
        resource_id: log.resource_id,
        status: log.status,
        ip_address: log.ip_address,
        user_agent: log.user_agent,
        details: JSON.stringify(log.details),
        error_message: log.error_message
      }));

      csv.stringify(csvData, { header: true }, (err, output) => {
        if (err) {
          return next(err);
        }
        res.send(output);
      });
    }
  } catch (error) {
    next(error);
  }
});

module.exports = router;
