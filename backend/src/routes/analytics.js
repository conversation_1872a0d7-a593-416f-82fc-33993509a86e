/**
 * Analytics API Routes
 * Provides endpoints for real-time analytics and reporting
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { rateLimits } = require('../middleware/security');
const analyticsService = require('../services/analyticsService');
const websocketService = require('../services/websocketService');

const router = express.Router();

/**
 * GET /api/analytics/dashboard
 * Get real-time dashboard data
 */
router.get('/dashboard', authenticateToken, async (req, res, next) => {
  try {
    const dashboardData = analyticsService.getDashboardData();
    
    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/historical
 * Get historical analytics data for charts
 */
router.get('/historical', authenticateToken, [
  query('timeRange').optional().isIn(['24h', '7d', '30d']).withMessage('Invalid time range')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.array()
      });
    }
    
    const { timeRange = '24h' } = req.query;
    const historicalData = analyticsService.getHistoricalData(timeRange);
    
    res.json({
      success: true,
      data: historicalData,
      timeRange
    });
  } catch (error) {
    console.error('Error fetching historical data:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/files
 * Get file-specific analytics
 */
router.get('/files', authenticateToken, async (req, res, next) => {
  try {
    const dashboardData = analyticsService.getDashboardData();
    
    res.json({
      success: true,
      data: {
        totalFiles: dashboardData.files.total,
        totalSize: dashboardData.files.totalSize,
        fileTypes: dashboardData.files.fileTypes,
        mostPlayed: dashboardData.files.mostPlayed,
        mostDownloaded: dashboardData.files.mostDownloaded,
        sizeDistribution: calculateSizeDistribution(dashboardData.files.mostDownloaded),
        formatDistribution: dashboardData.files.fileTypes
      }
    });
  } catch (error) {
    console.error('Error fetching file analytics:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/users
 * Get user activity analytics
 */
router.get('/users', authenticateToken, async (req, res, next) => {
  try {
    const dashboardData = analyticsService.getDashboardData();
    
    res.json({
      success: true,
      data: {
        totalUsers: dashboardData.users.total,
        activeUsers: dashboardData.realTime.activeUsers,
        activeToday: dashboardData.users.activeToday,
        recentLogins: dashboardData.users.recentLogins,
        avgSessionDuration: dashboardData.users.avgSessionDuration,
        userGrowth: calculateUserGrowth(), // Mock data
        activityHeatmap: generateActivityHeatmap() // Mock data
      }
    });
  } catch (error) {
    console.error('Error fetching user analytics:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/system
 * Get system performance analytics
 */
router.get('/system', authenticateToken, async (req, res, next) => {
  try {
    const dashboardData = analyticsService.getDashboardData();
    
    res.json({
      success: true,
      data: {
        uptime: dashboardData.system.uptime,
        apiCalls: dashboardData.system.apiCalls,
        errors: dashboardData.system.errors,
        errorRate: dashboardData.system.errorRate,
        avgResponseTime: dashboardData.system.avgResponseTime,
        s3Operations: dashboardData.system.s3Operations,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        websocketConnections: websocketService.getConnectionStats()
      }
    });
  } catch (error) {
    console.error('Error fetching system analytics:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/live-events
 * Get recent live events
 */
router.get('/live-events', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.array()
      });
    }
    
    const { limit = 20 } = req.query;
    const dashboardData = analyticsService.getDashboardData();
    
    res.json({
      success: true,
      data: dashboardData.realTime.liveEvents.slice(0, parseInt(limit))
    });
  } catch (error) {
    console.error('Error fetching live events:', error);
    next(error);
  }
});

/**
 * POST /api/analytics/track
 * Manual event tracking endpoint
 */
router.post('/track', rateLimits.api, authenticateToken, async (req, res, next) => {
  try {
    const { eventType, data } = req.body;
    const userId = req.user.id;
    
    if (!eventType) {
      return res.status(400).json({
        success: false,
        message: 'Event type is required'
      });
    }
    
    // Track custom events
    switch (eventType) {
      case 'page_view':
        analyticsService.addLiveEvent({
          type: 'page_view',
          userId,
          page: data.page,
          timestamp: new Date()
        });
        break;
        
      case 'feature_usage':
        analyticsService.addLiveEvent({
          type: 'feature_usage',
          userId,
          feature: data.feature,
          timestamp: new Date()
        });
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: 'Unknown event type'
        });
    }
    
    res.json({
      success: true,
      message: 'Event tracked successfully'
    });
  } catch (error) {
    console.error('Error tracking event:', error);
    next(error);
  }
});

/**
 * GET /api/analytics/export
 * Export analytics data
 */
router.get('/export', authenticateToken, [
  query('format').optional().isIn(['json', 'csv']).withMessage('Format must be json or csv'),
  query('timeRange').optional().isIn(['24h', '7d', '30d']).withMessage('Invalid time range')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.array()
      });
    }
    
    const { format = 'json', timeRange = '24h' } = req.query;
    const dashboardData = analyticsService.getDashboardData();
    const historicalData = analyticsService.getHistoricalData(timeRange);
    
    const exportData = {
      dashboard: dashboardData,
      historical: historicalData,
      exportedAt: new Date(),
      timeRange
    };
    
    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(exportData);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="analytics-${timeRange}-${Date.now()}.csv"`);
      res.send(csv);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="analytics-${timeRange}-${Date.now()}.json"`);
      res.json(exportData);
    }
  } catch (error) {
    console.error('Error exporting analytics:', error);
    next(error);
  }
});

/**
 * Helper function to calculate size distribution
 */
function calculateSizeDistribution(files) {
  const distribution = {
    tiny: 0,    // < 1MB
    small: 0,   // 1-10MB
    medium: 0,  // 10-50MB
    large: 0,   // 50-100MB
    huge: 0     // > 100MB
  };
  
  files.forEach(file => {
    const sizeMB = file.totalSize / (1024 * 1024);
    if (sizeMB < 1) distribution.tiny++;
    else if (sizeMB < 10) distribution.small++;
    else if (sizeMB < 50) distribution.medium++;
    else if (sizeMB < 100) distribution.large++;
    else distribution.huge++;
  });
  
  return distribution;
}

/**
 * Helper function to calculate user growth (mock data)
 */
function calculateUserGrowth() {
  const growth = [];
  const now = new Date();
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
    growth.push({
      date: date.toISOString().split('T')[0],
      newUsers: Math.floor(Math.random() * 10),
      totalUsers: 100 + (30 - i) * 3
    });
  }
  
  return growth;
}

/**
 * Helper function to generate activity heatmap (mock data)
 */
function generateActivityHeatmap() {
  const heatmap = [];
  
  for (let hour = 0; hour < 24; hour++) {
    for (let day = 0; day < 7; day++) {
      heatmap.push({
        hour,
        day,
        activity: Math.floor(Math.random() * 100)
      });
    }
  }
  
  return heatmap;
}

/**
 * Helper function to convert data to CSV
 */
function convertToCSV(data) {
  // Simple CSV conversion for historical data
  const headers = ['timestamp', 'activeUsers', 'apiCalls', 'uploads', 'downloads', 'plays'];
  const rows = data.historical.map(row => [
    row.timestamp,
    row.activeUsers,
    row.apiCalls,
    row.uploads,
    row.downloads,
    row.plays
  ]);
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
}

module.exports = router;
