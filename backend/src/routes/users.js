const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission, requireOwnershipOrAdmin } = require('../middleware/rbac');
const userService = require('../services/userService');
const { Role, Subsidiary } = require('../models');

const router = express.Router();

/**
 * GET /api/users
 * Get all users with pagination and filtering
 */
router.get('/', [
  authenticateToken,
  requirePermission('users:read'),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('search').optional().trim().escape(),
  query('subsidiaryId').optional({ values: 'falsy' }).isInt().toInt(),
  query('roleId').optional({ values: 'falsy' }).isInt().toInt(),

  query('sortBy').optional().isIn(['username', 'email', 'created_at', 'last_login_at']),
  query('sortOrder').optional().isIn(['ASC', 'DESC'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const options = {
      page: req.query.page || 1,
      limit: req.query.limit || 20,
      search: req.query.search || '',
      subsidiaryId: req.query.subsidiaryId || null,
      roleId: req.query.roleId || null,
      sortBy: req.query.sortBy || 'created_at',
      sortOrder: req.query.sortOrder || 'DESC'
    };

    // All users should be restricted to their own subsidiary
    // Admin users can only see users from their own subsidiary
    options.subsidiaryId = req.user.subsidiary_id;

    const result = await userService.getUsers(options);

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/roles
 * Get all available roles
 */
router.get('/roles', [
  authenticateToken,
  requirePermission('users:read')
], async (req, res, next) => {
  try {
    console.log('Roles endpoint called by user:', req.user?.email);
    console.log('User roles:', req.user?.roles?.map(r => r.code));
    console.log('User permissions:', req.user?.permissions);
    console.log('Required permission: users:read');

    const roles = await Role.findAll({
      where: { is_active: true },
      attributes: ['id', 'name', 'code', 'description', 'hierarchy_level', 'permissions'],
      order: [['hierarchy_level', 'DESC']]
    });

    res.json({
      success: true,
      roles
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/subsidiaries
 * Get all subsidiaries (for user creation)
 */
router.get('/subsidiaries', [
  authenticateToken,
  requirePermission('users:write')
], async (req, res, next) => {
  try {
    // Admin users should only see their own subsidiary
    const subsidiaries = await Subsidiary.findAll({
      where: {
        is_active: true,
        id: req.user.subsidiary_id
      },
      attributes: ['id', 'name', 'code', 'description'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      subsidiaries
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/:id
 * Get user by ID
 */
router.get('/:id', [
  authenticateToken,
  param('id').isInt().toInt(),
  requireOwnershipOrAdmin('id')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await userService.getUserById(req.params.id);

    res.json({
      success: true,
      user
    });
  } catch (error) {
    if (error.message === 'User not found') {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    next(error);
  }
});

/**
 * POST /api/users
 * Create a new user
 */
router.post('/', [
  authenticateToken,
  requirePermission('users:write'),
  body('username').notEmpty().trim().isLength({ min: 3, max: 50 }),
  body('email').isEmail().normalizeEmail(),
  body('password').custom((value, { req }) => {
    // If generateTempPassword is true, password is not required
    if (req.body.generateTempPassword) {
      return true;
    }
    // If generateTempPassword is false or not provided, password is required and must meet criteria
    if (!value) {
      throw new Error('Password is required when not generating temporary password');
    }
    if (value.length < 8) {
      throw new Error('Password must be at least 8 characters');
    }
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
      throw new Error('Password must contain uppercase, lowercase, and number');
    }
    return true;
  }),
  body('firstName').optional().trim().isLength({ max: 50 }),
  body('lastName').optional().trim().isLength({ max: 50 }),
  body('phone').optional().trim().isLength({ max: 20 }),
  body('subsidiaryId').optional().isInt(),
  body('roleIds').optional().isArray(),
  body('generateTempPassword').optional().isBoolean(),
  body('forcePasswordReset').optional().isBoolean()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.error('User creation validation failed:', {
        body: req.body,
        errors: errors.array()
      });
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Force the subsidiary to be the current user's subsidiary for security
    const userData = {
      ...req.body,
      subsidiaryId: req.user.subsidiary_id
    };

    const user = await userService.createUser(userData, req.user.id);

    // Convert Sequelize model to plain object to avoid circular references
    const userResponse = user.toJSON ? user.toJSON() : user;

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    if (error.message === 'Email already exists' ||
        error.message === 'Invalid or inactive subsidiary') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * PUT /api/users/:id
 * Update user
 */
router.put('/:id', [
  authenticateToken,
  param('id').isInt().toInt(),
  requireOwnershipOrAdmin('id'),
  body('username').optional().trim().isLength({ min: 3, max: 50 }),
  body('email').optional().isEmail().normalizeEmail(),
  body('password').optional().isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body('firstName').optional().trim().isLength({ max: 50 }),
  body('lastName').optional().trim().isLength({ max: 50 }),
  body('phone').optional().trim().isLength({ max: 20 }),
  body('isActive').optional().isBoolean()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await userService.updateUser(req.params.id, req.body, req.user.id);

    res.json({
      success: true,
      message: 'User updated successfully',
      user
    });
  } catch (error) {
    if (error.message === 'User not found') {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    if (error.message === 'Email already exists') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * DELETE /api/users/:id
 * Delete user (hard delete - permanently removes from database)
 */
router.delete('/:id', [
  authenticateToken,
  requirePermission('users:delete'),
  param('id').isInt().toInt()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Prevent self-deletion
    if (req.user.id === req.params.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    await userService.deleteUser(req.params.id, req.user.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    if (error.message === 'User not found') {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    next(error);
  }
});

/**
 * POST /api/users/:id/roles
 * Assign roles to user
 */
router.post('/:id/roles', [
  authenticateToken,
  requirePermission('users:assign_roles'),
  param('id').isInt().toInt(),
  body('roleIds').isArray().notEmpty()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    await userService.assignRoles(req.params.id, req.body.roleIds, req.user.id);

    res.json({
      success: true,
      message: 'Roles assigned successfully'
    });
  } catch (error) {
    if (error.message === 'User not found' ||
        error.message === 'One or more roles not found or inactive') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * GET /api/users/:id/permissions
 * Get user permissions
 */
router.get('/:id/permissions', [
  authenticateToken,
  param('id').isInt().toInt(),
  requireOwnershipOrAdmin('id')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const permissions = await userService.getUserPermissions(req.params.id);

    res.json({
      success: true,
      permissions
    });
  } catch (error) {
    if (error.message === 'User not found') {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    next(error);
  }
});

module.exports = router;
