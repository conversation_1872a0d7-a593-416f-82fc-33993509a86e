const { sequelize } = require('../config/database');
const { DataTypes } = require('sequelize');

// Import all models
const Subsidiary = require('./Subsidiary')(sequelize, DataTypes);
const User = require('./User')(sequelize, DataTypes);
const Role = require('./Role')(sequelize, DataTypes);
const UserRole = require('./UserRole')(sequelize, DataTypes);
const AuditLog = require('./AuditLog')(sequelize, DataTypes);

// Define associations
const defineAssociations = () => {
  // Subsidiary associations
  Subsidiary.hasMany(User, {
    foreignKey: 'subsidiary_id',
    as: 'users'
  });

  // User associations
  User.belongsTo(Subsidiary, {
    foreignKey: 'subsidiary_id',
    as: 'subsidiary'
  });

  User.belongsToMany(Role, {
    through: UserRole,
    foreignKey: 'user_id',
    otherKey: 'role_id',
    as: 'roles'
  });

  User.hasMany(AuditLog, {
    foreignKey: 'user_id',
    as: 'auditLogs'
  });

  // Role associations
  Role.belongsToMany(User, {
    through: UserRole,
    foreignKey: 'role_id',
    otherKey: 'user_id',
    as: 'users'
  });

  // UserRole associations
  UserRole.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  });

  UserRole.belongsTo(Role, {
    foreignKey: 'role_id',
    as: 'role'
  });

  // AuditLog associations
  AuditLog.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  });

  AuditLog.belongsTo(Subsidiary, {
    foreignKey: 'subsidiary_id',
    as: 'subsidiary'
  });
};

// Initialize associations
defineAssociations();

// Export models
module.exports = {
  sequelize,
  Subsidiary,
  User,
  Role,
  UserRole,
  AuditLog,
  
  // Helper function to sync all models
  syncModels: async (options = {}) => {
    try {
      await sequelize.sync(options);
      console.log('All models synchronized successfully');
      return true;
    } catch (error) {
      console.error('Model synchronization failed:', error.message);
      return false;
    }
  }
};
