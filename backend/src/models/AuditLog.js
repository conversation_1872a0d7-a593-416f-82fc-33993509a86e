module.exports = (sequelize, DataTypes) => {
  const AuditLog = sequelize.define('AuditLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    subsidiary_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'subsidiaries',
        key: 'id'
      }
    },
    action: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    resource_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    resource_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    details: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    session_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('success', 'failure', 'warning'),
      allowNull: false,
      defaultValue: 'success'
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'audit_logs',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['subsidiary_id']
      },
      {
        fields: ['action']
      },
      {
        fields: ['resource_type']
      },
      {
        fields: ['resource_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['user_id', 'created_at']
      }
    ]
  });

  // Class methods for logging different types of actions
  AuditLog.logUserAction = async function(userId, subsidiaryId, action, resourceType, resourceId, details = {}, req = null) {
    try {
      const logData = {
        user_id: userId,
        subsidiary_id: subsidiaryId,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        details,
        status: 'success'
      };

      if (req) {
        logData.ip_address = req.ip || req.connection?.remoteAddress;
        logData.user_agent = req.get('User-Agent');
        logData.session_id = req.sessionID;
      }

      await this.create(logData);
    } catch (error) {
      console.error('Failed to create audit log:', error.message);
    }
  };

  AuditLog.logFailure = async function(userId, subsidiaryId, action, resourceType, resourceId, errorMessage, req = null) {
    try {
      const logData = {
        user_id: userId,
        subsidiary_id: subsidiaryId,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        status: 'failure',
        error_message: errorMessage
      };

      if (req) {
        logData.ip_address = req.ip || req.connection?.remoteAddress;
        logData.user_agent = req.get('User-Agent');
        logData.session_id = req.sessionID;
      }

      await this.create(logData);
    } catch (error) {
      console.error('Failed to create failure audit log:', error.message);
    }
  };

  return AuditLog;
};
