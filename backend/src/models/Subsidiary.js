module.exports = (sequelize, DataTypes) => {
  const Subsidiary = sequelize.define('Subsidiary', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 20],
        is: /^[A-Z0-9_]+$/i // Allow alphanumeric and underscores
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    // AWS Configuration
    aws_access_key_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        notEmptyIfActive: function(value) {
          if (this.is_active && (!value || value.trim() === '')) {
            throw new Error('AWS Access Key ID is required for active subsidiaries');
          }
        }
      }
    },
    aws_secret_access_key: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        notEmptyIfActive: function(value) {
          if (this.is_active && (!value || value.trim() === '')) {
            throw new Error('AWS Secret Access Key is required for active subsidiaries');
          }
        }
      }
    },
    aws_region: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'us-east-1',
      validate: {
        notEmpty: true
      }
    },
    s3_bucket_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        notEmptyIfActive: function(value) {
          if (this.is_active && (!value || value.trim() === '')) {
            throw new Error('S3 Bucket Name is required for active subsidiaries');
          }
        }
      }
    },
    // Additional settings
    timezone: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: 'UTC'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    settings: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'subsidiaries',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['is_active']
      }
    ],
    hooks: {
      beforeCreate: (subsidiary) => {
        subsidiary.code = subsidiary.code.toUpperCase();
      },
      beforeUpdate: (subsidiary) => {
        if (subsidiary.code) {
          subsidiary.code = subsidiary.code.toUpperCase();
        }
      }
    }
  });

  // Instance methods
  Subsidiary.prototype.getAwsConfig = function() {
    if (!this.aws_access_key_id || !this.aws_secret_access_key || !this.s3_bucket_name) {
      throw new Error(`Incomplete AWS configuration for subsidiary ${this.name}`);
    }

    return {
      accessKeyId: this.aws_access_key_id,
      secretAccessKey: this.aws_secret_access_key,
      region: this.aws_region,
      bucketName: this.s3_bucket_name
    };
  };

  Subsidiary.prototype.toJSON = function() {
    const values = { ...this.get() };
    // Don't expose AWS credentials in JSON responses
    delete values.aws_access_key_id;
    delete values.aws_secret_access_key;
    return values;
  };

  return Subsidiary;
};
