module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 50]
      }
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 20],
        isUppercase: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    hierarchy_level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1,
        max: 10
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'roles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['hierarchy_level']
      }
    ],
    hooks: {
      beforeCreate: (role) => {
        role.code = role.code.toUpperCase();
      },
      beforeUpdate: (role) => {
        if (role.code) {
          role.code = role.code.toUpperCase();
        }
      }
    }
  });

  // Instance methods
  Role.prototype.hasPermission = function(permission) {
    return this.permissions.includes(permission);
  };

  Role.prototype.addPermission = function(permission) {
    if (!this.hasPermission(permission)) {
      this.permissions.push(permission);
    }
  };

  Role.prototype.removePermission = function(permission) {
    this.permissions = this.permissions.filter(p => p !== permission);
  };

  // Class methods
  Role.getDefaultRoles = function() {
    return [
      {
        name: 'Admin',
        code: 'ADMIN',
        description: 'Full access to subsidiary features. Can create/edit/delete users, assign roles, manage files, and access system health dashboards within their subsidiary.',
        hierarchy_level: 5,
        permissions: [
          'files:read', 'files:write', 'files:delete', 'files:download', 'files:upload',
          'users:read', 'users:write', 'users:delete', 'users:assign_roles',
          'system:admin', 'system:health', 'analytics:read', 'audit:read', 'settings:manage'
        ]
      },
      {
        name: 'QA (Quality Assurance)',
        code: 'QA',
        description: 'Can view, play, and download recordings. Can flag, tag, and rate calls for quality checks.',
        hierarchy_level: 3,
        permissions: [
          'files:read', 'files:download', 'files:flag', 'files:tag', 'files:rate',
          'files:comment', 'reports:qa', 'analytics:read'
        ]
      },
      {
        name: 'Compliance Officer',
        code: 'COMPLIANCE',
        description: 'Has access to recordings for audit and compliance checks. Cannot modify recordings.',
        hierarchy_level: 3,
        permissions: [
          'files:read', 'files:download', 'audit:read', 'compliance:review',
          'retention:view', 'access_logs:view'
        ]
      }
    ];
  };

  // Static method to seed default roles
  Role.seedDefaultRoles = async function() {
    try {
      const defaultRoles = this.getDefaultRoles();

      for (const roleData of defaultRoles) {
        const [role, created] = await this.findOrCreate({
          where: { code: roleData.code },
          defaults: roleData
        });

        if (created) {
          console.log(`Created default role: ${role.name}`);
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to seed default roles:', error.message);
      return false;
    }
  };

  return Role;
};
