const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [3, 50]
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        isEmail: true
      }
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    first_name: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    last_name: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    subsidiary_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'subsidiaries',
        key: 'id'
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true
    },

    password_changed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    settings: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['email']
      },
      {
        fields: ['subsidiary_id']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['username', 'subsidiary_id']
      }
    ],
    hooks: {
      beforeCreate: async (user) => {
        if (user.password_hash && !user.password_hash.startsWith('$2a$')) {
          user.password_hash = await bcrypt.hash(user.password_hash, 12);
        }
        // Only set password_changed_at if it's not explicitly set (null means force password reset)
        if (user.password_changed_at === undefined) {
          user.password_changed_at = new Date();
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password_hash') && !user.password_hash.startsWith('$2a$')) {
          user.password_hash = await bcrypt.hash(user.password_hash, 12);
          user.password_changed_at = new Date();
        }
      }
    }
  });

  // Instance methods
  User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password_hash);
  };

  User.prototype.updateLastLogin = async function() {
    this.last_login_at = new Date();
    await this.save();
  };

  User.prototype.hasRole = async function(roleCode) {
    const roles = await this.getRoles();
    return roles.some(role => role.code === roleCode);
  };

  User.prototype.hasPermission = async function(permission) {
    const roles = await this.getRoles();
    return roles.some(role => role.hasPermission(permission));
  };

  User.prototype.getFullName = function() {
    if (this.first_name && this.last_name) {
      return `${this.first_name} ${this.last_name}`;
    }
    return this.username;
  };

  User.prototype.toJSON = function() {
    const values = { ...this.get() };
    // Don't expose password hash
    delete values.password_hash;
    return values;
  };

  return User;
};
