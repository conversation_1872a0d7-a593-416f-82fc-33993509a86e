const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const http = require('http');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import analytics and WebSocket services
const analyticsService = require('./services/analyticsService');
const websocketService = require('./services/websocketService');

// Import database and seeding services
const { initializeDatabase } = require('./config/database');
const { seedDatabase } = require('./database/seeders');

// Import enhanced security middleware
const {
  rateLimits,
  securityHeaders,
  validateInput,
  validateFileAccess,
  securityLogger,
  corsOptions,
  securityErrorHandler
} = require('./middleware/security');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3000;

// Enhanced Security middleware
app.use(securityHeaders);
app.use(compression());

// Security logging and monitoring
app.use(securityLogger);

// Analytics tracking middleware
app.use((req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    const userId = req.user?.id || null;

    // Track API call
    analyticsService.trackApiCall(
      req.method,
      req.path,
      responseTime,
      res.statusCode,
      userId
    );
  });

  next();
});

// Apply CORS only to API routes, not static files
app.use('/api', cors(corsOptions));

// General API rate limiting
app.use('/api', rateLimits.api);

// Input validation and sanitization
app.use(validateInput);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Logging
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
}

// Routes with specific rate limiting
app.use('/api/auth', rateLimits.auth, require('./routes/auth'));
app.use('/api/files', rateLimits.files, require('./routes/files'));
app.use('/api/users', require('./routes/users'));
app.use('/api/analytics', rateLimits.api, require('./routes/analytics'));
app.use('/api/subsidiaries', require('./routes/subsidiaries'));
app.use('/api/audit', rateLimits.api, require('./routes/audit'));

// Mambu system call - serve app definition XML
app.get("/audio-manager/app-definition", (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../../public/dev.xml"));
});

// API status route (moved from root to avoid conflict with static files)
app.get('/api/status', (req, res) => {
  res.json({
    message: 'Audio Vault API is running!',
    version: '1.0.0',
    environment: process.env.NODE_ENV
  });
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  const { testS3Connection } = require('./config/aws');

  try {
    const s3Status = await testS3Connection();

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      services: {
        s3: {
          status: s3Status.success ? 'connected' : 'disconnected',
          message: s3Status.message
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      error: error.message
    });
  }
});

// Security error handling middleware (must be before other error handlers)
app.use(securityErrorHandler);

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "../../public")));

// Serve static frontend files in production
if (process.env.NODE_ENV === 'production') {
  // Serve static files from frontend/dist (where Vite builds to)
  const staticPath = path.join(__dirname, '../../frontend/dist');
  console.log('Serving static files from:', staticPath);

  // Check if the directory exists and list contents
  if (fs.existsSync(staticPath)) {
    const files = fs.readdirSync(staticPath);
    console.log('Static files found:', files);
  } else {
    console.log('Static directory does not exist:', staticPath);
  }

  // Debug middleware to see what requests are coming in
  app.use((req, res, next) => {
    console.log(`Request: ${req.method} ${req.path}`);
    next();
  });

  // Configure static file serving with proper MIME types and caching
  app.use(express.static(staticPath, {
    maxAge: '1d', // Cache static assets for 1 day
    etag: true,
    lastModified: true,
    setHeaders: (res, filePath) => {
      console.log('Serving static file:', filePath);

      // Set proper MIME types
      if (filePath.endsWith('.css')) {
        res.setHeader('Content-Type', 'text/css; charset=utf-8');
      } else if (filePath.endsWith('.js')) {
        res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      } else if (filePath.endsWith('.html')) {
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
      } else if (filePath.endsWith('.svg')) {
        res.setHeader('Content-Type', 'image/svg+xml');
      } else if (filePath.endsWith('.png')) {
        res.setHeader('Content-Type', 'image/png');
      } else if (filePath.endsWith('.ico')) {
        res.setHeader('Content-Type', 'image/x-icon');
      }

      // Disable caching for index.html to ensure updates are loaded
      if (filePath.endsWith('index.html')) {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
      }
    }
  }));

  // Handle client-side routing - serve index.html for all non-API routes
  // This should come AFTER static file middleware so static files are served first
  app.get('*', (req, res) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ message: 'API route not found' });
    }

    // At this point, static files should have been handled by express.static middleware above
    // So we can safely serve index.html for client-side routing
    const indexPath = path.join(staticPath, 'index.html');
    console.log('Serving index.html for client-side route:', req.path);
    res.sendFile(indexPath);
  });
} else {
  // Development mode - just handle API 404s
  app.use('*', (req, res) => {
    res.status(404).json({ message: 'Route not found' });
  });
}

// Error handling middleware
app.use(require('./middleware/errorHandler'));

// Initialize WebSocket server
websocketService.initialize(server);

// Initialize database and start server
const startServer = async () => {
  try {
    // Initialize database
    console.log('Initializing database...');
    const dbInitialized = await initializeDatabase();

    if (!dbInitialized) {
      console.error('Failed to initialize database. Exiting...');
      process.exit(1);
    }

    // Seed database with default data
    console.log('Seeding database...');
    await seedDatabase();

    // Start server - bind to all interfaces for Docker
    server.listen(PORT, '0.0.0.0', () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Server listening on 0.0.0.0:${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
      console.log(`Using PORT from env: ${process.env.PORT}`);
      console.log(`Analytics service initialized`);
      console.log(`Database initialized and seeded`);
    });
  } catch (error) {
    console.error('Failed to start server:', error.message);
    process.exit(1);
  }
};

// Start the server
startServer();
