const { Sequelize } = require('sequelize');

// Database configuration
const config = {
  development: {
    dialect: process.env.DB_DIALECT || 'postgres',
    host: process.env.DB_HOST || '127.0.0.1',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_DBNAME || 'audio_management_dev',
    username: process.env.DB_USERNAME || 'dev',
    password: process.env.DB_PASSWORD || 'Ahadho13',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  production: {
    dialect: process.env.DB_DIALECT || 'postgres',
    host: process.env.DB_HOST || '127.0.0.1',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_DBNAME || 'audio_management_prod',
    username: process.env.DB_USERNAME || 'dev',
    password: process.env.DB_PASSWORD || 'Ahadho13',
    logging: false,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    },
    pool: {
      max: 20,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
};

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Create Sequelize instance
const sequelize = new Sequelize(dbConfig);

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully');
    return true;
  } catch (error) {
    console.error('Unable to connect to database:', error.message);
    return false;
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    // Test connection
    await testConnection();
    // Sync models (create tables) - PostgreSQL handles this much better than SQLite
    await sequelize.sync({ alter: process.env.NODE_ENV === 'development' });
    console.log('Database synchronized successfully');

    return true;
  } catch (error) {
    console.error('Database initialization failed:', error.message);
    return false;
  }
};

module.exports = {
  sequelize,
  testConnection,
  initializeDatabase
};
