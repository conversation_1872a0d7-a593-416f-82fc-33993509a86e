const AWS = require('aws-sdk');
//Validate required environment variables
const requiredEnvVars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'S3_BUCKET_NAME'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.warn(`Missing AWS environment variables: ${missingVars.join(', ')}`);
  console.warn('S3 functionality will be limited until these are configured.');
}
// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});
// Create S3 instance
const s3 = new AWS.S3({
  apiVersion: '2006-03-01',
  signatureVersion: 'v4'
});
// S3 configuration
const S3_CONFIG = {
  bucketName: process.env.S3_BUCKET_NAME,
  region: process.env.AWS_REGION || 'us-east-1',
  signedUrlExpires: {
    download: parseInt(process.env.DOWNLOAD_URL_EXPIRES) || 3600, // 1 hour for downloads
    stream: parseInt(process.env.STREAM_URL_EXPIRES) || 7200, // 2 hours for streaming
    bulk: parseInt(process.env.BULK_URL_EXPIRES) || 1800, // 30 minutes for bulk downloads
    upload: parseInt(process.env.UPLOAD_URL_EXPIRES) || 900 // 15 minutes for uploads
  },
  allowedAudioFormats: (process.env.ALLOWED_AUDIO_FORMATS || 'mp3,wav,ogg,m4a,aac,flac,wma').split(','),
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024, // 100MB
  uploadPath: 'audio-files/',
  rateLimits: {
    downloadsPerHour: parseInt(process.env.DOWNLOADS_PER_HOUR) || 100,
    bulkDownloadsPerHour: parseInt(process.env.BULK_DOWNLOADS_PER_HOUR) || 20,
    maxConcurrentDownloads: parseInt(process.env.MAX_CONCURRENT_DOWNLOADS) || 5
  }
};

// Test S3 connection
const testS3Connection = async () => {
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.S3_BUCKET_NAME) {
    return { success: false, message: 'AWS credentials not configured' };
  }
  try {
    await s3.headBucket({ Bucket: S3_CONFIG.bucketName }).promise();
    console.log('S3 connection successful');
    return { success: true, message: 'S3 connection successful' };
  } catch (error) {
    console.error('S3 connection failed:', error.message);
    return { success: false, message: `S3 connection failed: ${error.message}` };
  }
};

module.exports = {
  s3,
  S3_CONFIG,
  testS3Connection
};
