/**
 * Role-based authorization middleware
 * Checks if the authenticated user has the required role(s)
 */

/**
 * Middleware to require specific roles
 * @param {string|string[]} requiredRoles - Single role or array of roles
 * @returns {Function} Express middleware function
 */
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Ensure user has roles
    if (!req.user.roles || !Array.isArray(req.user.roles)) {
      return res.status(403).json({
        success: false,
        message: 'User roles not found'
      });
    }

    // Convert single role to array
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    
    // Check if user has any of the required roles
    const userRoles = req.user.roles.map(role => role.code);
    const hasRequiredRole = roles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role(s): ${roles.join(', ')}`,
        userRoles: userRoles
      });
    }

    next();
  };
};

/**
 * Middleware to require admin role
 */
const requireAdmin = requireRole('ADMIN');

/**
 * Middleware to require compliance officer role or higher
 */
const requireCompliance = requireRole(['ADMIN', 'COMPLIANCE']);

/**
 * Middleware to require QA role or higher
 */
const requireQA = requireRole(['ADMIN', 'COMPLIANCE', 'QA']);

/**
 * Check if user has specific permission
 * @param {string} permission - Permission to check
 * @returns {Function} Express middleware function
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Ensure user has roles
    if (!req.user.roles || !Array.isArray(req.user.roles)) {
      return res.status(403).json({
        success: false,
        message: 'User roles not found'
      });
    }

    // Collect all permissions from user's roles
    const userPermissions = new Set();
    req.user.roles.forEach(role => {
      if (role.permissions && Array.isArray(role.permissions)) {
        role.permissions.forEach(perm => userPermissions.add(perm));
      }
    });

    if (!userPermissions.has(permission)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permission: ${permission}`,
        userPermissions: Array.from(userPermissions)
      });
    }

    next();
  };
};

/**
 * Check if user belongs to specific subsidiary
 * @param {number} subsidiaryId - Subsidiary ID to check
 * @returns {Function} Express middleware function
 */
const requireSubsidiary = (subsidiaryId) => {
  return (req, res, next) => {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user belongs to the required subsidiary
    if (req.user.subsidiary_id !== subsidiaryId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient subsidiary access',
        userSubsidiary: req.user.subsidiary_id,
        requiredSubsidiary: subsidiaryId
      });
    }

    next();
  };
};

/**
 * Check if user can access subsidiary data
 * Admins can access all subsidiaries, others only their own
 */
const checkSubsidiaryAccess = (req, res, next) => {
  // Ensure user is authenticated
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Get requested subsidiary ID from params or query
  const requestedSubsidiaryId = req.params.subsidiaryId || req.query.subsidiaryId;
  
  if (requestedSubsidiaryId) {
    const subsidiaryId = parseInt(requestedSubsidiaryId);
    
    // Check if user has admin role
    const isAdmin = req.user.roles && req.user.roles.some(role => role.code === 'ADMIN');
    
    if (!isAdmin && req.user.subsidiary_id !== subsidiaryId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Cannot access other subsidiary data',
        userSubsidiary: req.user.subsidiary_id,
        requestedSubsidiary: subsidiaryId
      });
    }
  }

  next();
};

module.exports = {
  requireRole,
  requireAdmin,
  requireCompliance,
  requireQA,
  requirePermission,
  requireSubsidiary,
  checkSubsidiaryAccess
};
