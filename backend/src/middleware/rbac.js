/**
 * Role-Based Access Control (RBAC) Middleware
 * Implements user roles and permissions for the Audio File Management system
 */
// Define user roles and their permissions
const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  VIEWER: 'viewer'
};
const PERMISSIONS = {
  // File permissions
  FILES_READ: 'files:read',
  FILES_WRITE: 'files:write',
  FILES_DELETE: 'files:delete',
  FILES_DOWNLOAD: 'files:download',
  FILES_UPLOAD: 'files:upload',
  
  // User management permissions
  USERS_READ: 'users:read',
  USERS_WRITE: 'users:write',
  USERS_DELETE: 'users:delete',
  
  // System permissions
  SYSTEM_ADMIN: 'system:admin',
  ANALYTICS_READ: 'analytics:read'
};

// Role-permission mapping
const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    PERMISSIONS.FILES_READ,
    PERMISSIONS.FILES_WRITE,
    PERMISSIONS.FILES_DELETE,
    PERMISSIONS.FILES_DOWNLOAD,
    PERMISSIONS.FILES_UPLOAD,
    PERMISSIONS.USERS_READ,
    PERMISSIONS.USERS_WRITE,
    PERMISSIONS.USERS_DELETE,
    PERMISSIONS.SYSTEM_ADMIN,
    PERMISSIONS.ANALYTICS_READ
  ],
  
  [ROLES.USER]: [
    PERMISSIONS.FILES_READ,
    PERMISSIONS.FILES_WRITE,
    PERMISSIONS.FILES_DELETE,
    PERMISSIONS.FILES_DOWNLOAD,
    PERMISSIONS.FILES_UPLOAD,
    PERMISSIONS.ANALYTICS_READ
  ],
  
  [ROLES.VIEWER]: [
    PERMISSIONS.FILES_READ,
    PERMISSIONS.FILES_DOWNLOAD
  ]
};

/**
 * Check if a user has a specific permission
 * @param {Object} user - User object with permissions array from JWT
 * @param {string} permission - Permission to check
 * @returns {boolean} - Whether user has permission
 */
const hasPermission = (user, permission) => {
  if (!user) {
    return false;
  }

  // Check if user has permissions array from JWT (new format)
  if (user.permissions && Array.isArray(user.permissions)) {
    return user.permissions.includes(permission);
  }

  // Fallback to old role-based format for backward compatibility
  if (user.role) {
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes(permission);
  }

  return false;
};

/**
 * Check if a user has any of the specified permissions
 * @param {Object} user - User object with role property
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} - Whether user has any of the permissions
 */
const hasAnyPermission = (user, permissions) => {
  return permissions.some(permission => hasPermission(user, permission));
};

/**
 * Check if a user has all of the specified permissions
 * @param {Object} user - User object with role property
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} - Whether user has all permissions
 */
const hasAllPermissions = (user, permissions) => {
  return permissions.every(permission => hasPermission(user, permission));
};
/**
 * Middleware to require specific permission
 * @param {string} permission - Required permission
 * @returns {Function} - Express middleware function
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (!hasPermission(req.user, permission)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        required: permission,
        userRoles: req.user.roles?.map(r => r.code) || req.user.role,
        userPermissions: req.user.permissions || []
      });
    }

    next();
  };
};

/**
 * Middleware to require any of the specified permissions
 * @param {Array} permissions - Array of permissions (user needs at least one)
 * @returns {Function} - Express middleware function
 */
const requireAnyPermission = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (!hasAnyPermission(req.user, permissions)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        required: permissions,
        userRole: req.user.role
      });
    }

    next();
  };
};

/**
 * Middleware to require all of the specified permissions
 * @param {Array} permissions - Array of permissions (user needs all)
 * @returns {Function} - Express middleware function
 */
const requireAllPermissions = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (!hasAllPermissions(req.user, permissions)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        required: permissions,
        userRole: req.user.role
      });
    }

    next();
  };
};

/**
 * Middleware to check if user can access their own resources or is admin
 * @param {string} userIdParam - Parameter name containing user ID (default: 'id')
 * @returns {Function} - Express middleware function
 */
const requireOwnershipOrAdmin = (userIdParam = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const targetUserId = req.params[userIdParam];
    const isOwner = req.user.id === parseInt(targetUserId);
    const isAdmin = hasPermission(req.user, PERMISSIONS.SYSTEM_ADMIN);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only access your own resources.'
      });
    }

    next();
  };
};

/**
 * Get user permissions based on role
 * @param {string} role - User role
 * @returns {Array} - Array of permissions
 */
const getUserPermissions = (role) => {
  return ROLE_PERMISSIONS[role] || [];
};

/**
 * Validate if a role exists
 * @param {string} role - Role to validate
 * @returns {boolean} - Whether role is valid
 */
const isValidRole = (role) => {
  return Object.values(ROLES).includes(role);
};

/**
 * Get role hierarchy (higher number = more permissions)
 * @param {string} role - Role to get hierarchy for
 * @returns {number} - Role hierarchy level
 */
const getRoleHierarchy = (role) => {
  const hierarchy = {
    [ROLES.VIEWER]: 1,
    [ROLES.USER]: 2,
    [ROLES.ADMIN]: 3
  };
  return hierarchy[role] || 0;
};

/**
 * Check if user can perform action on target user based on role hierarchy
 * @param {Object} user - Acting user
 * @param {Object} targetUser - Target user
 * @returns {boolean} - Whether action is allowed
 */
const canActOnUser = (user, targetUser) => {
  if (!user || !targetUser) return false;
  
  // Users can always act on themselves
  if (user.id === targetUser.id) return true;
  
  // Check role hierarchy
  const userLevel = getRoleHierarchy(user.role);
  const targetLevel = getRoleHierarchy(targetUser.role);
  
  return userLevel > targetLevel;
};

module.exports = {
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  requireOwnershipOrAdmin,
  getUserPermissions,
  isValidRole,
  getRoleHierarchy,
  canActOnUser
};
