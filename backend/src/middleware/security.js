const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');

/**
 * Rate limiting configurations
 */
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Custom key generator to include user ID if authenticated
    keyGenerator: (req) => {
      return req.user ? `${req.ip}-${req.user.id}` : req.ip;
    }
  });
};

// Different rate limits for different endpoints
const rateLimits = {
  // Rate limiting for authentication endpoints (more lenient for development)
  auth: createRateLimit(
    process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 15 * 60 * 1000, // 5 minutes in dev, 15 in prod
    process.env.NODE_ENV === 'development' ? 20 : 5, // 20 attempts in dev, 5 in prod
    process.env.NODE_ENV === 'development'
      ? 'Too many authentication attempts. Please try again in 5 minutes.'
      : 'Too many authentication attempts. Please try again in 15 minutes.'
  ),
  
  // General API rate limiting
  api: createRateLimit(
    15 * 60 * 1000, // 15 minutes
    100, // 100 requests per window
    'Too many API requests. Please try again later.'
  ),
  
  // File operations rate limiting
  files: createRateLimit(
    1 * 60 * 1000, // 1 minute
    30, // 30 file operations per minute
    'Too many file operations. Please slow down.'
  ),
  
  // Download rate limiting
  downloads: createRateLimit(
    5 * 60 * 1000, // 5 minutes
    10, // 10 downloads per 5 minutes
    'Download limit exceeded. Please wait before downloading more files.'
  )
};

/**
 * Security headers middleware using Helmet
 */
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: [
        "'self'",
        "'unsafe-inline'",
        "https://fonts.googleapis.com",
        "https://cdnjs.cloudflare.com" // Allow Font Awesome
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com",
        "https://cdnjs.cloudflare.com" // Allow Font Awesome fonts
      ],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'",
        "'unsafe-eval'", // Allow inline scripts for Vite
        "https://cdnjs.cloudflare.com" // Allow Font Awesome scripts if needed
      ],
      connectSrc: [
        "'self'",
        "https://s3.amazonaws.com",
        "https://*.amazonaws.com",
        "http://localhost:*",
        "ws://localhost:*"
      ],
      mediaSrc: ["'self'", "https://s3.amazonaws.com", "https://*.amazonaws.com"]
    }
  },
  crossOriginEmbedderPolicy: false, // Disable for S3 compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * Input validation and sanitization middleware
 */
const validateInput = (req, res, next) => {
  // Sanitize string inputs
  const sanitizeString = (str) => {
    if (typeof str !== 'string') return str;
    return validator.escape(str.trim());
  };

  // Recursively sanitize object properties
  const sanitizeObject = (obj) => {
    if (typeof obj !== 'object' || obj === null) return obj;
    
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        sanitized[key] = sanitizeString(value);
      } else if (typeof value === 'object') {
        sanitized[key] = sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  };
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    req.body = sanitizeObject(req.body);
  }
  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    req.query = sanitizeObject(req.query);
  }

  next();
};

/**
 * File access validation middleware
 */
const validateFileAccess = (req, res, next) => {
  const { key } = req.params;
  
  if (!key) {
    return res.status(400).json({
      success: false,
      message: 'File key is required'
    });
  }

  // Validate file key format (prevent path traversal but allow forward slashes for S3 keys)
  if (key.includes('..') || key.includes('\\')) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file key format'
    });
  }

  // Check file extension (only allow audio files)
  const allowedExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'];
  const hasValidExtension = allowedExtensions.some(ext => 
    key.toLowerCase().endsWith(ext)
  );

  if (!hasValidExtension) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file type. Only audio files are allowed.'
    });
  }

  next();
};

/**
 * Request logging middleware for security monitoring
 */
const securityLogger = (req, res, next) => {
  const startTime = Date.now();
  // Log security-relevant information
  const logData = {
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    method: req.method,
    url: req.originalUrl,
    userId: req.user?.id || null,
    userRole: req.user?.role || null
  };

  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /data:.*base64/i  // Data URI attacks
  ];

  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(req.originalUrl) || 
    pattern.test(JSON.stringify(req.body)) ||
    pattern.test(JSON.stringify(req.query))
  );

  if (isSuspicious) {
    console.warn('Suspicious request detected:', logData);
  }

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const responseLog = {
      ...logData,
      statusCode: res.statusCode,
      duration: `${duration}ms`
    };

    // Log failed authentication attempts
    if (req.originalUrl.includes('/auth/') && res.statusCode === 401) {
      console.warn('Failed authentication attempt:', responseLog);
    }

    // Log successful logins
    if (req.originalUrl.includes('/auth/login') && res.statusCode === 200) {
      console.info('Successful login:', responseLog);
    }

    // Log admin actions
    if (req.user?.role === 'admin' && req.method !== 'GET') {
      console.info('Admin action:', responseLog);
    }
  });

  next();
};

/**
 * CORS configuration for production
 */
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    // Get allowed origins from environment variable or use defaults
    const corsOriginEnv = process.env.CORS_ORIGIN || '';
    const envOrigins = corsOriginEnv ? corsOriginEnv.split(',').map(o => o.trim()) : [];

    const defaultOrigins = [
      'http://localhost:5173',
      'http://localhost:5174', // Added for current Vite dev server
      'http://localhost:5175', // Added for additional Vite dev server
      'http://localhost:3000',
      'http://localhost:8081', // Added for Docker containerized app
      'http://127.0.0.1:5173', // Added for 127.0.0.1 access
      'http://127.0.0.1:5174',
      'http://127.0.0.1:5175',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:8081', // Added for Docker containerized app
      'https://audio-uat.platcorpgroup.com' // Production UAT domain
    ];

    // Combine environment origins with defaults, removing duplicates
    const allowedOrigins = [...new Set([...defaultOrigins, ...envOrigins])];

    console.log('CORS check - Origin:', origin, 'Allowed:', allowedOrigins.includes(origin));

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

/**
 * Error handling middleware for security errors
 */
const securityErrorHandler = (err, req, res, next) => {
  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Rate limiting errors
  if (err.status === 429) {
    return res.status(429).json({
      success: false,
      message: 'Too many requests',
      retryAfter: err.retryAfter
    });
  }

  // CORS errors
  if (err.message === 'Not allowed by CORS') {
    return res.status(403).json({
      success: false,
      message: 'CORS policy violation'
    });
  }

  // JWT/Authentication errors
  if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: err.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token'
    });
  }

  // CSRF errors
  if (err.code === 'EBADCSRFTOKEN') {
    return res.status(403).json({
      success: false,
      message: 'Invalid CSRF token'
    });
  }

  // Only handle security-specific errors, pass others to next middleware
  const securityErrorTypes = [
    'CORS',
    'CSRF',
    'JWT',
    'RateLimit',
    'SecurityViolation',
    'InvalidToken',
    'AccessDenied'
  ];

  const isSecurityError = securityErrorTypes.some(type =>
    err.message?.includes(type) ||
    err.name?.includes(type) ||
    err.type?.includes(type)
  );

  if (isSecurityError) {
    console.error('Security error:', err);
    return res.status(500).json({
      success: false,
      message: 'Security error occurred',
      ...(isDevelopment && { error: err.message })
    });
  }

  // Not a security error, pass to next error handler
  next(err);
};

module.exports = {
  rateLimits,
  securityHeaders,
  validateInput,
  validateFileAccess,
  securityLogger,
  corsOptions,
  securityErrorHandler
};
