/**
 * WebSocket Service for Real-time Analytics
 * Provides real-time updates to connected clients
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const analyticsService = require('./analyticsService');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map of userId -> WebSocket connections
    this.setupAnalyticsListeners();
  }
  
  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws/analytics'
    });
    
    this.wss.on('connection', (ws, req) => {
      this.handleConnection(ws, req);
    });
    
    console.log('WebSocket server initialized for real-time analytics');
  }
  
  /**
   * Handle new WebSocket connection
   */
  async handleConnection(ws, req) {
    try {
      // Extract token from query parameters or headers
      const url = new URL(req.url, `http://${req.headers.host}`);
      const token = url.searchParams.get('token') || req.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        ws.close(1008, 'Authentication required');
        return;
      }
      
      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const userId = decoded.id;
      
      // Store client connection
      if (!this.clients.has(userId)) {
        this.clients.set(userId, new Set());
      }
      this.clients.get(userId).add(ws);
      
      console.log(`WebSocket client connected: User ${userId}`);
      
      // Send initial dashboard data
      this.sendToClient(ws, {
        type: 'dashboard_data',
        data: analyticsService.getDashboardData()
      });
      
      // Handle client messages
      ws.on('message', (message) => {
        this.handleMessage(ws, userId, message);
      });
      
      // Handle client disconnect
      ws.on('close', () => {
        this.handleDisconnect(userId, ws);
      });
      
      // Handle errors
      ws.on('error', (error) => {
        console.error(`WebSocket error for user ${userId}:`, error);
        this.handleDisconnect(userId, ws);
      });
      
    } catch (error) {
      console.error('WebSocket authentication error:', error);
      ws.close(1008, 'Invalid token');
    }
  }
  
  /**
   * Handle client messages
   */
  handleMessage(ws, userId, message) {
    try {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case 'subscribe_analytics':
          // Client wants to subscribe to analytics updates
          this.sendToClient(ws, {
            type: 'subscription_confirmed',
            data: { analytics: true }
          });
          break;
          
        case 'request_historical':
          // Client requests historical data
          const timeRange = data.timeRange || '24h';
          const historicalData = analyticsService.getHistoricalData(timeRange);
          this.sendToClient(ws, {
            type: 'historical_data',
            data: historicalData,
            timeRange
          });
          break;
          
        case 'ping':
          // Heartbeat
          this.sendToClient(ws, { type: 'pong' });
          break;
          
        default:
          console.log(`Unknown WebSocket message type: ${data.type}`);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }
  
  /**
   * Handle client disconnect
   */
  handleDisconnect(userId, ws) {
    if (this.clients.has(userId)) {
      this.clients.get(userId).delete(ws);
      
      // Remove user entry if no more connections
      if (this.clients.get(userId).size === 0) {
        this.clients.delete(userId);
      }
    }
    
    console.log(`WebSocket client disconnected: User ${userId}`);
  }
  
  /**
   * Send message to specific client
   */
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }
  
  /**
   * Send message to specific user (all their connections)
   */
  sendToUser(userId, message) {
    if (this.clients.has(userId)) {
      this.clients.get(userId).forEach(ws => {
        this.sendToClient(ws, message);
      });
    }
  }
  
  /**
   * Broadcast message to all connected clients
   */
  broadcast(message) {
    this.clients.forEach((connections, userId) => {
      connections.forEach(ws => {
        this.sendToClient(ws, message);
      });
    });
  }
  
  /**
   * Broadcast to admin users only
   */
  broadcastToAdmins(message) {
    // In a real implementation, you'd check user roles
    // For now, broadcast to all users
    this.broadcast(message);
  }
  
  /**
   * Setup analytics event listeners
   */
  setupAnalyticsListeners() {
    // Listen for live events from analytics service
    analyticsService.on('liveEvent', (event) => {
      this.broadcast({
        type: 'live_event',
        data: event
      });
    });
    
    // Send periodic dashboard updates
    setInterval(() => {
      const dashboardData = analyticsService.getDashboardData();
      this.broadcast({
        type: 'dashboard_update',
        data: dashboardData
      });
    }, 5000); // Update every 5 seconds
  }
  
  /**
   * Get connection statistics
   */
  getConnectionStats() {
    let totalConnections = 0;
    this.clients.forEach(connections => {
      totalConnections += connections.size;
    });
    
    return {
      connectedUsers: this.clients.size,
      totalConnections,
      clients: Array.from(this.clients.keys())
    };
  }
  
  /**
   * Send system notification
   */
  sendSystemNotification(message, level = 'info') {
    this.broadcast({
      type: 'system_notification',
      data: {
        message,
        level,
        timestamp: new Date()
      }
    });
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

module.exports = websocketService;
