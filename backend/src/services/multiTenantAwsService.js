const AWS = require('aws-sdk');
const { Subsidiary } = require('../models');

class MultiTenantAwsService {
  constructor() {
    this.s3Instances = new Map(); // Cache S3 instances by subsidiary ID
    this.awsConfigs = new Map(); // Cache AWS configs by subsidiary ID
  }

  /**
   * Get AWS configuration for a subsidiary
   * @param {number} subsidiaryId - Subsidiary ID
   * @returns {Object} AWS configuration
   */
  async getAwsConfig(subsidiaryId) {
    try {
      // Check cache first
      if (this.awsConfigs.has(subsidiaryId)) {
        return this.awsConfigs.get(subsidiaryId);
      }

      // Fetch from database
      const subsidiary = await Subsidiary.findByPk(subsidiaryId);
      if (!subsidiary) {
        throw new Error(`Subsidiary with ID ${subsidiaryId} not found`);
      }

      if (!subsidiary.is_active) {
        throw new Error(`Subsidiary ${subsidiary.name} is not active`);
      }

      const config = subsidiary.getAwsConfig();
      
      // Validate required credentials
      if (!config.accessKeyId || !config.secretAccessKey || !config.bucketName) {
        throw new Error(`Incomplete AWS configuration for subsidiary ${subsidiary.name}`);
      }

      // Cache the config
      this.awsConfigs.set(subsidiaryId, config);
      
      return config;
    } catch (error) {
      console.error(`Failed to get AWS config for subsidiary ${subsidiaryId}:`, error.message);
      throw error;
    }
  }

  /**
   * Get S3 instance for a subsidiary
   * @param {number} subsidiaryId - Subsidiary ID
   * @returns {AWS.S3} S3 instance
   */
  async getS3Instance(subsidiaryId) {
    try {
      // Check cache first
      if (this.s3Instances.has(subsidiaryId)) {
        return this.s3Instances.get(subsidiaryId);
      }

      // Get AWS config
      const config = await this.getAwsConfig(subsidiaryId);

      // Create S3 instance with subsidiary-specific credentials
      const s3 = new AWS.S3({
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
        region: config.region,
        apiVersion: '2006-03-01',
        signatureVersion: 'v4'
      });

      // Cache the S3 instance
      this.s3Instances.set(subsidiaryId, s3);

      return s3;
    } catch (error) {
      console.error(`Failed to get S3 instance for subsidiary ${subsidiaryId}:`, error.message);
      throw error;
    }
  }

  /**
   * Test S3 connection for a subsidiary
   * @param {number} subsidiaryId - Subsidiary ID
   * @returns {Object} Connection test result
   */
  async testS3Connection(subsidiaryId) {
    try {
      const s3 = await this.getS3Instance(subsidiaryId);
      const config = await this.getAwsConfig(subsidiaryId);

      // Test by listing objects in the bucket (limit to 1 for efficiency)
      await s3.listObjectsV2({
        Bucket: config.bucketName,
        MaxKeys: 1
      }).promise();

      return {
        success: true,
        message: 'S3 connection successful',
        bucketName: config.bucketName,
        region: config.region
      };
    } catch (error) {
      return {
        success: false,
        message: `S3 connection failed: ${error.message}`,
        error: error.code || 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Generate signed URL for file download
   * @param {number} subsidiaryId - Subsidiary ID
   * @param {string} fileKey - S3 file key
   * @param {number} expiresIn - URL expiration time in seconds
   * @param {Object} options - Additional options (responseHeaders, etc.)
   * @returns {string} Signed URL
   */
  async generateSignedUrl(subsidiaryId, fileKey, expiresIn = 3600, options = {}) {
    try {
      const s3 = await this.getS3Instance(subsidiaryId);
      const config = await this.getAwsConfig(subsidiaryId);

      const params = {
        Bucket: config.bucketName,
        Key: fileKey,
        Expires: expiresIn
      };

      // Add response headers if provided (for forcing download)
      if (options.responseHeaders) {
        params.ResponseContentDisposition = options.responseHeaders.contentDisposition;
        if (options.responseHeaders.contentType) {
          params.ResponseContentType = options.responseHeaders.contentType;
        }
      }

      return s3.getSignedUrl('getObject', params);
    } catch (error) {
      console.error(`Failed to generate signed URL for subsidiary ${subsidiaryId}:`, error.message);
      throw error;
    }
  }

  /**
   * List files in S3 bucket for a subsidiary
   * @param {number} subsidiaryId - Subsidiary ID
   * @param {Object} options - List options (prefix, maxKeys, continuationToken)
   * @returns {Object} List result
   */
  async listFiles(subsidiaryId, options = {}) {
    try {
      const s3 = await this.getS3Instance(subsidiaryId);
      const config = await this.getAwsConfig(subsidiaryId);

      const params = {
        Bucket: config.bucketName,
        MaxKeys: options.maxKeys || 1000,
        Prefix: options.prefix || '',
        ContinuationToken: options.continuationToken
      };

      const result = await s3.listObjectsV2(params).promise();
      
      return {
        files: result.Contents || [],
        isTruncated: result.IsTruncated,
        nextContinuationToken: result.NextContinuationToken,
        keyCount: result.KeyCount
      };
    } catch (error) {
      console.error(`Failed to list files for subsidiary ${subsidiaryId}:`, error.message);
      throw error;
    }
  }

  /**
   * Get file metadata from S3
   * @param {number} subsidiaryId - Subsidiary ID
   * @param {string} fileKey - S3 file key
   * @returns {Object} File metadata
   */
  async getFileMetadata(subsidiaryId, fileKey) {
    try {
      const s3 = await this.getS3Instance(subsidiaryId);
      const config = await this.getAwsConfig(subsidiaryId);

      const params = {
        Bucket: config.bucketName,
        Key: fileKey
      };

      const result = await s3.headObject(params).promise();
      
      return {
        size: result.ContentLength,
        lastModified: result.LastModified,
        contentType: result.ContentType,
        etag: result.ETag,
        metadata: result.Metadata || {}
      };
    } catch (error) {
      console.error(`Failed to get file metadata for subsidiary ${subsidiaryId}:`, error.message);
      throw error;
    }
  }

  /**
   * Clear cache for a subsidiary (useful when credentials are updated)
   * @param {number} subsidiaryId - Subsidiary ID
   */
  clearCache(subsidiaryId) {
    this.s3Instances.delete(subsidiaryId);
    this.awsConfigs.delete(subsidiaryId);
    console.log(`Cleared AWS cache for subsidiary ${subsidiaryId}`);
  }

  /**
   * Clear all caches
   */
  clearAllCaches() {
    this.s3Instances.clear();
    this.awsConfigs.clear();
    console.log('Cleared all AWS caches');
  }

  /**
   * Get all active subsidiaries with their AWS status
   * @returns {Array} Subsidiaries with AWS status
   */
  async getAllSubsidiariesStatus() {
    try {
      const subsidiaries = await Subsidiary.findAll({
        where: { is_active: true },
        attributes: ['id', 'name', 'code', 'aws_region', 's3_bucket_name']
      });

      const statusPromises = subsidiaries.map(async (subsidiary) => {
        const connectionTest = await this.testS3Connection(subsidiary.id);
        return {
          id: subsidiary.id,
          name: subsidiary.name,
          code: subsidiary.code,
          region: subsidiary.aws_region,
          bucketName: subsidiary.s3_bucket_name,
          status: connectionTest.success ? 'connected' : 'error',
          message: connectionTest.message
        };
      });

      return await Promise.all(statusPromises);
    } catch (error) {
      console.error('Failed to get subsidiaries status:', error.message);
      throw error;
    }
  }
}

// Create singleton instance
const multiTenantAwsService = new MultiTenantAwsService();

module.exports = multiTenantAwsService;
