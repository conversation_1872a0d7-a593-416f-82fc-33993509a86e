const { AuditLog, User, Subsidiary } = require('../models');
const { Op } = require('sequelize');

/**
 * Enhanced Audit Service for comprehensive logging and traceability
 * Handles all user actions and system events for security monitoring
 */
class AuditService {
  constructor() {
    this.eventTypes = {
      // Authentication Events
      AUTH: {
        LOGIN_SUCCESS: 'LOGIN_SUCCESS',
        LOGIN_FAILURE: 'LOGIN_FAILURE',
        LOGOUT: 'LOGOUT',
        SESSION_EXPIRED: 'SESSION_EXPIRED',
        PASSWORD_CHANGE: 'PASSWORD_CHANGE',
        ACCOUNT_LOCKED: 'ACCOUNT_LOCKED'
      },
      
      // S3 Bucket Access Events
      S3_ACCESS: {
        BUCKET_LIST: 'BUCKET_LIST',
        FILE_METADATA: 'FILE_METADATA',
        BUCKET_SYNC: 'BUCKET_SYNC',
        BUCKET_FILTER: 'BUCKET_FILTER',
        BUCKET_SEARCH: 'BUCKET_SEARCH'
      },
      
      // File Operations
      FILE_OPS: {
        FILE_UPLOAD: 'FILE_UPLOAD',
        FILE_DELETE: 'FILE_DELETE',
        FILE_MOVE: 'FILE_MOVE',
        FILE_COPY: 'FILE_COPY'
      },
      
      // Audio Playback Events
      PLAYBACK: {
        AUDIO_PLAY: 'AUDIO_PLAY',
        AUDIO_PAUSE: 'AUDIO_PAUSE',
        AUDIO_STOP: 'AUDIO_STOP',
        STREAM_START: 'STREAM_START',
        STREAM_END: 'STREAM_END'
      },
      
      // Download Events
      DOWNLOAD: {
        SINGLE_DOWNLOAD: 'SINGLE_DOWNLOAD',
        BULK_DOWNLOAD: 'BULK_DOWNLOAD',
        ZIP_DOWNLOAD: 'ZIP_DOWNLOAD',
        DOWNLOAD_FAILURE: 'DOWNLOAD_FAILURE'
      },
      
      // Search and Filter Events
      SEARCH: {
        SEARCH_QUERY: 'SEARCH_QUERY',
        FILTER_APPLY: 'FILTER_APPLY',
        SEARCH_SUGGESTIONS: 'SEARCH_SUGGESTIONS',
        ADVANCED_SEARCH: 'ADVANCED_SEARCH'
      },
      
      // User Management
      USER_MGMT: {
        USER_CREATE: 'USER_CREATE',
        USER_UPDATE: 'USER_UPDATE',
        USER_DELETE: 'USER_DELETE',
        ROLE_ASSIGN: 'ROLE_ASSIGN',
        ROLE_REVOKE: 'ROLE_REVOKE'
      },
      
      // System Events
      SYSTEM: {
        SYSTEM_START: 'SYSTEM_START',
        SYSTEM_SHUTDOWN: 'SYSTEM_SHUTDOWN',
        CONFIG_CHANGE: 'CONFIG_CHANGE',
        BACKUP_CREATE: 'BACKUP_CREATE',
        MAINTENANCE_MODE: 'MAINTENANCE_MODE'
      }
    };
    
    this.resourceTypes = {
      AUTH: 'auth',
      USER: 'user',
      FILE: 'file',
      BUCKET: 'bucket',
      SEARCH: 'search',
      DOWNLOAD: 'download',
      SYSTEM: 'system',
      SESSION: 'session'
    };
  }

  /**
   * Extract request metadata for audit logging
   */
  extractRequestMetadata(req) {
    return {
      ip_address: req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress,
      user_agent: req.get('User-Agent') || '',
      session_id: req.sessionID || req.session?.id,
      referer: req.get('Referer') || '',
      origin: req.get('Origin') || ''
    };
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(action, userId, subsidiaryId, details = {}, req = null, status = 'success', errorMessage = null) {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.AUTH,
      resource_id: userId?.toString(),
      details: {
        ...details,
        timestamp: new Date().toISOString()
      },
      status,
      error_message: errorMessage,
      ...metadata
    });
  }

  /**
   * Log S3 bucket access events
   */
  async logS3Access(action, userId, subsidiaryId, details = {}, req = null) {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.BUCKET,
      resource_id: subsidiaryId?.toString(),
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        bucket_operation: true
      },
      status: 'success',
      ...metadata
    });
  }

  /**
   * Log audio file playback events
   */
  async logPlaybackEvent(action, userId, subsidiaryId, fileKey, fileName, details = {}, req = null) {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.FILE,
      resource_id: fileKey,
      details: {
        file_name: fileName,
        file_key: fileKey,
        ...details,
        timestamp: new Date().toISOString(),
        playback_event: true
      },
      status: 'success',
      ...metadata
    });
  }

  /**
   * Log download events
   */
  async logDownloadEvent(action, userId, subsidiaryId, resourceId, details = {}, req = null, status = 'success') {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.DOWNLOAD,
      resource_id: resourceId,
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        download_event: true
      },
      status,
      ...metadata
    });
  }

  /**
   * Log search and filter events
   */
  async logSearchEvent(action, userId, subsidiaryId, searchParams, results = {}, req = null) {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.SEARCH,
      resource_id: `search_${Date.now()}`,
      details: {
        search_parameters: searchParams,
        results_count: results.totalFiles || 0,
        results_pages: results.totalPages || 0,
        timestamp: new Date().toISOString(),
        search_event: true
      },
      status: 'success',
      ...metadata
    });
  }

  /**
   * Log file operations
   */
  async logFileOperation(action, userId, subsidiaryId, fileKey, fileName, details = {}, req = null, status = 'success') {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.FILE,
      resource_id: fileKey,
      details: {
        file_name: fileName,
        file_key: fileKey,
        ...details,
        timestamp: new Date().toISOString(),
        file_operation: true
      },
      status,
      ...metadata
    });
  }

  /**
   * Log user management events
   */
  async logUserManagement(action, adminUserId, subsidiaryId, targetUserId, details = {}, req = null) {
    const metadata = req ? this.extractRequestMetadata(req) : {};
    
    return await this.createAuditLog({
      user_id: adminUserId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.USER,
      resource_id: targetUserId?.toString(),
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        user_management: true
      },
      status: 'success',
      ...metadata
    });
  }

  /**
   * Log system events
   */
  async logSystemEvent(action, details = {}, userId = null, subsidiaryId = null) {
    return await this.createAuditLog({
      user_id: userId,
      subsidiary_id: subsidiaryId,
      action,
      resource_type: this.resourceTypes.SYSTEM,
      resource_id: 'system',
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        system_event: true
      },
      status: 'success'
    });
  }

  /**
   * Create audit log entry
   */
  async createAuditLog(logData) {
    try {
      const auditLog = await AuditLog.create(logData);
      console.log(`Audit Log: ${logData.action} by user ${logData.user_id || 'system'}`);
      return auditLog;
    } catch (error) {
      console.error('Failed to create audit log:', error.message);
      // Don't throw error - audit logging failure shouldn't break the main operation
      return null;
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters = {}) {
    const {
      userId,
      subsidiaryId,
      action,
      resourceType,
      status,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = filters;

    const whereClause = {};
    
    if (userId) whereClause.user_id = userId;
    if (subsidiaryId) whereClause.subsidiary_id = subsidiaryId;
    if (action) whereClause.action = action;
    if (resourceType) whereClause.resource_type = resourceType;
    if (status) whereClause.status = status;
    
    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at[Op.gte] = new Date(startDate);
      if (endDate) whereClause.created_at[Op.lte] = new Date(endDate);
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await AuditLog.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: require('../models').User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'first_name', 'last_name']
        },
        {
          model: require('../models').Subsidiary,
          as: 'subsidiary',
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    return {
      logs: rows,
      totalLogs: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      hasNextPage: page * limit < count,
      hasPrevPage: page > 1
    };
  }
}

module.exports = new AuditService();
