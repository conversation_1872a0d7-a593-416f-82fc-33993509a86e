const fs = require('fs').promises;
const path = require('path');

class DownloadTrackingService {
  constructor() {
    this.downloadsFile = path.join(__dirname, '../data/downloads.json');
    this.rateLimitFile = path.join(__dirname, '../data/rate_limits.json');
    this.ensureDataDirectory();
  }

  /**
   * Ensure data directory exists
   */
  async ensureDataDirectory() {
    try {
      const dataDir = path.dirname(this.downloadsFile);
      await fs.mkdir(dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
    }
  }

  /**
   * Track a download request
   */
  async trackDownload(userId, fileKey, downloadType = 'single', userAgent = '', ipAddress = '') {
    try {
      const downloadRecord = {
        id: this.generateDownloadId(),
        userId,
        fileKey,
        downloadType, // 'single', 'bulk', 'zip'
        timestamp: new Date().toISOString(),
        userAgent,
        ipAddress,
        status: 'initiated'
      };

      // Load existing downloads
      let downloads = [];
      try {
        const data = await fs.readFile(this.downloadsFile, 'utf8');
        downloads = JSON.parse(data);
      } catch (error) {
        // File doesn't exist yet, start with empty array
      }

      downloads.push(downloadRecord);

      // Keep only last 10000 records to prevent file from growing too large
      if (downloads.length > 10000) {
        downloads = downloads.slice(-10000);
      }

      await fs.writeFile(this.downloadsFile, JSON.stringify(downloads, null, 2));
      
      console.log(`Download tracked: ${fileKey} by user ${userId}`);
      return downloadRecord;
    } catch (error) {
      console.error('Error tracking download:', error);
      // Don't throw error - tracking failure shouldn't break downloads
      return null;
    }
  }

  /**
   * Update download status
   */
  async updateDownloadStatus(downloadId, status, errorMessage = null) {
    try {
      const data = await fs.readFile(this.downloadsFile, 'utf8');
      const downloads = JSON.parse(data);
      
      const downloadIndex = downloads.findIndex(d => d.id === downloadId);
      if (downloadIndex !== -1) {
        downloads[downloadIndex].status = status;
        downloads[downloadIndex].completedAt = new Date().toISOString();
        if (errorMessage) {
          downloads[downloadIndex].error = errorMessage;
        }
        
        await fs.writeFile(this.downloadsFile, JSON.stringify(downloads, null, 2));
      }
    } catch (error) {
      console.error('Error updating download status:', error);
    }
  }

  /**
   * Check rate limit for user
   */
  async checkRateLimit(userId, maxDownloadsPerHour = 100) {
    try {
      let rateLimits = {};
      try {
        const data = await fs.readFile(this.rateLimitFile, 'utf8');
        rateLimits = JSON.parse(data);
      } catch (error) {
        // File doesn't exist yet
      }

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      // Clean up old entries
      if (rateLimits[userId]) {
        rateLimits[userId] = rateLimits[userId].filter(
          timestamp => new Date(timestamp) > oneHourAgo
        );
      } else {
        rateLimits[userId] = [];
      }

      // Check if user has exceeded rate limit
      if (rateLimits[userId].length >= maxDownloadsPerHour) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: new Date(Math.min(...rateLimits[userId].map(t => new Date(t).getTime())) + 60 * 60 * 1000)
        };
      }

      // Add current request
      rateLimits[userId].push(now.toISOString());
      
      // Save updated rate limits
      await fs.writeFile(this.rateLimitFile, JSON.stringify(rateLimits, null, 2));

      return {
        allowed: true,
        remaining: maxDownloadsPerHour - rateLimits[userId].length,
        resetTime: new Date(now.getTime() + 60 * 60 * 1000)
      };
    } catch (error) {
      console.error('Error checking rate limit:', error);
      // If rate limiting fails, allow the request
      return { allowed: true, remaining: 100, resetTime: new Date() };
    }
  }

  /**
   * Get download statistics for a user
   */
  async getUserDownloadStats(userId, days = 30) {
    try {
      const data = await fs.readFile(this.downloadsFile, 'utf8');
      const downloads = JSON.parse(data);
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      const userDownloads = downloads.filter(d => 
        d.userId === userId && 
        new Date(d.timestamp) > cutoffDate
      );

      return {
        totalDownloads: userDownloads.length,
        successfulDownloads: userDownloads.filter(d => d.status === 'completed').length,
        failedDownloads: userDownloads.filter(d => d.status === 'failed').length,
        downloadsByType: {
          single: userDownloads.filter(d => d.downloadType === 'single').length,
          bulk: userDownloads.filter(d => d.downloadType === 'bulk').length,
          zip: userDownloads.filter(d => d.downloadType === 'zip').length
        },
        recentDownloads: userDownloads.slice(-10).map(d => ({
          fileKey: d.fileKey,
          timestamp: d.timestamp,
          status: d.status,
          downloadType: d.downloadType
        }))
      };
    } catch (error) {
      console.error('Error getting user download stats:', error);
      return {
        totalDownloads: 0,
        successfulDownloads: 0,
        failedDownloads: 0,
        downloadsByType: { single: 0, bulk: 0, zip: 0 },
        recentDownloads: []
      };
    }
  }

  /**
   * Generate unique download ID
   */
  generateDownloadId() {
    return `dl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up old download records
   */
  async cleanupOldRecords(daysToKeep = 90) {
    try {
      const data = await fs.readFile(this.downloadsFile, 'utf8');
      const downloads = JSON.parse(data);
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      const filteredDownloads = downloads.filter(d => 
        new Date(d.timestamp) > cutoffDate
      );

      await fs.writeFile(this.downloadsFile, JSON.stringify(filteredDownloads, null, 2));
      
      console.log(`🧹 Cleaned up ${downloads.length - filteredDownloads.length} old download records`);
      return filteredDownloads.length;
    } catch (error) {
      console.error('Error cleaning up old records:', error);
      return 0;
    }
  }
}

module.exports = new DownloadTrackingService();
