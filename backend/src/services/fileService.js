const { s3, S3_CONFIG, testS3Connection } = require('../config/aws');
const { generateS3Key } = require('../middleware/s3Auth');
const downloadTrackingService = require('./downloadTrackingService');
const metadataService = require('./metadataService');
const multiTenantAwsService = require('./multiTenantAwsService');
const { AuditLog } = require('../models');
const path = require('path');
const archiver = require('archiver');

class FileService {
  constructor() {
    // Cache for file listings to avoid repeated S3 scans
    this.fileCache = new Map();
    this.cacheExpiry = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Check if cache is valid for a subsidiary
   */
  isCacheValid(subsidiaryId) {
    const expiry = this.cacheExpiry.get(subsidiaryId);
    return expiry && Date.now() < expiry;
  }

  /**
   * Get cached files for a subsidiary
   */
  getCachedFiles(subsidiaryId) {
    if (this.isCacheValid(subsidiaryId)) {
      return this.fileCache.get(subsidiaryId);
    }
    return null;
  }

  /**
   * Cache files for a subsidiary
   */
  setCachedFiles(subsidiaryId, files) {
    this.fileCache.set(subsidiaryId, files);
    this.cacheExpiry.set(subsidiaryId, Date.now() + this.CACHE_DURATION);
  }

  /**
   * Clear cache for a subsidiary
   */
  clearCache(subsidiaryId) {
    this.fileCache.delete(subsidiaryId);
    this.cacheExpiry.delete(subsidiaryId);
    console.log(`Cleared cache for subsidiary ${subsidiaryId}`);
  }

  /**
   * Force refresh cache for a subsidiary
   */
  async syncFiles(subsidiaryId) {
    try {
      console.log(`Force syncing files for subsidiary ${subsidiaryId}...`);

      // Get subsidiary-specific S3 instance and config
      const s3Instance = await multiTenantAwsService.getS3Instance(subsidiaryId);
      const awsConfig = await multiTenantAwsService.getAwsConfig(subsidiaryId);

      // Recursively list all objects from all folders/subdirectories
      const allObjects = await this.listAllS3Objects(s3Instance, awsConfig.bucketName);

      // Update cache
      this.setCachedFiles(subsidiaryId, allObjects);

      console.log(`Synced ${allObjects?.length || 0} objects for subsidiary ${subsidiaryId}`);

      return {
        success: true,
        totalFiles: allObjects?.length || 0,
        message: `Successfully synced ${allObjects?.length || 0} files`
      };
    } catch (error) {
      console.error(`Error syncing files for subsidiary ${subsidiaryId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Initialize service and verify S3 connection
   */
  async initialize() {
    const connectionTest = await testS3Connection();
    if (!connectionTest.success) {
      console.warn('FileService initialized without S3 connection');
    } else {
      // Pre-populate cache for active subsidiaries in background
      this.prePopulateCache();
    }
    return connectionTest;
  }

  /**
   * Pre-populate cache for all active subsidiaries in background
   */
  async prePopulateCache() {
    try {
      console.log('Pre-populating file cache for active subsidiaries...');

      // Get all active subsidiaries
      const { Subsidiary } = require('../models');
      const subsidiaries = await Subsidiary.findAll({ where: { is_active: true } });

      // Pre-populate cache for each subsidiary in background (don't await)
      subsidiaries.forEach(subsidiary => {
        this.syncFiles(subsidiary.id).catch(error => {
          console.warn(`Failed to pre-populate cache for subsidiary ${subsidiary.id}:`, error.message);
        });
      });

      console.log(`Started background cache population for ${subsidiaries.length} subsidiaries`);
    } catch (error) {
      console.warn('Failed to pre-populate cache:', error.message);
    }
  }

  /**
   * Upload file to S3
   */
  async uploadFile(file, userId, metadata = {}) {
    try {
      // Verify S3 connection first
      const connectionTest = await testS3Connection();
      if (!connectionTest.success) {
        throw new Error('S3 service unavailable');
      }

      const s3Key = generateS3Key(userId, file.originalname);

      const uploadParams = {
        Bucket: S3_CONFIG.bucketName,
        Key: s3Key,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          originalName: file.originalname,
          uploadedBy: userId.toString(),
          uploadDate: new Date().toISOString(),
          ...metadata
        },
        ServerSideEncryption: 'AES256'
      };

      const result = await s3.upload(uploadParams).promise();

      return {
        key: result.Key,
        location: result.Location,
        bucket: result.Bucket,
        size: file.size,
        originalName: file.originalname,
        contentType: file.mimetype,
        uploadedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }
  /**
   * List audio files from S3 bucket with advanced filtering (multi-tenant)
   */
  async listFiles(subsidiaryId, {
    page = 1,
    limit = 20,
    search = '',
    sortBy = 'name',
    sortOrder = 'asc',
    fileTypes = [],
    sizeRange = { min: null, max: null },
    dateRange = { start: null, end: null },
    duration = { min: null, max: null }
  }) {
    try {
      // Get AWS config for this subsidiary
      const awsConfig = await multiTenantAwsService.getAwsConfig(subsidiaryId);

      // Check cache first to avoid expensive S3 scanning
      let allObjects = this.getCachedFiles(subsidiaryId);

      if (!allObjects) {
        console.log(`Cache miss for subsidiary ${subsidiaryId}, starting background sync...`);

        // Start background sync but don't wait for it
        this.syncFiles(subsidiaryId).catch(error => {
          console.error(`Background sync failed for subsidiary ${subsidiaryId}:`, error.message);
        });

        // Return empty result immediately with loading indicator
        return {
          files: [],
          pagination: {
            currentPage: page,
            totalPages: 0,
            totalFiles: 0,
            hasNextPage: false,
            hasPreviousPage: false
          },
          filters: {
            search,
            sortBy,
            sortOrder,
            fileTypes,
            sizeRange,
            dateRange,
            duration
          },
          loading: true,
          message: 'Loading files from S3... This may take a moment for the first request.'
        };
      } else {
        console.log(`Cache hit for subsidiary ${subsidiaryId}, using cached data (${allObjects?.length || 0} objects)`);
      }

      // Debug logging
      console.log(`S3 Bucket Contents for subsidiary ${subsidiaryId}:`, {
        bucket: awsConfig.bucketName,
        totalObjects: allObjects?.length || 0,
        objects: allObjects?.map(f => ({ key: f.Key, size: f.Size, modified: f.LastModified })) || []
      });

      // Filter audio files only
      let audioFiles = allObjects.filter(file => {
        const ext = path.extname(file.Key).toLowerCase().substring(1);
        return S3_CONFIG.allowedAudioFormats.includes(ext);
      });

      // Apply advanced filtering
      audioFiles = this.applyAdvancedFilters(audioFiles, {
        search,
        fileTypes,
        sizeRange,
        dateRange,
        duration
      });

      // Sort files
      audioFiles.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
          case 'size':
            aValue = a.Size;
            bValue = b.Size;
            break;
          case 'lastModified':
            aValue = new Date(a.LastModified);
            bValue = new Date(b.LastModified);
            break;
          case 'name':
          default:
            aValue = a.Key.toLowerCase();
            bValue = b.Key.toLowerCase();
            break;
        }

        if (sortOrder === 'desc') {
          return aValue < bValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedFiles = audioFiles.slice(startIndex, endIndex);

      // Format response with metadata
      const files = await Promise.all(paginatedFiles.map(async file => {
        const metadata = await metadataService.getFileMetadata(file);

        return {
          key: file.Key,
          name: path.basename(file.Key),
          size: file.Size,
          lastModified: file.LastModified,
          extension: path.extname(file.Key).toLowerCase().substring(1),
          // Enhanced metadata
          metadata: {
            duration: metadata.estimatedDuration,
            formattedDuration: metadata.formattedDuration,
            bitrate: metadata.estimatedBitrate,
            formattedBitrate: metadata.formattedBitrate,
            format: metadata.format,
            sizeCategory: metadata.sizeCategory,
            formattedSize: metadata.formattedSize,
            qualityScore: metadata.qualityScore,
            isHighQuality: metadata.isHighQuality,
            isLossless: metadata.isLossless
          }
        };
      }));

      return {
        files,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(audioFiles.length / limit),
          totalItems: audioFiles.length,
          itemsPerPage: limit,
          hasNextPage: endIndex < audioFiles.length,
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error listing files:', error);
      throw error;
    }
  }

  /**
   * Generate signed URL for file download with tracking (subsidiary-specific)
   */
  async generateSubsidiaryDownloadUrl(subsidiaryId, fileKey, userId = null, downloadType = 'single', userAgent = '', ipAddress = '') {
    try {
      // Check rate limit if user is provided
      if (userId) {
        const rateLimit = await downloadTrackingService.checkRateLimit(
          userId,
          S3_CONFIG.rateLimits.downloadsPerHour
        );

        if (!rateLimit.allowed) {
          const error = new Error('Rate limit exceeded');
          error.statusCode = 429;
          error.details = {
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime
          };
          throw error;
        }
      }

      // Determine expiration time based on download type
      let expires;
      switch (downloadType) {
        case 'bulk':
          expires = S3_CONFIG.signedUrlExpires.bulk;
          break;
        case 'stream':
          expires = S3_CONFIG.signedUrlExpires.stream;
          break;
        default:
          expires = S3_CONFIG.signedUrlExpires.download;
      }

      // Prepare response headers - only force download for non-stream requests
      let url;
      if (downloadType === 'stream') {
        // For streaming, don't add attachment headers so browser can play the file
        url = await multiTenantAwsService.generateSignedUrl(subsidiaryId, fileKey, expires);
      } else {
        // For downloads, add attachment headers to force download
        const fileName = path.basename(fileKey);
        const responseHeaders = {
          contentDisposition: `attachment; filename="${fileName}"`,
          contentType: 'application/octet-stream'
        };
        url = await multiTenantAwsService.generateSignedUrl(subsidiaryId, fileKey, expires, { responseHeaders });
      }

      // Track the download if user is provided
      if (userId) {
        await downloadTrackingService.trackDownload(
          userId,
          fileKey,
          downloadType,
          userAgent,
          ipAddress
        );
      }

      console.log(`Generated subsidiary-specific download URL for: ${fileKey} (subsidiary: ${subsidiaryId}, expires in ${expires}s)`);
      return {
        url,
        expires: new Date(Date.now() + expires * 1000).toISOString(),
        downloadType,
        fileKey
      };
    } catch (error) {
      console.error('Error generating subsidiary download URL:', error);
      throw error;
    }
  }

  /**
   * Generate signed URL for file download with tracking (legacy - global bucket)
   */
  async generateDownloadUrl(fileKey, userId = null, downloadType = 'single', userAgent = '', ipAddress = '') {
    try {
      // Check rate limit if user is provided
      if (userId) {
        const rateLimit = await downloadTrackingService.checkRateLimit(
          userId,
          S3_CONFIG.rateLimits.downloadsPerHour
        );

        if (!rateLimit.allowed) {
          const error = new Error('Rate limit exceeded');
          error.statusCode = 429;
          error.details = {
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime
          };
          throw error;
        }
      }

      // Determine expiration time based on download type
      let expires;
      switch (downloadType) {
        case 'bulk':
          expires = S3_CONFIG.signedUrlExpires.bulk;
          break;
        case 'stream':
          expires = S3_CONFIG.signedUrlExpires.stream;
          break;
        default:
          expires = S3_CONFIG.signedUrlExpires.download;
      }

      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey,
        Expires: expires,
        ResponseContentDisposition: `attachment; filename="${path.basename(fileKey)}"`
      };

      const url = await s3.getSignedUrlPromise('getObject', params);

      // Track the download if user is provided
      if (userId) {
        await downloadTrackingService.trackDownload(
          userId,
          fileKey,
          downloadType,
          userAgent,
          ipAddress
        );
      }

      console.log(`Generated ${downloadType} download URL for: ${fileKey} (expires in ${expires}s)`);
      return {
        url,
        expires: new Date(Date.now() + expires * 1000).toISOString(),
        downloadType,
        fileKey
      };
    } catch (error) {
      console.error('Error generating download URL:', error);
      throw error;
    }
  }

  /**
   * Configure S3 bucket CORS for audio streaming
   */
  async ensureBucketCORS() {
    try {
      const corsConfiguration = {
        CORSRules: [
          {
            AllowedHeaders: ['*'],
            AllowedMethods: ['GET', 'HEAD'],
            AllowedOrigins: ['*'],
            ExposeHeaders: ['ETag', 'Content-Length', 'Content-Type'],
            MaxAgeSeconds: 3600
          }
        ]
      };

      await s3.putBucketCors({
        Bucket: S3_CONFIG.bucketName,
        CORSConfiguration: corsConfiguration
      }).promise();

      console.log('S3 bucket CORS configured successfully');
    } catch (error) {
      console.error('Error configuring S3 bucket CORS:', error);
      // Don't throw error - continue even if CORS config fails
    }
  }

  /**
   * Stream audio file directly through backend (avoids CORS issues) - updated
   */
  async streamFile(fileKey, res, subsidiaryId) {
    try {
      // Get AWS config and S3 instance for this subsidiary
      const awsConfig = await multiTenantAwsService.getAwsConfig(subsidiaryId);
      const s3Client = await multiTenantAwsService.getS3Instance(subsidiaryId);

      const params = {
        Bucket: awsConfig.bucketName,
        Key: fileKey
      };

      console.log('Streaming file:', fileKey);

      // First, get the object metadata to set proper headers
      const headParams = { ...params };
      const metadata = await s3Client.headObject(headParams).promise();

      console.log('File metadata:', {
        ContentType: metadata.ContentType,
        ContentLength: metadata.ContentLength,
        LastModified: metadata.LastModified
      });

      // Determine correct content type for audio files
      const getAudioContentType = (fileKey, s3ContentType) => {
        const extension = fileKey.toLowerCase().split('.').pop();
        const audioTypes = {
          'wav': 'audio/wav',
          'mp3': 'audio/mpeg',
          'flac': 'audio/flac',
          'aac': 'audio/aac',
          'ogg': 'audio/ogg',
          'm4a': 'audio/mp4',
          'wma': 'audio/x-ms-wma'
        };
        return audioTypes[extension] || s3ContentType || 'audio/wav';
      };

      const contentType = getAudioContentType(fileKey, metadata.ContentType);

      // Set appropriate headers for audio streaming
      res.set({
        'Content-Type': contentType,
        'Content-Length': metadata.ContentLength,
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Range, Content-Range, Content-Length'
      });

      // Handle range requests for audio seeking
      const range = res.req.headers.range;
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : metadata.ContentLength - 1;

        params.Range = `bytes=${start}-${end}`;
        res.status(206); // Partial Content
        res.set('Content-Range', `bytes ${start}-${end}/${metadata.ContentLength}`);
        res.set('Content-Length', end - start + 1);
      }

      // Get the S3 object stream
      const stream = s3Client.getObject(params).createReadStream();

      // Handle stream errors
      stream.on('error', (error) => {
        console.error('Error streaming file:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Failed to stream file' });
        }
      });

      // Log when streaming starts
      stream.on('open', () => {
        console.log('Started streaming file:', fileKey);
      });

      // Handle stream end
      stream.on('end', () => {
        console.log('Finished streaming file:', fileKey);
      });

      // Pipe the stream to the response
      stream.pipe(res);
    } catch (error) {
      console.error('Error setting up stream:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Failed to stream file' });
      }
    }
  }

  /**
   * Generate signed URL for audio streaming (fallback method)
   */
  async generateStreamUrl(fileKey, userId = null, userAgent = '', ipAddress = '') {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey,
        Expires: S3_CONFIG.signedUrlExpires.stream
      };

      const url = await s3.getSignedUrlPromise('getObject', params);

      // Track streaming access if user is provided
      if (userId) {
        await downloadTrackingService.trackDownload(
          userId,
          fileKey,
          'stream',
          userAgent,
          ipAddress
        );
      }

      console.log(`🎵 Generated stream URL for: ${fileKey} (expires in ${S3_CONFIG.signedUrlExpires.stream}s)`);
      return {
        url,
        expires: new Date(Date.now() + S3_CONFIG.signedUrlExpires.stream * 1000).toISOString(),
        downloadType: 'stream',
        fileKey
      };
    } catch (error) {
      console.error('Error generating stream URL:', error);
      throw error;
    }
  }

  /**
   * Get file stream from S3 for proxying through backend
   */
  async getFileStream(fileKey) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey
      };

      const stream = s3.getObject(params).createReadStream();
      return stream;
    } catch (error) {
      console.error('Error getting file stream:', error);
      throw error;
    }
  }

  /**
   * Get file metadata from S3
   */
  async getFileMetadata(fileKey) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey
      };

      const metadata = await s3.headObject(params).promise();
      return metadata;
    } catch (error) {
      console.error('Error getting file metadata:', error);
      throw error;
    }
  }

  /**
   * Ensure S3 bucket has proper CORS configuration for audio streaming
   */
  async ensureBucketCORS() {
    try {
      const corsParams = {
        Bucket: S3_CONFIG.bucketName,
        CORSConfiguration: {
          CORSRules: [
            {
              AllowedHeaders: ['*'],
              AllowedMethods: ['GET', 'HEAD'],
              AllowedOrigins: ['*'],
              ExposeHeaders: ['Content-Length', 'Content-Type', 'Accept-Ranges'],
              MaxAgeSeconds: 3600
            }
          ]
        }
      };

      await s3.putBucketCors(corsParams).promise();
      console.log('S3 bucket CORS configuration updated');
    } catch (error) {
      console.warn('Could not update S3 bucket CORS configuration:', error.message);
      // Don't throw error as this is not critical for functionality
    }
  }

  /**
   * Generate signed URLs for multiple files with enhanced tracking (subsidiary-specific)
   */
  async generateSubsidiaryBulkDownloadUrls(subsidiaryId, fileKeys, userId = null, userAgent = '', ipAddress = '') {
    try {
      // Check bulk download rate limit
      if (userId) {
        const rateLimit = await downloadTrackingService.checkRateLimit(
          userId,
          S3_CONFIG.rateLimits.bulkDownloadsPerHour
        );

        if (!rateLimit.allowed) {
          const error = new Error('Bulk download rate limit exceeded');
          error.statusCode = 429;
          error.details = {
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime
          };
          throw error;
        }
      }

      const downloadPromises = fileKeys.map(async (key) => {
        try {
          const result = await this.generateSubsidiaryDownloadUrl(subsidiaryId, key, userId, 'bulk', userAgent, ipAddress);
          return {
            key,
            url: result.url,
            expires: result.expires,
            success: true
          };
        } catch (error) {
          return {
            key,
            success: false,
            error: error.message
          };
        }
      });

      const results = await Promise.all(downloadPromises);

      console.log(`Generated ${results.filter(r => r.success).length}/${results.length} bulk download URLs for subsidiary ${subsidiaryId}`);
      return results;
    } catch (error) {
      console.error('Error generating subsidiary bulk download URLs:', error);
      throw error;
    }
  }

  /**
   * Generate signed URLs for multiple files with enhanced tracking (legacy - global bucket)
   */
  async generateBulkDownloadUrls(fileKeys, userId = null, userAgent = '', ipAddress = '') {
    try {
      // Check bulk download rate limit
      if (userId) {
        const rateLimit = await downloadTrackingService.checkRateLimit(
          userId,
          S3_CONFIG.rateLimits.bulkDownloadsPerHour
        );

        if (!rateLimit.allowed) {
          const error = new Error('Bulk download rate limit exceeded');
          error.statusCode = 429;
          error.details = {
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime
          };
          throw error;
        }
      }

      const downloadPromises = fileKeys.map(async (key) => {
        try {
          const result = await this.generateDownloadUrl(key, userId, 'bulk', userAgent, ipAddress);
          return {
            key,
            url: result.url,
            expires: result.expires,
            success: true
          };
        } catch (error) {
          return {
            key,
            error: error.message,
            success: false
          };
        }
      });

      const results = await Promise.all(downloadPromises);

      console.log(`Generated bulk download URLs for ${results.filter(r => r.success).length}/${fileKeys.length} files`);
      return results;
    } catch (error) {
      console.error('Error generating bulk download URLs:', error);
      throw error;
    }
  }

  /**
   * Create and stream a zip archive of multiple files (subsidiary-specific)
   */
  async createSubsidiaryZipArchive(subsidiaryId, fileKeys, responseStream) {
    return new Promise(async (resolve, reject) => {
      let isFinalized = false;

      const archive = archiver('zip', {
        zlib: { level: 6 } // Balanced compression for better performance
      });

      // Handle archive errors
      archive.on('error', (err) => {
        console.error('Archive error:', err);
        if (!isFinalized) {
          isFinalized = true;
          reject(err);
        }
      });

      // Handle archive warnings
      archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
          console.warn('Archive warning:', err);
        } else {
          console.error('Archive warning (treating as error):', err);
          if (!isFinalized) {
            isFinalized = true;
            reject(err);
          }
        }
      });

      // Handle successful completion
      archive.on('end', () => {
        console.log('Archive stream ended successfully');
        if (!isFinalized) {
          isFinalized = true;
          resolve();
        }
      });

      // Handle response stream errors
      responseStream.on('error', (err) => {
        console.error('Response stream error:', err);
        if (!isFinalized) {
          isFinalized = true;
          reject(err);
        }
      });

      // Pipe archive data to the response
      archive.pipe(responseStream);

      try {
        console.log(`Starting ZIP creation for ${fileKeys.length} files (subsidiary: ${subsidiaryId})`);

        // Get AWS config and S3 instance for this subsidiary
        const awsConfig = await multiTenantAwsService.getAwsConfig(subsidiaryId);
        const s3Client = await multiTenantAwsService.getS3Instance(subsidiaryId);

        // Add files to archive sequentially to avoid overwhelming S3
        for (let i = 0; i < fileKeys.length; i++) {
          const fileKey = fileKeys[i];
          try {
            console.log(`Adding file ${i + 1}/${fileKeys.length} to archive: ${path.basename(fileKey)}`);

            // Check if file exists first
            const headParams = {
              Bucket: awsConfig.bucketName,
              Key: fileKey
            };

            await s3Client.headObject(headParams).promise();

            // Get file stream from S3
            const getParams = {
              Bucket: awsConfig.bucketName,
              Key: fileKey
            };

            const s3Stream = s3Client.getObject(getParams).createReadStream();
            const fileName = path.basename(fileKey);

            // Handle S3 stream errors
            s3Stream.on('error', (streamError) => {
              console.error(`S3 stream error for ${fileKey}:`, streamError);
              // Continue with other files instead of failing the entire archive
            });

            // Add file to archive
            archive.append(s3Stream, { name: fileName });

          } catch (fileError) {
            console.error(`Error adding file ${fileKey} to archive:`, fileError);
            // Continue with other files instead of failing the entire archive
          }
        }

        // Finalize the archive
        console.log('Finalizing archive...');
        archive.finalize();

      } catch (error) {
        console.error('Error creating archive:', error);
        if (!isFinalized) {
          isFinalized = true;
          reject(error);
        }
      }
    });
  }

  /**
   * Create and stream a zip archive of multiple files (legacy - global bucket)
   */
  async createZipArchive(fileKeys, responseStream) {
    return new Promise(async (resolve, reject) => {
      let isFinalized = false;

      const archive = archiver('zip', {
        zlib: { level: 6 } // Balanced compression for better performance
      });

      // Handle archive errors
      archive.on('error', (err) => {
        console.error('Archive error:', err);
        if (!isFinalized) {
          isFinalized = true;
          reject(err);
        }
      });

      // Handle archive warnings
      archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
          console.warn('Archive warning:', err);
        } else {
          console.error('Archive warning (treating as error):', err);
          if (!isFinalized) {
            isFinalized = true;
            reject(err);
          }
        }
      });

      // Handle successful completion
      archive.on('end', () => {
        console.log('Archive stream ended successfully');
        if (!isFinalized) {
          isFinalized = true;
          resolve();
        }
      });

      // Handle response stream errors
      responseStream.on('error', (err) => {
        console.error('Response stream error:', err);
        if (!isFinalized) {
          isFinalized = true;
          reject(err);
        }
      });

      // Pipe archive data to the response
      archive.pipe(responseStream);

      try {
        console.log(`Starting ZIP creation for ${fileKeys.length} files`);

        // Add files to archive sequentially to avoid overwhelming S3
        for (let i = 0; i < fileKeys.length; i++) {
          const fileKey = fileKeys[i];
          try {
            console.log(`Adding file ${i + 1}/${fileKeys.length} to archive: ${path.basename(fileKey)}`);

            // Check if file exists first
            const headParams = {
              Bucket: S3_CONFIG.bucketName,
              Key: fileKey
            };

            await s3.headObject(headParams).promise();

            // Get file stream from S3
            const getParams = {
              Bucket: S3_CONFIG.bucketName,
              Key: fileKey
            };

            const s3Stream = s3.getObject(getParams).createReadStream();
            const fileName = path.basename(fileKey);

            // Handle S3 stream errors
            s3Stream.on('error', (streamError) => {
              console.error(`S3 stream error for ${fileKey}:`, streamError);
              // Continue with other files instead of failing the entire archive
            });

            // Add file to archive
            archive.append(s3Stream, { name: fileName });

          } catch (fileError) {
            console.error(`Error adding file ${fileKey} to archive:`, fileError);
            // Continue with other files instead of failing the entire archive
          }
        }

        // Finalize the archive
        console.log('Finalizing archive...');
        archive.finalize();

      } catch (error) {
        console.error('Error creating archive:', error);
        if (!isFinalized) {
          isFinalized = true;
          reject(error);
        }
      }
    });
  }

  /**
   * Check if file exists in S3
   */
  async fileExists(fileKey) {
    try {
      await s3.headObject({
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey
      }).promise();
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Get dashboard statistics
   */
  async getStats() {
    try {
      // Verify S3 connection first
      const connectionTest = await testS3Connection();
      if (!connectionTest.success) {
        throw new Error('S3 service unavailable');
      }

      const params = {
        Bucket: S3_CONFIG.bucketName,
        MaxKeys: 1000 // Get up to 1000 files for stats calculation
      };

      const data = await s3.listObjectsV2(params).promise();

      // Calculate statistics
      const totalFiles = data.KeyCount || 0;
      const totalSize = data.Contents?.reduce((sum, file) => sum + (file.Size || 0), 0) || 0;

      // Get file type breakdown
      const fileTypes = {};
      data.Contents?.forEach(file => {
        const ext = path.extname(file.Key).toLowerCase().substring(1);
        fileTypes[ext] = (fileTypes[ext] || 0) + 1;
      });

      // Find most recent file
      const lastUpdated = data.Contents?.length > 0
        ? Math.max(...data.Contents.map(file => new Date(file.LastModified).getTime()))
        : Date.now();

      return {
        totalFiles,
        totalSize,
        lastUpdated: new Date(lastUpdated),
        fileTypes,
        activeUsers: 1 // For now, just return 1 as we don't track active sessions
      };
    } catch (error) {
      console.error('Error getting stats:', error);
      throw error;
    }
  }

  /**
   * Get recently accessed/modified files
   */
  async getRecentFiles(limit = 10) {
    try {
      // Verify S3 connection first
      const connectionTest = await testS3Connection();
      if (!connectionTest.success) {
        throw new Error('S3 service unavailable');
      }

      const params = {
        Bucket: S3_CONFIG.bucketName,
        MaxKeys: 100 // Get more files to sort and limit
      };

      const data = await s3.listObjectsV2(params).promise();

      if (!data.Contents || data.Contents.length === 0) {
        return [];
      }

      // Sort by LastModified date (most recent first) and limit
      const recentFiles = data.Contents
        .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
        .slice(0, limit)
        .map(file => {
          const fileName = path.basename(file.Key);
          const fileExt = path.extname(fileName).toLowerCase();

          return {
            id: file.Key,
            name: fileName,
            key: file.Key,
            size: this.formatBytes(file.Size),
            sizeBytes: file.Size,
            lastModified: file.LastModified,
            lastAccessed: this.getRelativeTime(file.LastModified),
            type: fileExt.substring(1) || 'unknown',
            duration: this.estimateDuration(file.Size, fileExt) // Rough estimate
          };
        });

      return recentFiles;
    } catch (error) {
      console.error('Error getting recent files:', error);
      throw error;
    }
  }

  /**
   * Helper method to format bytes
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Helper method to get relative time
   */
  getRelativeTime(date) {
    const now = new Date();
    const diffMs = now - new Date(date);
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  }

  /**
   * Helper method to estimate audio duration based on file size and type
   */
  estimateDuration(sizeBytes, fileExt) {
    // Rough estimates based on common bitrates
    const bitrates = {
      '.mp3': 128, // kbps
      '.wav': 1411, // kbps (CD quality)
      '.flac': 800, // kbps (average)
      '.aac': 128, // kbps
      '.m4a': 128, // kbps
      '.ogg': 128  // kbps
    };

    const bitrate = bitrates[fileExt] || 128;
    const durationSeconds = (sizeBytes * 8) / (bitrate * 1000);

    const minutes = Math.floor(durationSeconds / 60);
    const seconds = Math.floor(durationSeconds % 60);

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Get download statistics for a user
   */
  async getDownloadStats(userId, days = 30) {
    try {
      return await downloadTrackingService.getUserDownloadStats(userId, days);
    } catch (error) {
      console.error('Error getting download stats:', error);
      throw error;
    }
  }

  /**
   * Clean up old download tracking records
   */
  async cleanupDownloadRecords(daysToKeep = 90) {
    try {
      return await downloadTrackingService.cleanupOldRecords(daysToKeep);
    } catch (error) {
      console.error('Error cleaning up download records:', error);
      throw error;
    }
  }

  /**
   * Apply advanced filters to file list
   */
  applyAdvancedFilters(files, filters) {
    let filteredFiles = [...files];

    // Search filter - enhanced to search in multiple fields
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredFiles = filteredFiles.filter(file => {
        const fileName = path.basename(file.Key).toLowerCase();
        const fileKey = file.Key.toLowerCase();
        const extension = path.extname(file.Key).toLowerCase().substring(1);

        return fileName.includes(searchLower) ||
               fileKey.includes(searchLower) ||
               extension.includes(searchLower);
      });
    }

    // File type filter
    if (filters.fileTypes && filters.fileTypes.length > 0) {
      filteredFiles = filteredFiles.filter(file => {
        const extension = path.extname(file.Key).toLowerCase().substring(1);
        return filters.fileTypes.includes(extension);
      });
    }

    // Size range filter
    if (filters.sizeRange && (filters.sizeRange.min !== null || filters.sizeRange.max !== null)) {
      filteredFiles = filteredFiles.filter(file => {
        const size = file.Size;
        const minSize = filters.sizeRange.min;
        const maxSize = filters.sizeRange.max;

        if (minSize !== null && size < minSize) return false;
        if (maxSize !== null && size > maxSize) return false;
        return true;
      });
    }

    // Date range filter
    if (filters.dateRange && (filters.dateRange.start || filters.dateRange.end)) {
      filteredFiles = filteredFiles.filter(file => {
        const fileDate = new Date(file.LastModified);
        const startDate = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
        const endDate = filters.dateRange.end ? new Date(filters.dateRange.end) : null;

        if (startDate && fileDate < startDate) return false;
        if (endDate && fileDate > endDate) return false;
        return true;
      });
    }

    console.log(`Advanced filtering: ${files.length} → ${filteredFiles.length} files`);
    return filteredFiles;
  }

  /**
   * Get search suggestions based on existing files
   */
  async getSearchSuggestions(query = '', limit = 10) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        MaxKeys: 1000
      };

      const data = await s3.listObjectsV2(params).promise();

      // Filter audio files only
      let audioFiles = data.Contents.filter(file => {
        const ext = path.extname(file.Key).toLowerCase().substring(1);
        return S3_CONFIG.allowedAudioFormats.includes(ext);
      });

      const suggestions = new Set();
      const queryLower = query.toLowerCase();

      audioFiles.forEach(file => {
        const fileName = path.basename(file.Key);
        const extension = path.extname(file.Key).toLowerCase().substring(1);

        // Add filename suggestions
        if (fileName.toLowerCase().includes(queryLower)) {
          suggestions.add(fileName);
        }

        // Add extension suggestions
        if (extension.includes(queryLower)) {
          suggestions.add(extension);
        }

        // Add partial word suggestions
        const words = fileName.toLowerCase().split(/[\s\-_\.]+/);
        words.forEach(word => {
          if (word.includes(queryLower) && word.length > 2) {
            suggestions.add(word);
          }
        });
      });

      return Array.from(suggestions)
        .slice(0, limit)
        .sort((a, b) => a.localeCompare(b));
    } catch (error) {
      console.error('Error getting search suggestions:', error);
      return [];
    }
  }

  /**
   * Get comprehensive file statistics with metadata
   */
  async getFileStatistics() {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        MaxKeys: 1000
      };

      const data = await s3.listObjectsV2(params).promise();

      // Filter audio files only
      let audioFiles = data.Contents.filter(file => {
        const ext = path.extname(file.Key).toLowerCase().substring(1);
        return S3_CONFIG.allowedAudioFormats.includes(ext);
      });

      // Get comprehensive metadata statistics
      const metadataStats = await metadataService.getMetadataStatistics(audioFiles);

      // Get recent files with metadata
      const recentFiles = await Promise.all(
        audioFiles
          .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
          .slice(0, 10)
          .map(async file => {
            const metadata = await metadataService.getFileMetadata(file);
            return {
              key: file.Key,
              name: path.basename(file.Key),
              size: file.Size,
              lastModified: file.LastModified,
              extension: path.extname(file.Key).toLowerCase().substring(1),
              metadata: {
                duration: metadata.estimatedDuration,
                formattedDuration: metadata.formattedDuration,
                bitrate: metadata.estimatedBitrate,
                formattedBitrate: metadata.formattedBitrate,
                format: metadata.format,
                formattedSize: metadata.formattedSize,
                qualityScore: metadata.qualityScore,
                isHighQuality: metadata.isHighQuality,
                isLossless: metadata.isLossless
              }
            };
          })
      );

      return {
        ...metadataStats,
        recentFiles,
        // Additional computed statistics
        averageQualityScore: Math.round(
          audioFiles.reduce(async (sum, file) => {
            const metadata = await metadataService.getFileMetadata(file);
            return (await sum) + metadata.qualityScore;
          }, 0) / audioFiles.length
        ),
        formatPopularity: Object.entries(metadataStats.formats)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5)
          .map(([format, count]) => ({ format, count, percentage: Math.round((count / audioFiles.length) * 100) }))
      };
    } catch (error) {
      console.error('Error getting file statistics:', error);
      throw error;
    }
  }

  /**
   * Get detailed metadata for a specific file (multi-tenant)
   */
  async getFileMetadata(fileKey, subsidiaryId) {
    try {
      // Use multi-tenant AWS service to get file metadata
      const awsConfig = await multiTenantAwsService.getAwsConfig(subsidiaryId);
      const s3Client = await multiTenantAwsService.getS3Instance(subsidiaryId);

      const params = {
        Bucket: awsConfig.bucketName,
        Key: fileKey
      };

      // Check if file exists
      const headResult = await s3Client.headObject(params).promise();

      // Create S3 object structure for metadata service
      const s3Object = {
        Key: fileKey,
        Size: headResult.ContentLength,
        LastModified: headResult.LastModified
      };

      // Get comprehensive metadata
      const metadata = await metadataService.getFileMetadata(s3Object);

      return {
        ...metadata,
        // Additional file information
        contentType: headResult.ContentType,
        etag: headResult.ETag,
        storageClass: headResult.StorageClass || 'STANDARD',
        // S3 specific metadata
        s3Metadata: headResult.Metadata || {}
      };
    } catch (error) {
      if (error.code === 'NotFound' || error.code === 'NoSuchKey') {
        return null;
      }
      console.error('Error getting file metadata:', error);
      throw error;
    }
  }

  /**
   * Recursively list all objects in S3 bucket (including subdirectories)
   * This is essential for multi-tenant setups where files may be organized in folders
   */
  async listAllS3Objects(s3Instance, bucketName, prefix = '', allObjects = []) {
    try {
      console.log(`🔍 Using AWS SDK to list S3 objects with prefix: "${prefix}" in bucket: ${bucketName}`);

      // Use AWS SDK directly (more reliable with database credentials)
      const params = {
        Bucket: bucketName,
        Prefix: prefix,
        MaxKeys: 1000
      };

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('S3 SDK operation timed out after 30 seconds')), 30000);
      });

      const data = await Promise.race([
        s3Instance.listObjectsV2(params).promise(),
        timeoutPromise
      ]);

      console.log(`SDK found ${data.Contents ? data.Contents.length : 0} objects (first 1000 only)`);
      return data.Contents || [];

    } catch (error) {
      console.error('AWS SDK failed:', error);
      throw new Error(`Failed to list S3 objects: ${error.message}`);
    }
  }
}

module.exports = new FileService();
