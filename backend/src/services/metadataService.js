const { s3, S3_CONFIG } = require('../config/aws');
const path = require('path');

class MetadataService {
  constructor() {
    this.metadataCache = new Map();
  }

  /**
   * Extract basic metadata from file information
   */
  extractBasicMetadata(s3Object) {
    const fileName = path.basename(s3Object.Key);
    const extension = path.extname(s3Object.Key).toLowerCase().substring(1);
    
    return {
      fileName,
      extension,
      size: s3Object.Size,
      lastModified: s3Object.LastModified,
      // Estimated duration based on file size and format (rough approximation)
      estimatedDuration: this.estimateDuration(s3Object.Size, extension),
      // Estimated bitrate based on file size and format
      estimatedBitrate: this.estimateBitrate(s3Object.Size, extension),
      // Audio format details
      format: this.getFormatDetails(extension),
      // File size categories
      sizeCategory: this.getSizeCategory(s3Object.Size)
    };
  }

  /**
   * Estimate audio duration based on file size and format
   */
  estimateDuration(sizeBytes, extension) {
    // Rough estimates based on typical bitrates for different formats
    const typicalBitrates = {
      'mp3': 128, // kbps
      'wav': 1411, // kbps (CD quality)
      'flac': 800, // kbps (lossless compressed)
      'ogg': 128, // kbps
      'm4a': 128, // kbps
      'aac': 128, // kbps
      'wma': 128  // kbps
    };

    const bitrate = typicalBitrates[extension] || 128;
    const sizeKb = sizeBytes / 1024;
    const durationSeconds = (sizeKb * 8) / bitrate;
    
    return Math.round(durationSeconds);
  }

  /**
   * Estimate bitrate based on file size and format
   */
  estimateBitrate(sizeBytes, extension) {
    const typicalBitrates = {
      'mp3': 128,
      'wav': 1411,
      'flac': 800,
      'ogg': 128,
      'm4a': 128,
      'aac': 128,
      'wma': 128
    };

    return typicalBitrates[extension] || 128;
  }

  /**
   * Get detailed format information
   */
  getFormatDetails(extension) {
    const formatDetails = {
      'mp3': {
        name: 'MP3',
        fullName: 'MPEG Audio Layer III',
        type: 'lossy',
        quality: 'good',
        compression: 'high'
      },
      'wav': {
        name: 'WAV',
        fullName: 'Waveform Audio File Format',
        type: 'lossless',
        quality: 'excellent',
        compression: 'none'
      },
      'flac': {
        name: 'FLAC',
        fullName: 'Free Lossless Audio Codec',
        type: 'lossless',
        quality: 'excellent',
        compression: 'medium'
      },
      'ogg': {
        name: 'OGG',
        fullName: 'Ogg Vorbis',
        type: 'lossy',
        quality: 'good',
        compression: 'high'
      },
      'm4a': {
        name: 'M4A',
        fullName: 'MPEG-4 Audio',
        type: 'lossy',
        quality: 'good',
        compression: 'high'
      },
      'aac': {
        name: 'AAC',
        fullName: 'Advanced Audio Coding',
        type: 'lossy',
        quality: 'good',
        compression: 'high'
      },
      'wma': {
        name: 'WMA',
        fullName: 'Windows Media Audio',
        type: 'lossy',
        quality: 'good',
        compression: 'high'
      }
    };

    return formatDetails[extension] || {
      name: extension.toUpperCase(),
      fullName: 'Unknown Audio Format',
      type: 'unknown',
      quality: 'unknown',
      compression: 'unknown'
    };
  }

  /**
   * Categorize file size
   */
  getSizeCategory(sizeBytes) {
    const sizeMB = sizeBytes / (1024 * 1024);
    
    if (sizeMB < 1) return 'tiny';
    if (sizeMB < 5) return 'small';
    if (sizeMB < 20) return 'medium';
    if (sizeMB < 50) return 'large';
    return 'huge';
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get comprehensive metadata for a file
   */
  async getFileMetadata(s3Object) {
    const cacheKey = `${s3Object.Key}_${s3Object.LastModified}`;
    
    // Check cache first
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey);
    }

    try {
      const basicMetadata = this.extractBasicMetadata(s3Object);
      
      const metadata = {
        ...basicMetadata,
        // Formatted versions for display
        formattedDuration: this.formatDuration(basicMetadata.estimatedDuration),
        formattedSize: this.formatFileSize(basicMetadata.size),
        formattedBitrate: `${basicMetadata.estimatedBitrate} kbps`,
        // Additional computed properties
        qualityScore: this.calculateQualityScore(basicMetadata),
        isHighQuality: basicMetadata.estimatedBitrate >= 320 || basicMetadata.format.type === 'lossless',
        isLossless: basicMetadata.format.type === 'lossless',
        // Metadata extraction timestamp
        extractedAt: new Date().toISOString()
      };

      // Cache the metadata
      this.metadataCache.set(cacheKey, metadata);
      
      // Limit cache size
      if (this.metadataCache.size > 1000) {
        const firstKey = this.metadataCache.keys().next().value;
        this.metadataCache.delete(firstKey);
      }

      return metadata;
    } catch (error) {
      console.error('Error extracting metadata:', error);
      return this.extractBasicMetadata(s3Object);
    }
  }

  /**
   * Calculate a quality score based on format and bitrate
   */
  calculateQualityScore(metadata) {
    let score = 0;
    
    // Base score from format type
    switch (metadata.format.type) {
      case 'lossless':
        score += 50;
        break;
      case 'lossy':
        score += 30;
        break;
      default:
        score += 10;
    }
    
    // Score from bitrate
    if (metadata.estimatedBitrate >= 320) {
      score += 30;
    } else if (metadata.estimatedBitrate >= 192) {
      score += 20;
    } else if (metadata.estimatedBitrate >= 128) {
      score += 10;
    }
    
    // Bonus for popular formats
    if (['mp3', 'flac', 'wav'].includes(metadata.extension)) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }

  /**
   * Get metadata statistics for all files
   */
  async getMetadataStatistics(files) {
    const stats = {
      totalFiles: files.length,
      totalSize: 0,
      totalDuration: 0,
      formats: {},
      qualityDistribution: {
        lossless: 0,
        highQuality: 0,
        standard: 0,
        lowQuality: 0
      },
      sizeDistribution: {
        tiny: 0,
        small: 0,
        medium: 0,
        large: 0,
        huge: 0
      },
      averageBitrate: 0,
      averageFileSize: 0
    };

    let totalBitrate = 0;

    for (const file of files) {
      const metadata = await this.getFileMetadata(file);
      
      stats.totalSize += metadata.size;
      stats.totalDuration += metadata.estimatedDuration;
      totalBitrate += metadata.estimatedBitrate;
      
      // Format distribution
      stats.formats[metadata.extension] = (stats.formats[metadata.extension] || 0) + 1;
      
      // Quality distribution
      if (metadata.isLossless) {
        stats.qualityDistribution.lossless++;
      } else if (metadata.estimatedBitrate >= 320) {
        stats.qualityDistribution.highQuality++;
      } else if (metadata.estimatedBitrate >= 128) {
        stats.qualityDistribution.standard++;
      } else {
        stats.qualityDistribution.lowQuality++;
      }
      
      // Size distribution
      stats.sizeDistribution[metadata.sizeCategory]++;
    }

    stats.averageBitrate = Math.round(totalBitrate / files.length);
    stats.averageFileSize = Math.round(stats.totalSize / files.length);
    stats.formattedTotalSize = this.formatFileSize(stats.totalSize);
    stats.formattedTotalDuration = this.formatDuration(stats.totalDuration);

    return stats;
  }

  /**
   * Clear metadata cache
   */
  clearCache() {
    this.metadataCache.clear();
  }
}

module.exports = new MetadataService();
