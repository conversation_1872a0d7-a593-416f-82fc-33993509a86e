const { User, Role, UserRole, Subsidiary, AuditLog } = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');

class UserService {
  /**
   * Get all users with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Object} Users with pagination info
   */
  async getUsers(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        subsidiaryId = null,
        roleId = null,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (search) {
        whereClause[Op.or] = [
          { username: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { first_name: { [Op.like]: `%${search}%` } },
          { last_name: { [Op.like]: `%${search}%` } }
        ];
      }

      if (subsidiaryId) {
        whereClause.subsidiary_id = subsidiaryId;
      }

      // Build include for roles filter
      const include = [
        {
          model: Subsidiary,
          as: 'subsidiary',
          attributes: ['id', 'name', 'code']
        },
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name', 'code', 'hierarchy_level'],
          through: {
            attributes: ['assigned_at', 'is_active'],
            where: { is_active: true }
          }
        }
      ];

      // Add role filter if specified
      if (roleId) {
        include[1].where = { id: roleId };
        include[1].required = true;
      }

      const { count, rows } = await User.findAndCountAll({
        where: whereClause,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true
      });

      return {
        users: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalUsers: count,
          hasNext: page * limit < count,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Error getting users:', error.message);
      throw error;
    }
  }

  /**
   * Get user by ID with roles and subsidiary
   * @param {number} userId - User ID
   * @returns {Object} User with roles and subsidiary
   */
  async getUserById(userId) {
    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: Subsidiary,
            as: 'subsidiary',
            attributes: ['id', 'name', 'code', 'timezone']
          },
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'code', 'description', 'hierarchy_level', 'permissions'],
            through: {
              attributes: ['assigned_at', 'expires_at', 'is_active'],
              where: { is_active: true }
            }
          }
        ]
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Convert to plain object to avoid circular references
      return user.toJSON();
    } catch (error) {
      console.error(`Error getting user ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @param {number} createdBy - ID of user creating this user
   * @returns {Object} Created user
   */
  async createUser(userData, createdBy) {
    try {
      const {
        username,
        email,
        password,
        firstName,
        lastName,
        phone,
        subsidiaryId,
        roleIds = [],
        generateTempPassword = false,
        forcePasswordReset: initialForcePasswordReset = false
      } = userData;

      // Generate temporary password if requested
      let finalPassword = password;
      let tempPassword = null;
      let forcePasswordReset = initialForcePasswordReset;

      if (generateTempPassword) {
        tempPassword = this.generateTempPassword();
        finalPassword = tempPassword;
        // Force password reset when using temporary password
        forcePasswordReset = true;
      }

      // Check if email already exists
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        throw new Error('Email already exists');
      }

      // Verify subsidiary exists and is active
      const subsidiary = await Subsidiary.findByPk(subsidiaryId);
      if (!subsidiary || !subsidiary.is_active) {
        throw new Error('Invalid or inactive subsidiary');
      }

      // Create user
      const user = await User.create({
        username,
        email,
        password_hash: finalPassword, // Will be hashed by model hook
        first_name: firstName,
        last_name: lastName,
        phone,
        subsidiary_id: subsidiaryId,
        password_changed_at: forcePasswordReset ? null : new Date()
      });

      // Assign roles if provided
      if (roleIds.length > 0) {
        await this.assignRoles(user.id, roleIds, createdBy);
      }

      // Log the action
      await AuditLog.logUserAction(
        createdBy,
        subsidiaryId,
        'CREATE_USER',
        'user',
        user.id.toString(),
        { username, email, roleIds }
      );

      // Return user with roles and temp password if generated
      const createdUser = await this.getUserById(user.id);

      if (tempPassword) {
        return {
          ...createdUser,
          tempPassword: tempPassword
        };
      }

      return createdUser;
    } catch (error) {
      console.error('Error creating user:', error.message);
      throw error;
    }
  }

  /**
   * Generate a secure temporary password
   * @returns {string} Generated password
   */
  generateTempPassword() {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    const specialChars = '!@#$%&*';
    let password = '';

    // Ensure at least one of each type
    password += chars.charAt(Math.floor(Math.random() * 26)); // uppercase
    password += chars.charAt(Math.floor(Math.random() * 26) + 26); // lowercase
    password += '23456789'.charAt(Math.floor(Math.random() * 8)); // number
    password += specialChars.charAt(Math.floor(Math.random() * specialChars.length)); // special

    // Fill the rest randomly
    for (let i = 4; i < 12; i++) {
      const allChars = chars + specialChars;
      password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * Update user
   * @param {number} userId - User ID
   * @param {Object} updateData - Update data
   * @param {number} updatedBy - ID of user making the update
   * @returns {Object} Updated user
   */
  async updateUser(userId, updateData, updatedBy) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const {
        username,
        email,
        firstName,
        lastName,
        phone,
        isActive,
        password
      } = updateData;

      // Check if email is being changed and doesn't conflict
      if (email && email !== user.email) {
        const existingUser = await User.findOne({ 
          where: { 
            email,
            id: { [Op.ne]: userId }
          }
        });
        if (existingUser) {
          throw new Error('Email already exists');
        }
      }

      // Update user fields
      const updates = {};
      if (username !== undefined) updates.username = username;
      if (email !== undefined) updates.email = email;
      if (firstName !== undefined) updates.first_name = firstName;
      if (lastName !== undefined) updates.last_name = lastName;
      if (phone !== undefined) updates.phone = phone;
      if (isActive !== undefined) updates.is_active = isActive;
      if (password) updates.password_hash = password; // Will be hashed by model hook

      await user.update(updates);

      // Log the action
      await AuditLog.logUserAction(
        updatedBy,
        user.subsidiary_id,
        'UPDATE_USER',
        'user',
        userId.toString(),
        updates
      );

      return await this.getUserById(userId);
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * Delete user (hard delete - permanently removes from database)
   * @param {number} userId - User ID
   * @param {number} deletedBy - ID of user performing deletion
   * @returns {boolean} Success status
   */
  async deleteUser(userId, deletedBy) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Store user info for audit log before deletion
      const userInfo = {
        username: user.username,
        email: user.email,
        name: `${user.first_name} ${user.last_name}`.trim(),
        subsidiary_id: user.subsidiary_id
      };

      // Log the action before deletion
      await AuditLog.logUserAction(
        deletedBy,
        user.subsidiary_id,
        'DELETE_USER',
        'user',
        userId.toString(),
        userInfo
      );

      // Hard delete - this will cascade delete related records due to foreign key constraints
      // UserRole records will be automatically deleted due to CASCADE constraint
      await user.destroy();

      return true;
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * Assign roles to user
   * @param {number} userId - User ID
   * @param {Array} roleIds - Array of role IDs
   * @param {number} assignedBy - ID of user assigning roles
   * @returns {boolean} Success status
   */
  async assignRoles(userId, roleIds, assignedBy) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify all roles exist
      const roles = await Role.findAll({
        where: {
          id: { [Op.in]: roleIds },
          is_active: true
        }
      });

      if (roles.length !== roleIds.length) {
        throw new Error('One or more roles not found or inactive');
      }

      // Remove all existing user roles (hard delete)
      await UserRole.destroy({
        where: { user_id: userId }
      });

      // Create new user role assignments
      const userRoles = roleIds.map(roleId => ({
        user_id: userId,
        role_id: roleId,
        assigned_by: assignedBy,
        assigned_at: new Date(),
        is_active: true
      }));

      await UserRole.bulkCreate(userRoles);

      // Log the action
      await AuditLog.logUserAction(
        assignedBy,
        user.subsidiary_id,
        'ASSIGN_ROLES',
        'user',
        userId.toString(),
        { roleIds, roleNames: roles.map(r => r.name) }
      );

      return true;
    } catch (error) {
      console.error(`Error assigning roles to user ${userId}:`, error.message);
      throw error;
    }
  }

  /**
   * Get user's permissions
   * @param {number} userId - User ID
   * @returns {Array} Array of permissions
   */
  async getUserPermissions(userId) {
    try {
      const user = await User.findByPk(userId, {
        include: [{
          model: Role,
          as: 'roles',
          attributes: ['permissions'],
          through: {
            attributes: [],
            where: { is_active: true }
          }
        }]
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Combine all permissions from all roles
      const allPermissions = new Set();
      user.roles.forEach(role => {
        role.permissions.forEach(permission => {
          allPermissions.add(permission);
        });
      });

      return Array.from(allPermissions);
    } catch (error) {
      console.error(`Error getting permissions for user ${userId}:`, error.message);
      throw error;
    }
  }
}

module.exports = new UserService();
