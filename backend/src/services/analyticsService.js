/**
 * Real-time Analytics Service
 * Provides comprehensive analytics and monitoring for the audio file management system
 */

const EventEmitter = require('events');

class AnalyticsService extends EventEmitter {
  constructor() {
    super();
    
    // In-memory analytics data (in production, use Redis or database)
    this.analytics = {
      // Real-time metrics
      activeUsers: new Set(),
      currentSessions: new Map(),
      
      // File statistics
      fileStats: {
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {},
        mostPlayed: [],
        mostDownloaded: [],
        recentActivity: []
      },
      
      // User activity
      userActivity: {
        totalUsers: 0,
        activeToday: new Set(),
        loginHistory: [],
        sessionDurations: []
      },
      
      // System metrics
      systemMetrics: {
        apiCalls: 0,
        errors: 0,
        responseTime: [],
        uptime: Date.now(),
        s3Operations: 0
      },
      
      // Real-time events
      liveEvents: []
    };
    
    // Start periodic cleanup
    this.startCleanup();
  }
  
  /**
   * Track user login
   */
  trackLogin(userId, userAgent, ipAddress) {
    const event = {
      type: 'login',
      userId,
      userAgent,
      ipAddress,
      timestamp: new Date()
    };
    
    // Add to active users
    this.analytics.activeUsers.add(userId);
    this.analytics.userActivity.activeToday.add(userId);
    
    // Track login history
    this.analytics.userActivity.loginHistory.unshift(event);
    if (this.analytics.userActivity.loginHistory.length > 100) {
      this.analytics.userActivity.loginHistory.pop();
    }
    
    // Add to live events
    this.addLiveEvent(event);
    
    console.log(`Analytics: User ${userId} logged in`);
  }
  
  /**
   * Track user logout
   */
  trackLogout(userId, sessionDuration) {
    const event = {
      type: 'logout',
      userId,
      sessionDuration,
      timestamp: new Date()
    };
    
    // Remove from active users
    this.analytics.activeUsers.delete(userId);
    this.analytics.currentSessions.delete(userId);
    
    // Track session duration
    this.analytics.userActivity.sessionDurations.push(sessionDuration);
    if (this.analytics.userActivity.sessionDurations.length > 1000) {
      this.analytics.userActivity.sessionDurations.shift();
    }
    
    this.addLiveEvent(event);
    console.log(`Analytics: User ${userId} logged out (session: ${sessionDuration}ms)`);
  }
  
  /**
   * Track file play
   */
  trackFilePlay(userId, fileKey, fileName) {
    const event = {
      type: 'play',
      userId,
      fileKey,
      fileName,
      timestamp: new Date()
    };
    
    // Update most played
    const existing = this.analytics.fileStats.mostPlayed.find(f => f.fileKey === fileKey);
    if (existing) {
      existing.count++;
      existing.lastPlayed = new Date();
    } else {
      this.analytics.fileStats.mostPlayed.push({
        fileKey,
        fileName,
        count: 1,
        lastPlayed: new Date()
      });
    }
    
    // Sort and limit
    this.analytics.fileStats.mostPlayed.sort((a, b) => b.count - a.count);
    this.analytics.fileStats.mostPlayed = this.analytics.fileStats.mostPlayed.slice(0, 50);
    
    this.addLiveEvent(event);
    console.log(`Analytics: File played - ${fileName}`);
  }
  
  /**
   * Track file download
   */
  trackFileDownload(userId, fileKey, fileName, fileSize) {
    const event = {
      type: 'download',
      userId,
      fileKey,
      fileName,
      fileSize,
      timestamp: new Date()
    };
    
    // Update most downloaded
    const existing = this.analytics.fileStats.mostDownloaded.find(f => f.fileKey === fileKey);
    if (existing) {
      existing.count++;
      existing.lastDownloaded = new Date();
      existing.totalSize += fileSize;
    } else {
      this.analytics.fileStats.mostDownloaded.push({
        fileKey,
        fileName,
        count: 1,
        totalSize: fileSize,
        lastDownloaded: new Date()
      });
    }
    
    // Sort and limit
    this.analytics.fileStats.mostDownloaded.sort((a, b) => b.count - a.count);
    this.analytics.fileStats.mostDownloaded = this.analytics.fileStats.mostDownloaded.slice(0, 50);
    
    this.addLiveEvent(event);
    console.log(`Analytics: File downloaded - ${fileName} (${fileSize} bytes)`);
  }
  
  /**
   * Track file upload
   */
  trackFileUpload(userId, fileName, fileSize, fileType) {
    const event = {
      type: 'upload',
      userId,
      fileName,
      fileSize,
      fileType,
      timestamp: new Date()
    };
    
    // Update file statistics
    this.analytics.fileStats.totalFiles++;
    this.analytics.fileStats.totalSize += fileSize;
    
    // Update file types
    if (this.analytics.fileStats.fileTypes[fileType]) {
      this.analytics.fileStats.fileTypes[fileType]++;
    } else {
      this.analytics.fileStats.fileTypes[fileType] = 1;
    }
    
    this.addLiveEvent(event);
    console.log(`Analytics: File uploaded - ${fileName} (${fileType})`);
  }
  
  /**
   * Track API call
   */
  trackApiCall(method, endpoint, responseTime, statusCode, userId = null) {
    this.analytics.systemMetrics.apiCalls++;
    this.analytics.systemMetrics.responseTime.push(responseTime);
    
    // Keep only last 1000 response times
    if (this.analytics.systemMetrics.responseTime.length > 1000) {
      this.analytics.systemMetrics.responseTime.shift();
    }
    
    // Track errors
    if (statusCode >= 400) {
      this.analytics.systemMetrics.errors++;
    }
    
    // Emit real-time event for slow requests
    if (responseTime > 2000) {
      this.addLiveEvent({
        type: 'slow_request',
        method,
        endpoint,
        responseTime,
        userId,
        timestamp: new Date()
      });
    }
  }
  
  /**
   * Track S3 operation
   */
  trackS3Operation(operation, success = true) {
    this.analytics.systemMetrics.s3Operations++;
    
    if (!success) {
      this.addLiveEvent({
        type: 's3_error',
        operation,
        timestamp: new Date()
      });
    }
  }
  
  /**
   * Add live event
   */
  addLiveEvent(event) {
    this.analytics.liveEvents.unshift(event);
    
    // Keep only last 100 events
    if (this.analytics.liveEvents.length > 100) {
      this.analytics.liveEvents.pop();
    }
    
    // Emit to WebSocket clients
    this.emit('liveEvent', event);
  }
  
  /**
   * Get real-time dashboard data
   */
  getDashboardData() {
    const now = new Date();
    const avgResponseTime = this.analytics.systemMetrics.responseTime.length > 0
      ? this.analytics.systemMetrics.responseTime.reduce((a, b) => a + b, 0) / this.analytics.systemMetrics.responseTime.length
      : 0;
    
    const avgSessionDuration = this.analytics.userActivity.sessionDurations.length > 0
      ? this.analytics.userActivity.sessionDurations.reduce((a, b) => a + b, 0) / this.analytics.userActivity.sessionDurations.length
      : 0;
    
    return {
      // Real-time metrics
      realTime: {
        activeUsers: this.analytics.activeUsers.size,
        currentSessions: this.analytics.currentSessions.size,
        liveEvents: this.analytics.liveEvents.slice(0, 20)
      },
      
      // File statistics
      files: {
        total: this.analytics.fileStats.totalFiles,
        totalSize: this.analytics.fileStats.totalSize,
        fileTypes: this.analytics.fileStats.fileTypes,
        mostPlayed: this.analytics.fileStats.mostPlayed.slice(0, 10),
        mostDownloaded: this.analytics.fileStats.mostDownloaded.slice(0, 10)
      },
      
      // User activity
      users: {
        total: this.analytics.userActivity.totalUsers,
        activeToday: this.analytics.userActivity.activeToday.size,
        recentLogins: this.analytics.userActivity.loginHistory.slice(0, 10),
        avgSessionDuration: Math.round(avgSessionDuration / 1000) // Convert to seconds
      },
      
      // System metrics
      system: {
        uptime: now - this.analytics.systemMetrics.uptime,
        apiCalls: this.analytics.systemMetrics.apiCalls,
        errors: this.analytics.systemMetrics.errors,
        errorRate: this.analytics.systemMetrics.apiCalls > 0 
          ? (this.analytics.systemMetrics.errors / this.analytics.systemMetrics.apiCalls * 100).toFixed(2)
          : 0,
        avgResponseTime: Math.round(avgResponseTime),
        s3Operations: this.analytics.systemMetrics.s3Operations
      },
      
      // Timestamp
      timestamp: now
    };
  }
  
  /**
   * Get historical data for charts
   */
  getHistoricalData(timeRange = '24h') {
    // This would typically query a database
    // For now, return mock data based on current analytics
    
    const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 720;
    const interval = timeRange === '24h' ? 1 : timeRange === '7d' ? 6 : 24;
    
    const data = [];
    const now = new Date();
    
    for (let i = hours; i >= 0; i -= interval) {
      const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));
      data.push({
        timestamp,
        activeUsers: Math.floor(Math.random() * this.analytics.activeUsers.size * 2),
        apiCalls: Math.floor(Math.random() * 100),
        uploads: Math.floor(Math.random() * 10),
        downloads: Math.floor(Math.random() * 20),
        plays: Math.floor(Math.random() * 50)
      });
    }
    
    return data;
  }
  
  /**
   * Start periodic cleanup
   */
  startCleanup() {
    setInterval(() => {
      // Clean up old daily active users (reset at midnight)
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0) {
        this.analytics.userActivity.activeToday.clear();
      }
      
      // Clean up old events
      this.analytics.liveEvents = this.analytics.liveEvents.slice(0, 100);
      
    }, 60000); // Run every minute
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

module.exports = analyticsService;
