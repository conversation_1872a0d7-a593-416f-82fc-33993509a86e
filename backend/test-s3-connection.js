#!/usr/bin/env node

require('dotenv').config();
const AWS = require('aws-sdk');

async function testS3Connection() {
  try {
    console.log('Testing S3 connection for Fanikiwa...');
    
    // Configure AWS for Fanikiwa
    const s3 = new AWS.S3({
      accessKeyId: process.env.FANIKIWA_AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.FANIKIWA_AWS_SECRET_ACCESS_KEY,
      region: process.env.FANIKIWA_AWS_REGION || 'us-east-1'
    });
    
    const bucketName = process.env.FANIKIWA_S3_BUCKET;
    
    console.log('Configuration:');
    console.log('  Bucket:', bucketName);
    console.log('  Region:', process.env.FANIKIWA_AWS_REGION || 'us-east-1');
    console.log('  Access Key ID:', process.env.FANIKIWA_AWS_ACCESS_KEY_ID ? 'Set' : 'Missing');
    console.log('  Secret Access Key:', process.env.FANIKIWA_AWS_SECRET_ACCESS_KEY ? 'Set' : 'Missing');
    
    console.log('\n Testing bucket access...');
    
    // Test bucket access
    const headResult = await s3.headBucket({ Bucket: bucketName }).promise();
    console.log('Bucket exists and is accessible');
    
    console.log('\nListing objects (first 10)...');
    
    // List objects
    const listResult = await s3.listObjectsV2({
      Bucket: bucketName,
      MaxKeys: 10
    }).promise();
    
    console.log(`Found ${listResult.Contents?.length || 0} objects in root`);
    
    if (listResult.Contents && listResult.Contents.length > 0) {
      console.log('\n🎵 Sample files:');
      listResult.Contents.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.Key} (${file.Size} bytes)`);
      });
    }
    
    // Test recursive listing
    console.log('\n Testing recursive listing...');
    
    const allObjects = [];
    let continuationToken = null;
    let totalScanned = 0;
    
    do {
      const params = {
        Bucket: bucketName,
        MaxKeys: 100,
        ...(continuationToken && { ContinuationToken: continuationToken })
      };
      
      const data = await s3.listObjectsV2(params).promise();
      
      if (data.Contents) {
        allObjects.push(...data.Contents);
        totalScanned += data.Contents.length;
        console.log(`Scanned ${totalScanned} objects so far...`);
      }
      
      continuationToken = data.NextContinuationToken;
      
      // Safety limit to prevent infinite loops
      if (totalScanned > 1000) {
        console.log('Stopping at 1000 objects for safety');
        break;
      }
      
    } while (continuationToken);
    
    console.log(`\n Total objects found: ${allObjects.length}`);
    
    // Filter audio files
    const audioExtensions = ['mp3', 'wav', 'flac', 'm4a', 'aac', 'ogg', 'wma'];
    const audioFiles = allObjects.filter(file => {
      const ext = file.Key.split('.').pop()?.toLowerCase();
      return audioExtensions.includes(ext);
    });
    
    console.log(`🎵 Audio files found: ${audioFiles.length}`);
    
    if (audioFiles.length > 0) {
      console.log('\n🎵 Sample audio files:');
      audioFiles.slice(0, 10).forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.Key} (${(file.Size / 1024 / 1024).toFixed(2)} MB)`);
      });
    }
    
  } catch (error) {
    console.error('S3 Connection Error:', error.message);
    if (error.code) {
      console.error('   Error Code:', error.code);
    }
    if (error.statusCode) {
      console.error('   Status Code:', error.statusCode);
    }
  }
}

testS3Connection();
