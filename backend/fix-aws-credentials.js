#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

// Import models
const { Subsidiary } = require('./src/models');

async function fixAwsCredentials() {
  try {
    console.log('Fixing AWS credentials for subsidiaries...');
    
    // Update AWS credentials for each subsidiary
    const updates = [
      {
        code: 'PREMIERKE',
        aws_access_key_id: process.env.PREMIER_KE_AWS_ACCESS_KEY_ID,
        aws_secret_access_key: process.env.PREMIER_KE_AWS_SECRET_ACCESS_KEY,
        aws_region: process.env.PREMIER_KE_AWS_REGION || 'eu-central-1',
        s3_bucket_name: process.env.PREMIER_KE_S3_BUCKET
      },
      {
        code: 'PLATINUMUG',
        aws_access_key_id: process.env.PLATINUM_UG_AWS_ACCESS_KEY_ID,
        aws_secret_access_key: process.env.PLATINUM_UG_AWS_SECRET_ACCESS_KEY,
        aws_region: process.env.PLATINUM_UG_AWS_REGION || 'eu-central-1',
        s3_bucket_name: process.env.PLATINUM_UG_S3_BUCKET
      },
      {
        code: 'PREMIERUG',
        aws_access_key_id: process.env.PREMIER_UG_AWS_ACCESS_KEY_ID,
        aws_secret_access_key: process.env.PREMIER_UG_AWS_SECRET_ACCESS_KEY,
        aws_region: process.env.PREMIER_UG_AWS_REGION || 'eu-central-1',
        s3_bucket_name: process.env.PREMIER_UG_S3_BUCKET
      }
    ];
    
    for (const update of updates) {
      const { code, ...credentials } = update;
      
      console.log(`Updating ${code} with credentials:`, {
        aws_access_key_id: credentials.aws_access_key_id ? '***' + credentials.aws_access_key_id.slice(-4) : 'MISSING',
        aws_secret_access_key: credentials.aws_secret_access_key ? '***' + credentials.aws_secret_access_key.slice(-4) : 'MISSING',
        aws_region: credentials.aws_region,
        s3_bucket_name: credentials.s3_bucket_name
      });
      
      const [updatedRows] = await Subsidiary.update(credentials, {
        where: { code }
      });
      
      if (updatedRows > 0) {
        console.log(`Updated AWS credentials for ${code}`);
      } else {
        console.log(`Failed to update ${code} - subsidiary not found`);
      }
    }
    
    // Verify the updates
    console.log('\n Verifying AWS configurations...');
    const subsidiaries = await Subsidiary.findAll({
      where: { 
        code: ['PREMIERKE', 'PLATINUMUG', 'PREMIERUG'],
        is_active: true 
      }
    });
    
    for (const subsidiary of subsidiaries) {
      try {
        const config = subsidiary.getAwsConfig();
        console.log(`${subsidiary.code}: AWS config is complete`);
      } catch (error) {
        console.log(`${subsidiary.code}: ${error.message}`);
      }
    }
    
    console.log('AWS credentials fix completed!');
    return true;
  } catch (error) {
    console.error('Error fixing AWS credentials:', error.message);
    return false;
  }
}

// Run the fix
fixAwsCredentials().then((success) => {
  process.exit(success ? 0 : 1);
});
