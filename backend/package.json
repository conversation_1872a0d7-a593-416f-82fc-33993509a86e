{"name": "audio-file-management-backend", "version": "1.0.0", "description": "Backend API for Audio File Management application", "license": "MIT", "scripts": {"lint": "eslint --fix src", "server": "nodemon src/server.js", "server-stable": "node src/server.js", "client": "cd ../frontend && npm run dev", "clientinstall": "npm start --prefix frontend", "dev": "concurrently \"npm run server\" \"npm run client\"", "dev-stable": "concurrently \"npm run server-stable\" \"npm run client\"", "start": "node src/server.js", "test": "mocha --exit", "lint:fix": "eslint src/ --fix"}, "keywords": ["audio", "file-management", "s3", "nodejs", "express"], "author": "Your Name", "dependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-mocha": "^5.0.8", "@vueform/multiselect": "^2.6.11", "archiver": "^5.3.1", "autoprefixer": "^10.4.20", "aws-sdk": "^2.1467.0", "axios": "^0.26.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-stringify": "^6.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.11.2", "express-validator": "^7.0.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.2", "music-metadata": "^7.14.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "pinia": "^2.3.1", "postcss": "^8.5.1", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "tailwindcss": "^4.0.3", "uuid": "^11.1.0", "validator": "^13.15.15", "vue": "^3.5.13", "vue-chartjs": "^4.1.2", "vue-cli-plugin-vuetify": "^2.5.8", "vue-multiselect": "^3.2.0", "vue-router": "^3.6.5", "vuetify": "^3.7.12", "vuex": "^4.1.0", "ws": "^8.18.3"}, "devDependencies": {"concurrently": "^7.0.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.32.0", "nodemon": "^2.0.15", "prettier": "3.4.2"}}