#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

// Import the seeder
const { seedDatabase } = require('./src/database/seeders');

// Run the seeder
async function runSeeder() {
  try {
    console.log('🌱 Starting database seeding...');
    const success = await seedDatabase();
    
    if (success) {
      console.log('✅ Database seeding completed successfully!');
      process.exit(0);
    } else {
      console.error('❌ Database seeding failed!');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error running seeder:', error.message);
    process.exit(1);
  }
}

runSeeder();
