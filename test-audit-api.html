<!DOCTYPE html>
<html>
<head>
    <title>Test Audit API</title>
</head>
<body>
    <h1>Test Audit API</h1>
    <button onclick="testLogin()">1. Login</button>
    <button onclick="testAuditSummary()">2. Test Audit Summary</button>
    <div id="results"></div>

    <script>
        let token = '';
        
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (data.success) {
                    token = data.token;
                    document.getElementById('results').innerHTML += '<p>✅ Login successful</p>';
                } else {
                    document.getElementById('results').innerHTML += '<p>❌ Login failed: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('results').innerHTML += '<p>❌ Login error: ' + error.message + '</p>';
            }
        }
        
        async function testAuditSummary() {
            if (!token) {
                document.getElementById('results').innerHTML += '<p>❌ Please login first</p>';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8081/api/audit/summary', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                const data = await response.json();
                console.log('Audit summary response:', data);
                
                if (data.success) {
                    document.getElementById('results').innerHTML += '<p>✅ Audit summary successful</p>';
                    document.getElementById('results').innerHTML += '<pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
                } else {
                    document.getElementById('results').innerHTML += '<p>❌ Audit summary failed: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('Audit summary error:', error);
                document.getElementById('results').innerHTML += '<p>❌ Audit summary error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
