# Docker Compose Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration
POSTGRES_USER=dev
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=audio_management_dev

# Redis Configuration
REDIS_PASSWORD=your_redis_password

# Application Configuration
NODE_ENV=production
PORT=8082
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Database Connection for App
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=dev
DB_PASSWORD=your_postgres_password
DB_DBNAME=audio_management_dev
DB_DIALECT=postgres

# Redis Connection for App
REDIS_HOST=redis
REDIS_PORT=6379

# External Port Mappings
POSTGRES_EXTERNAL_PORT=5433
REDIS_EXTERNAL_PORT=6380

# AWS Configuration (Default/Fallback)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=eu-central-1
S3_BUCKET_NAME=your_default_s3_bucket

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://127.0.0.1:5173,http://localhost:5174,http://127.0.0.1:5174,https://your-domain.com

# File Upload Configuration
MAX_FILE_SIZE=104857600
ALLOWED_AUDIO_FORMATS=mp3,wav,ogg,m4a,aac,flac,wma
UPLOAD_PATH=./uploads
LOG_LEVEL=info
LOG_PATH=./logs

# Subsidiary-specific AWS Credentials
# Spectrum Zambia
SPECTRUM_AWS_ACCESS_KEY_ID=your_spectrum_access_key_id
SPECTRUM_AWS_SECRET_ACCESS_KEY=your_spectrum_secret_access_key
SPECTRUM_AWS_REGION=eu-central-1
SPECTRUM_S3_BUCKET=your_spectrum_s3_bucket

# Premier Fanikiwa
FANIKIWA_AWS_ACCESS_KEY_ID=your_fanikiwa_access_key_id
FANIKIWA_AWS_SECRET_ACCESS_KEY=your_fanikiwa_secret_access_key
FANIKIWA_AWS_REGION=eu-central-1
FANIKIWA_S3_BUCKET=your_fanikiwa_s3_bucket

# Premier Uganda
PREMIER_UG_AWS_ACCESS_KEY_ID=your_premier_ug_access_key_id
PREMIER_UG_AWS_SECRET_ACCESS_KEY=your_premier_ug_secret_access_key
PREMIER_UG_AWS_REGION=eu-central-1
PREMIER_UG_S3_BUCKET=your_premier_ug_s3_bucket

# Platinum Uganda
PLATINUM_UG_AWS_ACCESS_KEY_ID=your_platinum_ug_access_key_id
PLATINUM_UG_AWS_SECRET_ACCESS_KEY=your_platinum_ug_secret_access_key
PLATINUM_UG_AWS_REGION=eu-central-1
PLATINUM_UG_S3_BUCKET=your_platinum_ug_s3_bucket

# Premier Kenya
PREMIER_KE_AWS_ACCESS_KEY_ID=your_premier_ke_access_key_id
PREMIER_KE_AWS_SECRET_ACCESS_KEY=your_premier_ke_secret_access_key
PREMIER_KE_AWS_REGION=eu-central-1
PREMIER_KE_S3_BUCKET=your_premier_ke_s3_bucket

# Momentum Credit
MOMENTUM_AWS_ACCESS_KEY_ID=your_momentum_access_key_id
MOMENTUM_AWS_SECRET_ACCESS_KEY=your_momentum_secret_access_key
MOMENTUM_AWS_REGION=eu-central-1
MOMENTUM_S3_BUCKET=your_momentum_s3_bucket

# Platinum Kenya
PLATINUM_KE_AWS_ACCESS_KEY_ID=your_platinum_ke_access_key_id
PLATINUM_KE_AWS_SECRET_ACCESS_KEY=your_platinum_ke_secret_access_key
PLATINUM_KE_AWS_REGION=eu-central-1
PLATINUM_KE_S3_BUCKET=your_platinum_ke_s3_bucket

# Platinum Tanzania
PLATINUM_TZ_AWS_ACCESS_KEY_ID=your_platinum_tz_access_key_id
PLATINUM_TZ_AWS_SECRET_ACCESS_KEY=your_platinum_tz_secret_access_key
PLATINUM_TZ_AWS_REGION=eu-central-1
PLATINUM_TZ_S3_BUCKET=your_platinum_tz_s3_bucket
