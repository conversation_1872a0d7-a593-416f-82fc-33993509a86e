<template>
  <div class="user-management">
    <!-- Header -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="fas fa-users"></i>
            User Management
          </h1>
          <p class="page-subtitle">Manage users and their roles within your subsidiary</p>
        </div>
        
        <div class="header-actions">
          <button 
            v-if="canCreateUsers"
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <i class="fas fa-plus"></i>
            Add User
          </button>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchQuery"
          @input="debouncedSearch"
          type="text"
          placeholder="Search users by name, email, or username..."
          class="search-input"
        />
      </div>
      
      <div class="filter-controls">
        <select v-model="filters.roleId" @change="loadUsers(1)" class="filter-select">
          <option value="">All Roles</option>
          <option v-for="role in roles" :key="role.id" :value="role.id">
            {{ role.name }}
          </option>
        </select>

        <select v-model="filters.isActive" @change="loadUsers(1)" class="filter-select">
          <option value="">All Status</option>
          <option value="true">Active</option>
          <option value="false">Inactive</option>
        </select>

        <select v-model="filters.sortBy" @change="loadUsers(1)" class="filter-select">
          <option value="created_at">Created Date</option>
          <option value="username">Username</option>
          <option value="email">Email</option>
          <option value="last_login_at">Last Login</option>
        </select>

        <select v-model="filters.sortOrder" @change="loadUsers(1)" class="filter-select">
          <option value="DESC">Newest First</option>
          <option value="ASC">Oldest First</option>
        </select>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading users...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button @click="loadUsers(1)" class="btn btn-secondary">
        <i class="fas fa-redo"></i>
        Retry
      </button>
    </div>

    <!-- Users Table -->
    <div v-else class="users-table-container">
      <div class="table-wrapper">
        <table class="users-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Email</th>
              <th>Roles</th>
              <th>Subsidiary</th>
              <th>Status</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.id" class="user-row">
              <td class="user-info">
                <div class="user-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                  <div class="user-name">{{ user.getFullName ? user.getFullName() : user.username }}</div>
                  <div class="user-username">@{{ user.username }}</div>
                </div>
              </td>
              
              <td class="user-email">{{ user.email }}</td>
              
              <td class="user-roles">
                <div class="role-badges">
                  <span 
                    v-for="role in user.roles" 
                    :key="role.id"
                    :class="['role-badge', getRoleClass(role.hierarchy_level)]"
                  >
                    <i :class="getRoleIcon(role.hierarchy_level)"></i>
                    {{ role.name }}
                  </span>
                </div>
              </td>
              
              <td class="user-subsidiary">
                <span class="subsidiary-badge">
                  {{ user.subsidiary?.name || 'N/A' }}
                </span>
              </td>
              
              <td class="user-status">
                <span :class="['status-badge', user.is_active ? 'active' : 'inactive']">
                  <i :class="user.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                  {{ user.is_active ? 'Active' : 'Inactive' }}
                </span>
              </td>
              
              <td class="user-last-login">
                {{ formatDate(user.last_login_at) }}
              </td>
              
              <td class="user-actions">
                <div class="action-buttons">
                  <button
                    @click="viewUser(user)"
                    class="btn-action btn-view"
                    title="View Details"
                  >
                    <i class="fas fa-eye"></i>
                    <span>View</span>
                  </button>

                  <button
                    v-if="canEditUser(user)"
                    @click="editUser(user)"
                    class="btn-action btn-edit"
                    title="Edit User"
                  >
                    <i class="fas fa-edit"></i>
                    <span>Edit</span>
                  </button>

                  <button
                    v-if="canDeleteUser(user)"
                    @click="confirmDeleteUser(user)"
                    class="btn-action btn-delete"
                    title="Delete User"
                  >
                    <i class="fas fa-trash"></i>
                    <span>Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="pagination.totalPages > 1" class="pagination-container">
        <div class="pagination-info">
          Showing {{ (pagination.currentPage - 1) * 20 + 1 }} to 
          {{ Math.min(pagination.currentPage * 20, pagination.totalUsers) }} 
          of {{ pagination.totalUsers }} users
        </div>
        
        <div class="pagination-controls">
          <button 
            @click="changePage(pagination.currentPage - 1)"
            :disabled="!pagination.hasPrev"
            class="btn btn-secondary"
          >
            <i class="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span class="page-info">
            Page {{ pagination.currentPage }} of {{ pagination.totalPages }}
          </span>
          
          <button 
            @click="changePage(pagination.currentPage + 1)"
            :disabled="!pagination.hasNext"
            class="btn btn-secondary"
          >
            Next
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <UserCreateModal 
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @created="handleUserCreated"
    />
    
    <UserEditModal 
      v-if="showEditModal && selectedUser"
      :user="selectedUser"
      @close="showEditModal = false"
      @updated="handleUserUpdated"
    />
    
    <!-- User Details Modal -->
    <div v-if="showDetailsModal && selectedUser" class="modal-overlay" @click="showDetailsModal = false">
      <div class="user-details-modal" @click.stop>
        <!-- Modal Header -->
        <div class="modal-header">
          <div class="header-content">
            <div class="user-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="user-title">
              <h2 class="modal-title">{{ getFullName(selectedUser) }}</h2>
              <p class="user-subtitle">{{ selectedUser.email }}</p>
            </div>
          </div>
          <button @click="showDetailsModal = false" class="modal-close">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
          <!-- Status Badge -->
          <div class="status-section">
            <span :class="['status-badge-large', selectedUser.is_active ? 'active' : 'inactive']">
              <i :class="selectedUser.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
              {{ selectedUser.is_active ? 'Active Account' : 'Inactive Account' }}
            </span>
          </div>

          <!-- User Information Grid -->
          <div class="info-grid">
            <div class="info-card">
              <div class="info-header">
                <i class="fas fa-user-circle"></i>
                <h3>Personal Information</h3>
              </div>
              <div class="info-content">
                <div class="info-item">
                  <label>Username</label>
                  <span>{{ selectedUser.username }}</span>
                </div>
                <div class="info-item">
                  <label>First Name</label>
                  <span>{{ selectedUser.first_name || 'Not provided' }}</span>
                </div>
                <div class="info-item">
                  <label>Last Name</label>
                  <span>{{ selectedUser.last_name || 'Not provided' }}</span>
                </div>
                <div class="info-item">
                  <label>Phone</label>
                  <span>{{ selectedUser.phone || 'Not provided' }}</span>
                </div>
              </div>
            </div>

            <div class="info-card">
              <div class="info-header">
                <i class="fas fa-building"></i>
                <h3>Organization</h3>
              </div>
              <div class="info-content">
                <div class="info-item">
                  <label>Subsidiary</label>
                  <span>{{ selectedUser.subsidiary?.name || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <label>Roles</label>
                  <div class="roles-list">
                    <span
                      v-for="role in selectedUser.roles"
                      :key="role.id"
                      :class="['role-badge', getRoleClass(role.hierarchy_level)]"
                    >
                      <i :class="getRoleIcon(role.hierarchy_level)"></i>
                      {{ role.name }}
                    </span>
                    <span v-if="!selectedUser.roles || selectedUser.roles.length === 0" class="no-roles">
                      No roles assigned
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="info-card">
              <div class="info-header">
                <i class="fas fa-clock"></i>
                <h3>Account Activity</h3>
              </div>
              <div class="info-content">
                <div class="info-item">
                  <label>Created</label>
                  <span>{{ formatDate(selectedUser.created_at) }}</span>
                </div>
                <div class="info-item">
                  <label>Last Login</label>
                  <span>{{ formatDate(selectedUser.last_login_at) || 'Never' }}</span>
                </div>
                <div class="info-item">
                  <label>Login Attempts</label>
                  <span>{{ selectedUser.login_attempts || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
          <button @click="showDetailsModal = false" class="btn btn-secondary">
            <i class="fas fa-times"></i>
            Close
          </button>
          <button @click="editUser(selectedUser)" class="btn btn-primary">
            <i class="fas fa-edit"></i>
            Edit User
          </button>
        </div>
      </div>
    </div>

    <ConfirmationModal
      v-if="showDeleteConfirmation && userToDelete"
      :title="'Delete User'"
      :message="`Are you sure you want to delete user '${userToDelete.username}'? This action cannot be undone.`"
      :confirm-text="'Delete'"
      :cancel-text="'Cancel'"
      :type="'danger'"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
      @close="handleDeleteCancel"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/userService'
import UserCreateModal from '@/components/modals/UserCreateModal.vue'
import UserEditModal from '@/components/modals/UserEditModal.vue'
import UserDetailsModal from '@/components/modals/UserDetailsModal.vue'
import ConfirmationModal from '@/components/modals/ConfirmationModal.vue'
// Simple debounce function
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export default {
  name: 'UserManagementView',
  components: {
    UserCreateModal,
    UserEditModal,
    UserDetailsModal,
    ConfirmationModal
  },
  setup() {
    const authStore = useAuthStore()

    // Reactive data
    const loading = ref(false)
    const error = ref(null)
    const users = ref([])
    const roles = ref([])
    const pagination = ref({})
    const searchQuery = ref('')
    
    const filters = reactive({
      roleId: '',
      isActive: '',
      sortBy: 'created_at',
      sortOrder: 'DESC'
    })
    
    // Modal states
    const showCreateModal = ref(false)
    const showEditModal = ref(false)
    const showDetailsModal = ref(false)
    const selectedUser = ref(null)
    const showDeleteConfirmation = ref(false)
    const userToDelete = ref(null)
    
    // Computed properties
    const currentUser = computed(() => authStore.user)
    const userPermissions = computed(() => authStore.permissions)
    
    const canCreateUsers = computed(() => 
      userPermissions.value.includes('users:write')
    )
    
    // Methods
    const loadUsers = async (page = 1) => {
      try {
        loading.value = true
        error.value = null
        
        const params = {
          page,
          search: searchQuery.value,
          ...filters
        }
        
        const response = await userService.getUsers(params)
        users.value = response.users
        pagination.value = response.pagination
      } catch (err) {
        error.value = err.response?.data?.message || 'Failed to load users'
        console.error('Error loading users:', err)
      } finally {
        loading.value = false
      }
    }
    
    const loadRoles = async () => {
      try {
        const response = await userService.getRoles()
        roles.value = response.roles
      } catch (err) {
        console.error('Error loading roles:', err)
      }
    }
    
    const debouncedSearch = debounce(() => {
      loadUsers(1)
    }, 500)
    
    const changePage = (page) => {
      loadUsers(page)
    }
    
    const viewUser = (user) => {
      console.log('ViewUser clicked:', user)
      selectedUser.value = user
      showDetailsModal.value = true
      console.log('Modal state:', showDetailsModal.value, selectedUser.value)
    }
    
    const editUser = (user) => {
      selectedUser.value = user
      showEditModal.value = true
      showDetailsModal.value = false
    }
    
    const canEditUser = (user) => {
      return userPermissions.value.includes('users:write') && 
             (currentUser.value.id !== user.id || userPermissions.value.includes('system:admin'))
    }
    
    const canDeleteUser = (user) => {
      return userPermissions.value.includes('users:delete') && 
             currentUser.value.id !== user.id
    }
    
    const confirmDeleteUser = (user) => {
      userToDelete.value = user
      showDeleteConfirmation.value = true
    }

    const handleDeleteConfirm = async () => {
      try {
        await userService.deleteUser(userToDelete.value.id)
        await loadUsers(pagination.value.currentPage)
        showDeleteConfirmation.value = false
        userToDelete.value = null
        // Show success message
      } catch (err) {
        error.value = err.response?.data?.message || 'Failed to delete user'
        showDeleteConfirmation.value = false
        userToDelete.value = null
      }
    }

    const handleDeleteCancel = () => {
      showDeleteConfirmation.value = false
      userToDelete.value = null
    }

    const getFullName = (user) => {
      if (user.first_name && user.last_name) {
        return `${user.first_name} ${user.last_name}`
      } else if (user.first_name) {
        return user.first_name
      } else if (user.last_name) {
        return user.last_name
      }
      return user.username
    }
    
    const handleUserCreated = () => {
      showCreateModal.value = false
      loadUsers(1)
    }
    
    const handleUserUpdated = () => {
      showEditModal.value = false
      loadUsers(pagination.value.currentPage)
    }
    
    const getRoleClass = (hierarchyLevel) => {
      const hierarchy = userService.getRoleHierarchy()
      return hierarchy[hierarchyLevel]?.color || 'gray'
    }
    
    const getRoleIcon = (hierarchyLevel) => {
      const hierarchy = userService.getRoleHierarchy()
      return `fas fa-${hierarchy[hierarchyLevel]?.icon || 'user'}`
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return 'Never'
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // Lifecycle
    onMounted(() => {
      loadUsers(1)
      loadRoles()

      // Check if we should open a specific user from query params
      const route = useRoute()
      if (route.query.view) {
        const userId = parseInt(route.query.view)
        setTimeout(() => {
          const user = users.value.find(u => u.id === userId)
          if (user) {
            viewUser(user)
          }
        }, 500) // Wait for users to load
      }
    })
    
    return {
      loading,
      error,
      users,
      roles,
      pagination,
      searchQuery,
      filters,
      showCreateModal,
      showEditModal,
      showDetailsModal,
      selectedUser,
      showDeleteConfirmation,
      userToDelete,
      canCreateUsers,
      loadUsers,
      debouncedSearch,
      changePage,
      viewUser,
      editUser,
      canEditUser,
      canDeleteUser,
      confirmDeleteUser,
      handleDeleteConfirm,
      handleDeleteCancel,
      handleUserCreated,
      handleUserUpdated,
      getRoleClass,
      getRoleIcon,
      formatDate,
      getFullName
    }
  }
}
</script>

<style scoped>
.user-management {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header Section */
.header-section {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title i {
  color: #4f46e5;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.search-box {
  position: relative;
  margin-bottom: 1rem;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.filter-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
  transition: all 0.2s;
}

.filter-select:focus {
  outline: none;
  border-color: #4f46e5;
}

/* Loading and Error States */
.loading-container, .error-container {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container i {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

/* Users Table */
.users-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.table-wrapper {
  overflow-x: auto;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f9fafb;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.users-table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.user-row:hover {
  background: #f9fafb;
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 200px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.user-username {
  font-size: 0.875rem;
  color: #6b7280;
}

.user-email {
  color: #4b5563;
  min-width: 200px;
}

/* Role Badges */
.role-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.role-badge.red {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.role-badge.purple {
  background: #faf5ff;
  color: #7c3aed;
  border: 1px solid #e9d5ff;
}

.role-badge.blue {
  background: #eff6ff;
  color: #2563eb;
  border: 1px solid #bfdbfe;
}

.role-badge.green {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.role-badge.gray {
  background: #f9fafb;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

/* Status and Subsidiary Badges */
.subsidiary-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.active {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* User Actions */
.user-actions {
  white-space: nowrap;
  min-width: 200px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  font-weight: 500;
}

.btn-action i {
  font-size: 0.75rem;
}

.btn-action span {
  font-size: 0.75rem;
}

.btn-view {
  background: #eff6ff;
  color: #2563eb;
  border: 1px solid #dbeafe;
}

.btn-view:hover {
  background: #dbeafe;
  border-color: #bfdbfe;
}

.btn-edit {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fde68a;
}

.btn-edit:hover {
  background: #fde68a;
  border-color: #fcd34d;
}

.btn-delete {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-delete:hover {
  background: #fecaca;
  border-color: #fca5a5;
}

/* Legacy icon-only buttons (fallback) */
.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-info {
  color: #374151;
  font-weight: 500;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-controls {
    flex-direction: column;
  }

  .filter-select {
    min-width: auto;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }

  .users-table {
    font-size: 0.875rem;
  }

  .users-table th,
  .users-table td {
    padding: 0.5rem;
  }
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

/* User Details Modal */
.user-details-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

/* Modal Header */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-avatar {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-title {
  flex: 1;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-subtitle {
  margin: 0.25rem 0 0 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

.modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Modal Body */
.modal-body {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.status-section {
  margin-bottom: 2rem;
  text-align: center;
}

.status-badge-large {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge-large.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.status-badge-large.inactive {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.info-card {
  background: #f8fafc;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-header {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.info-header i {
  color: #667eea;
  font-size: 1.1rem;
}

.info-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
}

.info-content {
  padding: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: #64748b;
  font-size: 0.9rem;
}

.info-item span {
  color: #334155;
  font-weight: 500;
  text-align: right;
}

/* Roles List */
.roles-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.role-badge.admin {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.role-badge.qa {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
}

.role-badge.system_admin {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  color: white;
}

.role-badge.compliance {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.no-roles {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.9rem;
}

/* Modal Footer */
.modal-footer {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  border-top: 1px solid #e2e8f0;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.9rem;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.btn-secondary {
  background: #e2e8f0;
  color: #64748b;
}

.btn-secondary:hover {
  background: #cbd5e1;
  color: #475569;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Responsive Design */
@media (max-width: 768px) {
  .user-details-modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .modal-header {
    padding: 1.5rem;
  }

  .header-content {
    gap: 1rem;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .modal-title {
    font-size: 1.3rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
