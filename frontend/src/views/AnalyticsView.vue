<template>
  <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">Real-time Analytics</h1>
              <p class="text-sm text-gray-600">Live insights and system monitoring</p>
            </div>
          </div>
          
          <!-- Connection Status -->
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div :class="connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'" class="w-2 h-2 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-600">{{ connectionStatus === 'connected' ? 'Live' : 'Disconnected' }}</span>
            </div>
            <button @click="refreshData" class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <svg class="w-5 h-5" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Real-time Metrics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Active Users -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Active Users</p>
              <p class="text-3xl font-bold text-gray-900">{{ dashboardData?.realTime?.activeUsers || 0 }}</p>
              <p class="text-sm text-green-600 mt-1">
                <span class="inline-flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  Live
                </span>
              </p>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Files -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Files</p>
              <p class="text-3xl font-bold text-gray-900">{{ formatNumber(dashboardData?.files?.total || 0) }}</p>
              <p class="text-sm text-gray-500 mt-1">{{ formatBytes(dashboardData?.files?.totalSize || 0) }}</p>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- API Calls -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">API Calls</p>
              <p class="text-3xl font-bold text-gray-900">{{ formatNumber(dashboardData?.system?.apiCalls || 0) }}</p>
              <p class="text-sm text-gray-500 mt-1">{{ dashboardData?.system?.avgResponseTime || 0 }}ms avg</p>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Error Rate -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Error Rate</p>
              <p class="text-3xl font-bold text-gray-900">{{ dashboardData?.system?.errorRate || 0 }}%</p>
              <p class="text-sm text-gray-500 mt-1">{{ dashboardData?.system?.errors || 0 }} errors</p>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-rose-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts and Tables Row -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Most Played Files -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Most Played Files</h3>
          <div class="space-y-3">
            <div v-for="(file, index) in dashboardData?.files?.mostPlayed?.slice(0, 5) || []" :key="file.fileKey" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                  {{ index + 1 }}
                </div>
                <div>
                  <p class="font-medium text-gray-900 truncate max-w-xs">{{ file.fileName }}</p>
                  <p class="text-sm text-gray-500">{{ file.count }} plays</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-500">{{ formatDate(file.lastPlayed) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Activity Feed -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Live Activity</h3>
          <div class="space-y-3 max-h-80 overflow-y-auto">
            <div v-for="event in liveEvents.slice(0, 10)" :key="event.timestamp" class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <div :class="getEventColor(event.type)" class="w-2 h-2 rounded-full mt-2 flex-shrink-0"></div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ getEventDescription(event) }}</p>
                <p class="text-xs text-gray-500">{{ formatTime(event.timestamp) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Status -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ formatUptime(dashboardData?.system?.uptime || 0) }}</p>
            <p class="text-sm text-gray-600">Uptime</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ dashboardData?.system?.s3Operations || 0 }}</p>
            <p class="text-sm text-gray-600">S3 Operations</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ dashboardData?.users?.activeToday || 0 }}</p>
            <p class="text-sm text-gray-600">Active Today</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/services/api'

// Reactive data
const dashboardData = ref(null)
const liveEvents = ref([])
const loading = ref(false)
const connectionStatus = ref('disconnected')
const ws = ref(null)

// Stores
const authStore = useAuthStore()

// WebSocket connection
const connectWebSocket = () => {
  if (!authStore.token) return
  
  const wsUrl = `ws://localhost:8080/ws/analytics?token=${authStore.token}`
  ws.value = new WebSocket(wsUrl)
  
  ws.value.onopen = () => {
    connectionStatus.value = 'connected'
    console.log('Connected to analytics WebSocket')
  }
  
  ws.value.onmessage = (event) => {
    const data = JSON.parse(event.data)
    handleWebSocketMessage(data)
  }
  
  ws.value.onclose = () => {
    connectionStatus.value = 'disconnected'
    console.log('Disconnected from analytics WebSocket')
    
    // Reconnect after 5 seconds
    setTimeout(connectWebSocket, 5000)
  }
  
  ws.value.onerror = (error) => {
    console.error('WebSocket error:', error)
    connectionStatus.value = 'disconnected'
  }
}

// Handle WebSocket messages
const handleWebSocketMessage = (data) => {
  switch (data.type) {
    case 'dashboard_data':
    case 'dashboard_update':
      dashboardData.value = data.data
      break
      
    case 'live_event':
      liveEvents.value.unshift(data.data)
      // Keep only last 50 events
      if (liveEvents.value.length > 50) {
        liveEvents.value = liveEvents.value.slice(0, 50)
      }
      break
  }
}

// Fetch initial data
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const response = await api.get('/analytics/dashboard')
    if (response.data.success) {
      dashboardData.value = response.data.data
      liveEvents.value = response.data.data.realTime.liveEvents || []
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
  } finally {
    loading.value = false
  }
}

// Refresh data
const refreshData = () => {
  fetchDashboardData()
}

// Utility functions
const formatNumber = (num) => {
  return new Intl.NumberFormat().format(num)
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatUptime = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}d ${hours % 24}h`
  if (hours > 0) return `${hours}h ${minutes % 60}m`
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`
  return `${seconds}s`
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString()
}

const getEventColor = (type) => {
  const colors = {
    login: 'bg-green-500',
    logout: 'bg-gray-500',
    play: 'bg-blue-500',
    download: 'bg-purple-500',
    upload: 'bg-indigo-500',
    error: 'bg-red-500'
  }
  return colors[type] || 'bg-gray-400'
}

const getEventDescription = (event) => {
  switch (event.type) {
    case 'login':
      return `User ${event.userId} logged in`
    case 'logout':
      return `User ${event.userId} logged out`
    case 'play':
      return `Playing: ${event.fileName}`
    case 'download':
      return `Downloaded: ${event.fileName}`
    case 'upload':
      return `Uploaded: ${event.fileName}`
    default:
      return `${event.type}: ${JSON.stringify(event)}`
  }
}

// Lifecycle
onMounted(() => {
  fetchDashboardData()
  connectWebSocket()
})

onUnmounted(() => {
  if (ws.value) {
    ws.value.close()
  }
})
</script>
