<template>
  <div class="min-h-screen flex bg-gray-50">
    <!-- Left Side - Login Form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
      <div class="w-full max-w-md space-y-8">
        <div class="bg-white rounded-2xl shadow-xl p-8">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
          <div
            class="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-400 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg"
          >
            <svg
              class="w-8 h-8 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"
              />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Audio Vault</h2>
          <p class="text-gray-600 text-sm">Welcome back! Please sign in to your account</p>
        </div>

        <!-- Error Alert -->
        <div
          v-if="errorMessage"
          class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 font-medium">{{ errorMessage }}</p>
            </div>
            <div class="ml-auto pl-3">
              <button
                @click="errorMessage = ''"
                class="text-red-400 hover:text-red-600 transition-colors"
              >
                <svg
                  class="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <form class="space-y-6" @submit.prevent="handleLogin">
          <!-- Subsidiary Selection -->
          <div class="space-y-2">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                  />
                </svg>
              </div>
              <select
                id="subsidiary"
                v-model="form.subsidiaryId"
                required
                :class="[
                  'w-full pl-12 pr-12 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 appearance-none bg-white',
                  errors.subsidiary ? 'border-red-300 text-red-900' : '',
                ]"
                @change="clearError('subsidiary')"
              >
                <option value="">-- Select a subsidiary --</option>
                <option
                  v-for="subsidiary in subsidiaries"
                  :key="subsidiary.id"
                  :value="subsidiary.id"
                >
                  {{ subsidiary.name }}
                </option>
              </select>
              <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </div>
            <p
              v-if="errors.subsidiary"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.subsidiary }}
            </p>
            <p class="text-xs text-gray-500 ml-4">
              You will only be able to access subsidiaries you have permission for.
            </p>
          </div>

          <!-- Email/Phone Field -->
          <div class="space-y-2">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                  />
                </svg>
              </div>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="text"
                required
                :class="[
                  'w-full pl-12 pr-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.email ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Email / Phone number"
                @blur="validateEmail"
                @input="clearError('email')"
              />
            </div>
            <p
              v-if="errors.email"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.email }}
            </p>
          </div>

          <!-- Password Field -->
          <div class="space-y-2">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"
                  />
                </svg>
              </div>
              <input
                id="password"
                v-model="form.password"
                name="password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full pl-12 pr-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.password ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Password"
                @blur="validatePassword"
                @input="clearError('password')"
              />
            </div>
            <p
              v-if="errors.password"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.password }}
            </p>
          </div>

          <!-- Forgot Password Link -->
          <div class="text-left">
            <a
              href="#"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              Forgot password?
            </a>
          </div>

          <!-- Remember Me -->
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.rememberMe"
              name="remember-me"
              type="checkbox"
              class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
            />
            <label for="remember-me" class="ml-3 text-gray-700 font-medium"
              >Remember me</label
            >
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-full transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
          >
            <svg
              v-if="loading"
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ loading ? "Signing in..." : "Login" }}
          </button>
        </form>
        </div>
      </div>
    </div>

    <!-- Right Side - Illustration -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
      <!-- Background with Purple Gradient -->
      <div class="absolute inset-0 bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600"></div>



      <!-- Curved Background Elements -->
      <div class="absolute inset-0">
        <!-- Large curved shapes exactly like in the reference -->
        <!-- Top right curved shape -->
        <div class="absolute top-0 right-0 w-[600px] h-[400px] rounded-full transform translate-x-48 -translate-y-32"
             style="background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%); border: 2px solid rgba(255,255,255,0.2);"></div>

        <!-- Bottom left curved shape -->
        <div class="absolute bottom-0 left-0 w-[500px] h-[350px] rounded-full transform -translate-x-40 translate-y-32"
             style="background: linear-gradient(45deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%); border: 2px solid rgba(255,255,255,0.15);"></div>

        <!-- Additional curved elements for depth -->
        <div class="absolute top-1/4 right-1/4 w-[300px] h-[200px] rounded-full transform translate-x-24 -translate-y-12"
             style="border: 1px solid rgba(255,255,255,0.1);"></div>

        <!-- Small floating circles -->
        <div class="absolute top-1/3 left-1/4 w-6 h-6 bg-white/20 rounded-full"></div>
        <div class="absolute bottom-1/4 right-1/3 w-4 h-4 bg-white/15 rounded-full"></div>
      </div>

      <!-- Audio Icon (Top Left) -->
      <div class="absolute top-16 left-16 z-10">
        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
          </svg>
        </div>
      </div>

      <!-- Main Illustration Content -->
      <div class="relative z-10 flex flex-col justify-center items-center p-12 text-white">
        <!-- Audio Management Dashboard Mockup -->
        <div class="relative mb-8">
          <!-- Download Icon (Floating outside dashboard) -->
          <div class="absolute -top-4 -right-4 z-20">
            <div class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4V12L15.5 10.5L16.9 11.9L12 16.8L7.1 11.9L8.5 10.5L10 12V4C10 2.9 10.9 2 12 2ZM21 15V18C21 19.1 20.1 20 19 20H5C3.9 20 3 19.1 3 18V15H5V18H19V15H21Z"/>
              </svg>
            </div>
          </div>

          <!-- Main Dashboard Window -->
          <div class="w-80 h-80 bg-white rounded-2xl shadow-2xl relative overflow-hidden">
            <!-- Window Header -->
            <div class="h-10 bg-gray-50 flex items-center px-4 border-b border-gray-200">
              <div class="flex space-x-1.5">
                <div class="w-2.5 h-2.5 bg-red-400 rounded-full"></div>
                <div class="w-2.5 h-2.5 bg-yellow-400 rounded-full"></div>
                <div class="w-2.5 h-2.5 bg-green-400 rounded-full"></div>
              </div>
              <div class="ml-3 text-xs font-medium text-gray-600">Audio Vault Dashboard</div>
            </div>

            <!-- Dashboard Content -->
            <div class="p-6">
              <!-- Audio File List -->
              <div class="space-y-2 mb-4">
                <!-- File Item 1 - Purple -->
                <div class="flex items-center space-x-2 p-2 bg-purple-50 rounded-lg">
                  <div class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-1.5 bg-purple-200 rounded-full">
                      <div class="h-1.5 bg-purple-500 rounded-full w-3/4"></div>
                    </div>
                  </div>
                  <div class="w-8 h-3 bg-purple-500 rounded text-xs text-white flex items-center justify-center font-medium">75%</div>
                </div>

                <!-- File Item 2 - Blue -->
                <div class="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg">
                  <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-1.5 bg-blue-200 rounded-full">
                      <div class="h-1.5 bg-blue-500 rounded-full w-1/2"></div>
                    </div>
                  </div>
                  <div class="w-8 h-3 bg-blue-500 rounded text-xs text-white flex items-center justify-center font-medium">50%</div>
                </div>

                <!-- File Item 3 - Green -->
                <div class="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                  <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-1.5 bg-green-200 rounded-full">
                      <div class="h-1.5 bg-green-500 rounded-full w-full"></div>
                    </div>
                  </div>
                  <div class="w-8 h-3 bg-green-500 rounded text-xs text-white flex items-center justify-center font-medium">100%</div>
                </div>
              </div>

              <!-- Audio Waveform Visualization -->
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-end justify-center space-x-1 h-16">
                  <div class="w-1 bg-purple-400 rounded-full" style="height: 20%"></div>
                  <div class="w-1 bg-purple-500 rounded-full" style="height: 60%"></div>
                  <div class="w-1 bg-purple-600 rounded-full" style="height: 100%"></div>
                  <div class="w-1 bg-purple-500 rounded-full" style="height: 40%"></div>
                  <div class="w-1 bg-purple-400 rounded-full" style="height: 80%"></div>
                  <div class="w-1 bg-purple-600 rounded-full" style="height: 30%"></div>
                  <div class="w-1 bg-purple-500 rounded-full" style="height: 90%"></div>
                  <div class="w-1 bg-purple-400 rounded-full" style="height: 50%"></div>
                  <div class="w-1 bg-purple-600 rounded-full" style="height: 70%"></div>
                  <div class="w-1 bg-purple-500 rounded-full" style="height: 35%"></div>
                  <div class="w-1 bg-purple-400 rounded-full" style="height: 85%"></div>
                  <div class="w-1 bg-purple-600 rounded-full" style="height: 45%"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Floating Audio Icons -->
          <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
          </div>
          <div class="absolute -bottom-4 -right-4 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
          </div>
        </div>

        <!-- Title -->
        <h2 class="text-2xl font-bold text-white mb-6">Audio Vault Dashboard</h2>

        <!-- Feature Icons -->
        <div class="flex space-x-8">
          <!-- Audio Analysis -->
          <div class="flex flex-col items-center">
            <div class="w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
              </svg>
            </div>
            <span class="text-white text-xs font-medium">Audio Analysis</span>
          </div>

          <!-- Cloud Storage -->
          <div class="flex flex-col items-center">
            <div class="w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96z"/>
              </svg>
            </div>
            <span class="text-white text-xs font-medium">Cloud Storage</span>
          </div>

          <!-- File Management -->
          <div class="flex flex-col items-center">
            <div class="w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
            <span class="text-white text-xs font-medium">File Management</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { subsidiaryService } from '@/services/subsidiaryService'
import { useToast } from "vue-toastification";

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

// Reactive state
const loading = ref(false);
const showPassword = ref(false);
const errorMessage = ref("");

const form = reactive({
  subsidiaryId: "",
  email: "",
  password: "",
  rememberMe: false,
});

const errors = reactive({
  subsidiary: "",
  email: "",
  password: "",
});

const subsidiaries = ref([]);

// Computed properties
const isFormValid = computed(() => {
  return (
    form.subsidiaryId !== "" &&
    form.email.trim() !== "" &&
    form.password.trim() !== "" &&
    !errors.subsidiary &&
    !errors.email &&
    !errors.password
  );
});

// Load subsidiaries
const loadSubsidiaries = async () => {
  try {
    const response = await subsidiaryService.getActiveSubsidiaries()
    subsidiaries.value = response.data
  } catch (error) {
    console.error('Failed to load subsidiaries:', error)

    // Fallback to actual subsidiaries from seeders for development
    subsidiaries.value = [
      { id: 1, name: 'Spectrum Zambia', code: 'SPECTRUMZM' },
      { id: 2, name: 'Premier Fanikiwa', code: 'PREMIERFK' }
    ]

    // Show a less alarming message
    console.warn('Using mock subsidiary data for development')
  }
}

// Validation functions
const validateEmail = () => {
  if (!form.email.trim()) {
    errors.email = "Email is required";
    return false;
  }
  if (form.email.length < 3) {
    errors.email = "Email must be at least 3 characters";
    return false;
  }
  errors.email = "";
  return true;
};

const validatePassword = () => {
  if (!form.password.trim()) {
    errors.password = "Password is required";
    return false;
  }
  if (form.password.length < 6) {
    errors.password = "Password must be at least 6 characters";
    return false;
  }
  errors.password = "";
  return true;
};

const clearError = (field) => {
  errors[field] = "";
  errorMessage.value = "";
};

const validateForm = () => {
  const isSubsidiaryValid = form.subsidiaryId !== "";
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();

  if (!isSubsidiaryValid) {
    errors.subsidiary = "Please select a subsidiary";
  } else {
    errors.subsidiary = "";
  }

  return isSubsidiaryValid && isEmailValid && isPasswordValid;
};

// Login handler with enhanced error handling
const handleLogin = async () => {
  // Clear previous errors
  errorMessage.value = "";

  // Validate form
  if (!validateForm()) {
    errorMessage.value = "Please fix the errors above";
    return;
  }

  try {
    loading.value = true;
    // Attempt login
    const loginResult = await authStore.login({
      email: form.email.trim(),
      password: form.password,
      subsidiaryId: form.subsidiaryId
    });

    // Success feedback
    toast.success("Welcome back! Login successful.");

    // Store remember me preference
    if (form.rememberMe) {
      localStorage.setItem("rememberMe", "true");
      localStorage.setItem("lastEmail", form.email.trim());
      localStorage.setItem("lastSubsidiary", form.subsidiaryId);
    } else {
      localStorage.removeItem("rememberMe");
      localStorage.removeItem("lastEmail");
      localStorage.removeItem("lastSubsidiary");
    }

    // Wait a moment to ensure all auth state is properly updated
    await new Promise(resolve => setTimeout(resolve, 300))

    // Check if password reset is required
    if (loginResult.requiresPasswordReset) {
      toast.info("Please change your password to continue.");
      router.push({ name: "PasswordReset" });
    } else {
      // Redirect to dashboard
      router.push({ name: "Dashboard" });
    }
  } catch (error) {
    console.error("Login error:", error);

    // Handle different error types
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.message || "Login failed";

      switch (status) {
        case 401:
          errorMessage.value =
            "Invalid credentials or subsidiary access. Please try again.";
          break;
        case 429:
          errorMessage.value =
            "Too many login attempts. Please try again later.";
          break;
        case 500:
          errorMessage.value = "Server error. Please try again later.";
          break;
        default:
          errorMessage.value = message;
      }
    } else if (error.request) {
      errorMessage.value =
        "Unable to connect to server. Please check your internet connection.";
    } else {
      errorMessage.value = "An unexpected error occurred. Please try again.";
    }

    // Also show toast for immediate feedback
    toast.error(errorMessage.value);
  } finally {
    loading.value = false;
  }
};

// Load remembered credentials on mount
onMounted(() => {
  loadSubsidiaries();

  const rememberMe = localStorage.getItem("rememberMe");
  const lastEmail = localStorage.getItem("lastEmail");
  const lastSubsidiary = localStorage.getItem("lastSubsidiary");

  if (rememberMe === "true" && lastEmail) {
    form.email = lastEmail;
    form.rememberMe = true;
    if (lastSubsidiary) {
      form.subsidiaryId = lastSubsidiary;
    }
  }
});
</script>
