<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Mobile Navigation -->
    <MobileNavigation
      title="Audio Files"
      subtitle="Manage your collection"
    />

    <!-- Desktop Header Section -->
    <div class="hidden lg:block relative overflow-hidden bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-r from-indigo-50/50 to-purple-50/50"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between py-8">
          <!-- Brand & Title -->
          <div class="flex items-center space-x-6 mb-6 lg:mb-0">
            <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold font-display bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                Audio Files
              </h1>
              <p class="text-gray-600 font-medium">Manage and organize your audio collection</p>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="flex flex-wrap gap-4">
            <div class="bg-white/70 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20 shadow-sm">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-600">Total Files</p>
                  <p class="text-lg font-bold text-gray-900">{{ filesStore.files.length }}</p>
                </div>
              </div>
            </div>
            <div
              class="bg-white/70 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20 shadow-sm transition-all duration-300"
              :class="[
                filesStore.selectedCount > 0
                  ? 'ring-2 ring-indigo-400 ring-offset-2 shadow-lg'
                  : ''
              ]"
            >
              <div class="flex items-center">
                <div
                  class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-all duration-300"
                  :class="[
                    filesStore.selectedCount > 0
                      ? 'bg-indigo-100 animate-pulse'
                      : 'bg-green-100'
                  ]"
                >
                  <svg
                    class="w-4 h-4 transition-colors duration-300"
                    :class="[
                      filesStore.selectedCount > 0
                        ? 'text-indigo-600'
                        : 'text-green-600'
                    ]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-600">
                    {{ filesStore.selectedCount > 0 ? 'Files Selected' : 'Selected' }}
                  </p>
                  <p
                    class="text-lg font-bold transition-all duration-300"
                    :class="[
                      filesStore.selectedCount > 0
                        ? 'text-indigo-900 scale-110'
                        : 'text-gray-900'
                    ]"
                  >
                    {{ filesStore.selectedCount }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Search Section -->
    <div class="lg:hidden px-4 py-4 bg-white/50 backdrop-blur-sm border-b border-gray-200/50">
      <div class="relative mb-4">
        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        <input
          v-model="searchQuery"
          @input="handleSearchInput"
          type="text"
          placeholder="Search audio files..."
          class="w-full pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-sm"
        />
      </div>

      <!-- Mobile Stats & Filter Toggle -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          {{ filteredFiles.length }} file{{ filteredFiles.length !== 1 ? 's' : '' }}
          <span v-if="filesStore.selectedCount > 0" class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs font-medium">
            {{ filesStore.selectedCount }} selected
          </span>
        </div>

        <button
          @click="showMobileFilters = !showMobileFilters"
          class="flex items-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors text-sm font-medium"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
          <span>Filters</span>
          <span v-if="activeFiltersCount > 0" class="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">{{ activeFiltersCount }}</span>
        </button>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-4 lg:py-8 px-4 sm:px-6 lg:px-8">
      <!-- Action Bar -->
      <div class="mb-8">
        <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-50/30 to-purple-50/30"></div>

          <div class="relative p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <!-- Search Bar -->
              <div class="flex-1 max-w-2xl">
                <div class="relative group">
                  <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                  </div>
                  <input
                    v-model="searchQuery"
                    @input="handleSearchInput"
                    @focus="showSuggestions = true"
                    @blur="hideSuggestions"
                    @keydown="handleSearchKeydown"
                    type="text"
                    placeholder="Search your audio collection..."
                    class="block w-full pl-12 pr-12 py-3 border border-gray-200 rounded-xl bg-white/70 backdrop-blur-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent shadow-sm transition-all duration-200 text-base"
                    autocomplete="off"
                  />

                  <!-- Clear search button -->
                  <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      @click="clearSearch"
                      class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
                    >
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>

                  <!-- Search Suggestions Dropdown -->
                  <div
                    v-if="showSuggestions && searchSuggestions.length > 0"
                    class="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-60 overflow-y-auto"
                  >
                    <div class="p-2">
                      <div class="text-xs font-medium text-gray-500 px-3 py-2">Suggestions</div>
                      <button
                        v-for="(suggestion, index) in searchSuggestions"
                        :key="suggestion"
                        @mousedown="selectSuggestion(suggestion)"
                        :class="[
                          'w-full text-left px-3 py-2 text-sm rounded-lg transition-colors',
                          index === selectedSuggestionIndex
                            ? 'bg-indigo-50 text-indigo-700'
                            : 'text-gray-700 hover:bg-gray-50'
                        ]"
                      >
                        <svg class="w-4 h-4 inline mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        {{ suggestion }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex flex-wrap gap-3">
                <button
                  v-if="filesStore.selectedCount > 0"
                  @click="downloadSelected"
                  class="inline-flex items-center px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  :disabled="downloading"
                >
                  <svg v-if="downloading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                  </svg>
                  {{ downloading ? 'Creating Archive...' : `Download ${filesStore.selectedCount > 1 ? 'as ZIP' : 'Selected'}` }}
                </button>

                <button
                  @click="navigateToUpload"
                  class="inline-flex items-center px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  Upload Files
                </button>

                <button
                  @click="retryLoadFiles"
                  class="inline-flex items-center px-4 py-3 bg-white/70 backdrop-blur-sm text-gray-700 font-semibold rounded-xl border border-gray-200 hover:bg-white hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Filters -->
      <div class="mb-8">
        <AdvancedFilters />
      </div>

      <!-- Sort & Actions Controls -->
      <div class="mb-8">
        <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <div class="absolute inset-0 bg-gradient-to-r from-gray-50/30 to-slate-50/30"></div>

          <div class="relative p-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <!-- Sort Controls -->
              <div class="flex items-center gap-3">
                <span class="text-sm font-medium text-gray-700">Sort by:</span>

                <div class="relative">
                  <select
                    v-model="sortBy"
                    @change="handleSort"
                    class="appearance-none bg-white/70 backdrop-blur-sm border border-gray-200 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent cursor-pointer"
                  >
                    <option value="name">📝 Name</option>
                    <option value="size">📊 Size</option>
                    <option value="lastModified">📅 Date</option>
                  </select>
                  <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </div>
                </div>

                <button
                  @click="toggleSortOrder"
                  class="inline-flex items-center px-3 py-2 bg-white/70 backdrop-blur-sm border border-gray-200 rounded-lg text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200"
                  :title="sortOrder === 'asc' ? 'Sort Ascending' : 'Sort Descending'"
                >
                  <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': sortOrder === 'desc' }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </button>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center gap-3">
                <button
                  v-if="filesStore.selectedCount > 0"
                  @click="downloadSelected"
                  :disabled="downloading"
                  class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <svg v-if="downloading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  {{ downloading ? 'Downloading...' : `Download ${filesStore.selectedCount} file${filesStore.selectedCount > 1 ? 's' : ''}` }}
                </button>

                <button
                  @click="filesStore.selectAllFiles"
                  class="inline-flex items-center px-4 py-2 backdrop-blur-sm border rounded-lg text-sm font-medium focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105"
                  :class="[
                    filesStore.isAllSelected
                      ? 'bg-indigo-100 border-indigo-300 text-indigo-700 hover:bg-indigo-200 shadow-md'
                      : 'bg-white/70 border-gray-200 text-gray-700 hover:bg-white hover:shadow-md'
                  ]"
                >
                  <svg
                    class="w-4 h-4 mr-2 transition-transform duration-200"
                    :class="[filesStore.isAllSelected ? 'rotate-180' : '']"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      :d="filesStore.isAllSelected
                        ? 'M6 18L18 6M6 6l12 12'
                        : 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'"
                    ></path>
                  </svg>
                  <span class="transition-all duration-200">
                    {{ filesStore.isAllSelected ? 'Deselect All' : 'Select All' }}
                    <span v-if="filesStore.selectedCount > 0 && !filesStore.isAllSelected" class="ml-1 text-xs opacity-75">
                      ({{ filesStore.selectedCount }})
                    </span>
                  </span>
                </button>

                <!-- Keyboard shortcuts help -->
                <div class="relative group">
                  <button
                    class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-white/50 transition-all duration-200"
                    title="Keyboard shortcuts"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </button>

                  <!-- Tooltip -->
                  <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    <div class="space-y-1">
                      <div><kbd class="px-1 py-0.5 bg-gray-700 rounded text-xs">Ctrl+A</kbd> Select All</div>
                      <div><kbd class="px-1 py-0.5 bg-gray-700 rounded text-xs">Esc</kbd> Clear Selection</div>
                      <div><kbd class="px-1 py-0.5 bg-gray-700 rounded text-xs">Ctrl+D</kbd> Download Selected</div>
                    </div>
                    <!-- Arrow -->
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Messages -->
      <div class="space-y-4 mb-8">
        <!-- S3 Status Banner -->
        <div v-if="!filesStore.isS3Available && !filesStore.loading" class="relative overflow-hidden bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl shadow-lg">
          <div class="absolute inset-0 bg-gradient-to-r from-amber-100/20 to-yellow-100/20"></div>
          <div class="relative p-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-xl flex items-center justify-center">
                  <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-amber-900">S3 Storage Not Available</h3>
                <div class="mt-2 text-base text-amber-800">
                  <p>{{ filesStore.s3Status.message || 'AWS S3 storage is not configured. File operations are currently unavailable.' }}</p>
                  <p class="mt-1">Please configure AWS credentials to enable file management features.</p>
                </div>
                <div class="mt-4">
                  <button
                    @click="checkS3Status"
                    class="inline-flex items-center px-4 py-2 bg-amber-100 hover:bg-amber-200 border border-amber-300 rounded-lg text-sm font-semibold text-amber-900 transition-all duration-200 transform hover:scale-105"
                    :disabled="checkingS3"
                  >
                    <svg v-if="checkingS3" class="animate-spin -ml-1 mr-2 h-4 w-4 text-amber-900" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ checkingS3 ? 'Checking...' : 'Check S3 Status' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading/Error Display -->
        <div v-if="filesStore.hasError && !filesStore.loading" class="relative overflow-hidden border rounded-2xl shadow-lg"
             :class="filesStore.error.type === 'loading' ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'">
          <div class="absolute inset-0"
               :class="filesStore.error.type === 'loading' ? 'bg-gradient-to-r from-blue-100/20 to-indigo-100/20' : 'bg-gradient-to-r from-red-100/20 to-rose-100/20'"></div>
          <div class="relative p-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 rounded-xl flex items-center justify-center"
                     :class="filesStore.error.type === 'loading' ? 'bg-gradient-to-r from-blue-500 to-indigo-500' : 'bg-gradient-to-r from-red-500 to-rose-500'">
                  <!-- Loading icon for cache miss -->
                  <svg v-if="filesStore.error.type === 'loading'" class="h-5 w-5 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <!-- Error icon -->
                  <svg v-else class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold"
                    :class="filesStore.error.type === 'loading' ? 'text-blue-900' : 'text-red-900'">
                  {{ filesStore.error.type === 'loading' ? 'Loading Files from S3' : 'Error Loading Files' }}
                </h3>
                <div class="mt-2 text-base"
                     :class="filesStore.error.type === 'loading' ? 'text-blue-800' : 'text-red-800'">
                  <p>{{ filesStore.error.message }}</p>
                  <p v-if="filesStore.error.details" class="mt-1 text-sm opacity-80">{{ filesStore.error.details }}</p>
                </div>
                <div class="mt-4 flex gap-3">
                  <button
                    @click="retryLoadFiles"
                    class="inline-flex items-center px-4 py-2 border rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105"
                    :class="filesStore.error.type === 'loading' ?
                      'bg-blue-100 hover:bg-blue-200 border-blue-300 text-blue-900' :
                      'bg-red-100 hover:bg-red-200 border-red-300 text-red-900'"
                    :disabled="filesStore.loading"
                  >
                    <svg v-if="filesStore.loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ filesStore.loading ? 'Loading...' : 'Retry' }}
                  </button>

                  <!-- Sync button for manual refresh -->
                  <button
                    v-if="filesStore.error.type !== 'loading'"
                    @click="syncFiles"
                    class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-lg text-sm font-semibold text-gray-900 transition-all duration-200 transform hover:scale-105"
                    :disabled="filesStore.loading"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Force Sync
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Files Content -->
      <div class="space-y-6">
        <!-- Loading State -->
        <div v-if="filesStore.loading" class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <LoadingState
            type="cards"
            message="Loading your audio files"
            submessage="Please wait while we fetch your collection..."
            :card-count="6"
            size="large"
          />
        </div>

        <!-- Demo Files (when S3 is not available) -->
        <div v-else-if="!filesStore.isS3Available && !filesStore.hasError">
          <DemoFileList />
        </div>

        <!-- Empty State -->
        <div v-else-if="!filesStore.hasFiles && !filesStore.hasError" class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <div class="absolute inset-0 bg-gradient-to-r from-gray-50/30 to-slate-50/30"></div>
          <div class="relative text-center py-16">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl mb-6">
              <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No audio files found</h3>
            <p class="text-gray-600 mb-6">Try adjusting your search criteria or upload some files to get started.</p>
            <button class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Upload Your First Files
            </button>
          </div>
        </div>

        <!-- Files List - Mobile and Desktop -->
        <div v-else-if="filesStore.hasFiles">
          <!-- Mobile Files List -->
          <MobileFileList
            :files="filteredFiles"
            :selected-files="filesStore.selectedFiles"
            @select="filesStore.selectFile"
            @play="handlePlay"
            @download="handleDownload"
            @delete="handleDelete"
            @show-metadata="handleShowMetadata"
            class="lg:hidden"
          />

          <!-- Desktop Files List -->
          <div class="hidden lg:block relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <div class="absolute inset-0 bg-gradient-to-r from-white/30 to-gray-50/30"></div>

          <!-- Table Header -->
          <div
            class="relative border-b border-gray-200/50 backdrop-blur-sm transition-all duration-300"
            :class="[
              filesStore.selectedCount > 0
                ? 'bg-gradient-to-r from-indigo-50/80 to-purple-50/80'
                : 'bg-gradient-to-r from-gray-50/50 to-white/50'
            ]"
          >
            <div class="px-6 py-4">
              <div class="flex items-center justify-between">
                <label class="inline-flex items-center cursor-pointer group/header">
                  <div class="relative">
                    <input
                      type="checkbox"
                      :checked="filesStore.isAllSelected"
                      @change="filesStore.selectAllFiles"
                      class="h-5 w-5 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 border-2 border-gray-300 rounded transition-all duration-200 transform group-hover/header:scale-110"
                      :class="[
                        filesStore.isAllSelected
                          ? 'bg-indigo-600 border-indigo-600 shadow-lg'
                          : 'bg-white hover:border-indigo-400'
                      ]"
                    />
                    <!-- Header selection indicator -->
                    <div
                      v-if="filesStore.isAllSelected"
                      class="absolute inset-0 flex items-center justify-center pointer-events-none"
                    >
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </div>
                  </div>
                  <span
                    class="ml-3 text-sm font-semibold uppercase tracking-wider transition-colors duration-200"
                    :class="[
                      filesStore.selectedCount > 0
                        ? 'text-indigo-700'
                        : 'text-gray-700'
                    ]"
                  >
                    Select All Files
                  </span>
                </label>

                <!-- Selection status indicator -->
                <div
                  v-if="filesStore.selectedCount > 0"
                  class="flex items-center text-sm text-indigo-600 animate-fade-in"
                >
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  {{ filesStore.selectedCount }} of {{ filesStore.files.length }} selected
                </div>
              </div>
            </div>
          </div>

          <!-- Files List -->
          <div class="relative divide-y divide-gray-200/50">
            <AudioFileItem
              v-for="file in filesStore.files"
              :key="file.key"
              :file="file"
              :selected="filesStore.isFileSelected(file)"
              @select="filesStore.selectFile(file)"
              @download="(file) => downloadFile(file)"
              @play="(file) => playFile(file)"
            />
          </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="filesStore.hasFiles" class="flex justify-center">
          <Pagination
            :current-page="filesStore.pagination.currentPage"
            :total-pages="filesStore.pagination.totalPages"
            :total-items="filesStore.pagination.totalItems"
            :items-per-page="filesStore.pagination.itemsPerPage"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- Audio Player Modal -->
    <AudioPlayerModal
      v-if="currentAudio"
      :file="currentAudio"
      @close="currentAudio = null"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useFilesStore } from '@/stores/files'
import { useToast } from 'vue-toastification'
import { useErrorHandler } from '@/composables/useErrorHandler'
import AudioFileItem from '@/components/AudioFileItem.vue'
import Pagination from '@/components/Pagination.vue'
import AudioPlayerModal from '@/components/AudioPlayerModal.vue'
import DemoFileList from '@/components/DemoFileList.vue'
import AdvancedFilters from '@/components/AdvancedFilters.vue'
import MobileNavigation from '@/components/MobileNavigation.vue'
import MobileFileList from '@/components/MobileFileList.vue'
import LoadingState from '@/components/LoadingState.vue'
import ErrorState from '@/components/ErrorState.vue'

// Simple debounce function
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const filesStore = useFilesStore()
const toast = useToast()
const router = useRouter()
const errorHandler = useErrorHandler()

const searchQuery = ref('')
const sortBy = ref('name')
const sortOrder = ref('asc')
const downloading = ref(false)
const currentAudio = ref(null)
const checkingS3 = ref(false)
const showSuggestions = ref(false)
const searchSuggestions = ref([])
const selectedSuggestionIndex = ref(-1)
const showMobileFilters = ref(false)

const handleSearch = debounce(async () => {
  try {
    await filesStore.searchFiles(searchQuery.value)
  } catch (error) {
    console.error('Search error:', error)
  }
}, 300)

const handleSearchInput = debounce(async () => {
  // Perform search
  await handleSearch()

  // Get suggestions if query is not empty
  if (searchQuery.value.trim().length > 0) {
    try {
      const suggestions = await filesStore.getSearchSuggestions(searchQuery.value.trim(), 8)
      searchSuggestions.value = suggestions.filter(s =>
        s.toLowerCase() !== searchQuery.value.toLowerCase()
      )
    } catch (error) {
      console.error('Error getting search suggestions:', error)
      searchSuggestions.value = []
    }
  } else {
    searchSuggestions.value = []
  }
}, 300)

const handleSearchKeydown = (event) => {
  if (!showSuggestions.value || searchSuggestions.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        searchSuggestions.value.length - 1
      )
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1)
      break
    case 'Enter':
      if (selectedSuggestionIndex.value >= 0) {
        event.preventDefault()
        selectSuggestion(searchSuggestions.value[selectedSuggestionIndex.value])
      }
      break
    case 'Escape':
      showSuggestions.value = false
      selectedSuggestionIndex.value = -1
      break
  }
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  handleSearch()
}

const hideSuggestions = () => {
  // Delay hiding to allow click events on suggestions
  setTimeout(() => {
    showSuggestions.value = false
    selectedSuggestionIndex.value = -1
  }, 150)
}

const clearSearch = () => {
  searchQuery.value = ''
  searchSuggestions.value = []
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  handleSearch()
}

const handleSort = async () => {
  try {
    await filesStore.sortFiles(sortBy.value, sortOrder.value)
  } catch (error) {
    console.error('Sort error:', error)
  }
}

const toggleSortOrder = async () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  await handleSort()
}

const handlePageChange = async (page) => {
  try {
    await filesStore.fetchFiles(page)
  } catch (error) {
    console.error('Pagination error:', error)
  }
}

const downloadFile = async (file) => {
  try {
    console.log('downloadFile called with:', file)
    await filesStore.downloadFile(file)
    toast.success(`Downloading ${file.name}`)
  } catch (error) {
    console.error('Download error:', error)
    toast.error('Failed to download file')
  }
}

const downloadSelected = async () => {
  try {
    downloading.value = true
    const selectedCount = filesStore.selectedCount

    if (selectedCount > 1) {
      toast.info(`Creating ZIP archive with ${selectedCount} files...`)
    }

    await filesStore.downloadSelectedFiles()

    if (selectedCount > 1) {
      toast.success(`ZIP archive with ${selectedCount} files is downloading`)
    } else {
      toast.success(`Downloading ${filesStore.selectedFiles[0]?.name || 'file'}`)
    }

    filesStore.clearSelection()
  } catch (error) {
    console.error('Bulk download error:', error)
    toast.error('Failed to download files')
  } finally {
    downloading.value = false
  }
}

const playFile = (file) => {
  console.log('playFile called with:', file)
  currentAudio.value = file
}

const checkS3Status = async () => {
  try {
    checkingS3.value = true
    await filesStore.checkS3Status()

    if (filesStore.isS3Available) {
      toast.success('S3 connection successful!')
      // Retry loading files if S3 is now available
      await filesStore.fetchFiles()
    } else {
      toast.warning('S3 is still not available')
    }
  } catch (error) {
    console.error('S3 status check error:', error)
    toast.error('Failed to check S3 status')
  } finally {
    checkingS3.value = false
  }
}

const retryLoadFiles = async () => {
  try {
    await filesStore.fetchFiles()
    if (!filesStore.hasError) {
      toast.success('Files loaded successfully!')
    }
  } catch (error) {
    console.error('Retry load files error:', error)
    toast.error('Failed to load files')
  }
}

const syncFiles = async () => {
  try {
    toast.info('Syncing files from S3...')
    await filesStore.syncFiles()
    toast.success('Files synced successfully!')
  } catch (error) {
    console.error('Sync files error:', error)
    toast.error('Failed to sync files')
  }
}

const navigateToUpload = () => {
  router.push('/upload')
}

// Keyboard shortcuts for selection
const handleKeydown = (event) => {
  // Ctrl+A or Cmd+A to select all
  if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
    event.preventDefault()
    if (!filesStore.isAllSelected) {
      filesStore.selectAllFiles()
      toast.info(`Selected all ${filesStore.files.length} files`)
    }
  }

  // Escape to clear selection
  if (event.key === 'Escape' && filesStore.selectedCount > 0) {
    filesStore.clearSelection()
    toast.info('Selection cleared')
  }

  // Ctrl+D or Cmd+D to download selected
  if ((event.ctrlKey || event.metaKey) && event.key === 'd' && filesStore.selectedCount > 0) {
    event.preventDefault()
    downloadSelected()
  }
}

onMounted(async () => {
  try {
    await filesStore.fetchFiles()
  } catch (error) {
    console.error('Error loading files:', error)
    // Don't show toast error here as the error display will handle it
  }

  // Add keyboard event listeners
  document.addEventListener('keydown', handleKeydown)
})

// Computed properties for mobile
const filteredFiles = computed(() => {
  return filesStore.files || []
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (searchQuery.value.trim()) count++
  // Add other filter counts here
  return count
})

// Mobile-specific methods
const handlePlay = (file) => {
  playFile(file)
}

const handleDownload = (file) => {
  downloadFile(file)
}

const handleDelete = (file) => {
  // Add delete functionality
  console.log('Delete file:', file)
  toast.info('Delete functionality coming soon')
}

const handleShowMetadata = (file) => {
  // Add metadata display functionality
  console.log('Show metadata:', file)
  toast.info('Metadata display coming soon')
}

onUnmounted(() => {
  // Remove keyboard event listeners
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Enhanced checkbox animations */
input[type="checkbox"]:checked {
  animation: checkbox-bounce 0.2s ease-in-out;
}

@keyframes checkbox-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Selection highlight animation */
.selection-highlight {
  animation: selection-pulse 0.5s ease-in-out;
}

@keyframes selection-pulse {
  0% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(99, 102, 241, 0); }
  100% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0); }
}
</style>
