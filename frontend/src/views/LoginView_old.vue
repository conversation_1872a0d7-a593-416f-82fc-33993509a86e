<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
          <div
            class="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg"
          >
            <svg
              class="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              ></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Login to Your Account</h2>
        </div>

        <!-- Error Alert -->
        <div
          v-if="errorMessage"
          class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 font-medium">{{ errorMessage }}</p>
            </div>
            <div class="ml-auto pl-3">
              <button
                @click="errorMessage = ''"
                class="text-red-400 hover:text-red-600 transition-colors"
              >
                <svg
                  class="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <form class="space-y-6" @submit.prevent="handleLogin">
          <!-- Email/Phone Field -->
          <div class="space-y-2">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                  />
                </svg>
              </div>
              <input
                id="username"
                v-model="form.username"
                name="username"
                type="text"
                required
                :class="[
                  'w-full pl-12 pr-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.username ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Email / Phone number"
                @blur="validateUsername"
                @input="clearError('username')"
              />
            </div>
            <p
              v-if="errors.username"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.username }}
            </p>
          </div>

          <!-- Password Field -->
          <div class="space-y-2">
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"
                  />
                </svg>
              </div>
              <input
                id="password"
                v-model="form.password"
                name="password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full pl-12 pr-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.password ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Password"
                @blur="validatePassword"
                @input="clearError('password')"
              />
            </div>
            <p
              v-if="errors.password"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.password }}
            </p>
          </div>

          <!-- Forgot Password Link -->
          <div class="text-left">
            <a
              href="#"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              Forgot password?
            </a>
          </div>

          <!-- Remember Me -->
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.rememberMe"
              name="remember-me"
              type="checkbox"
              class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
            />
            <label for="remember-me" class="ml-3 text-gray-700 font-medium"
              >Remember me</label
            >
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-full transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
          >
            <svg
              v-if="loading"
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ loading ? "Signing in..." : "Login" }}
          </button>
        </form>
      </div>
    </div>

    <!-- Right Side - Illustration -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
      <!-- Background with Purple Gradient -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600"
      ></div>

      <!-- Curved Background Elements -->
      <div class="absolute inset-0">
        <!-- Large curved shape -->
        <div
          class="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full transform translate-x-32 -translate-y-32"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-80 h-80 bg-white/10 rounded-full transform -translate-x-24 translate-y-24"
        ></div>
        <!-- Smaller floating elements -->
        <div
          class="absolute top-1/4 left-1/4 w-16 h-16 bg-white/20 rounded-full animate-pulse"
        ></div>
        <div
          class="absolute bottom-1/3 right-1/4 w-12 h-12 bg-white/20 rounded-full animate-bounce"
        ></div>
      </div>

      <!-- Curved Design Elements -->
      <div class="absolute inset-0 z-5">
        <!-- Large curved shape like in the reference -->
        <svg
          class="absolute top-0 left-0 w-full h-full"
          viewBox="0 0 400 600"
          fill="none"
        >
          <path
            d="M50 100 Q200 50 350 150 Q300 300 150 250 Q100 200 50 100 Z"
            fill="rgba(59, 130, 246, 0.3)"
            stroke="rgba(59, 130, 246, 0.5)"
            stroke-width="3"
          />
          <path
            d="M80 400 Q250 350 380 450 Q320 550 180 500 Q120 450 80 400 Z"
            fill="rgba(147, 197, 253, 0.2)"
            stroke="rgba(147, 197, 253, 0.4)"
            stroke-width="2"
          />
        </svg>
      </div>

      <!-- Main Illustration Content -->
      <div
        class="relative z-10 flex flex-col justify-center items-center p-12 text-white"
      >
        <!-- Audio Management Dashboard Mockup -->
        <div class="relative mb-8">
          <!-- Main Dashboard Window -->
          <div
            class="w-80 h-96 bg-white rounded-2xl shadow-2xl transform rotate-2 relative overflow-hidden"
          >
            <!-- Window Header -->
            <div
              class="h-16 bg-gray-100 flex items-center px-6 border-b border-gray-200"
            >
              <div class="flex space-x-2">
                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
              </div>
              <div class="ml-4 text-sm font-medium text-gray-700">
                Audio Vault Dashboard
              </div>
            </div>

            <!-- Dashboard Content -->
            <div class="p-6">
              <!-- Audio File List -->
              <div class="space-y-3 mb-6">
                <!-- File Item 1 -->
                <div
                  class="flex items-center space-x-3 p-2 bg-purple-50 rounded-lg"
                >
                  <div
                    class="w-8 h-8 bg-purple-200 rounded flex items-center justify-center"
                  >
                    <svg
                      class="w-4 h-4 text-purple-600"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"
                      />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-2 bg-purple-300 rounded w-24 mb-1"></div>
                    <div class="h-1 bg-gray-300 rounded w-16"></div>
                  </div>
                  <div class="w-12 h-2 bg-purple-400 rounded"></div>
                </div>

                <!-- File Item 2 -->
                <div
                  class="flex items-center space-x-3 p-2 bg-blue-50 rounded-lg"
                >
                  <div
                    class="w-8 h-8 bg-blue-200 rounded flex items-center justify-center"
                  >
                    <svg
                      class="w-4 h-4 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"
                      />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-2 bg-blue-300 rounded w-32 mb-1"></div>
                    <div class="h-1 bg-gray-300 rounded w-20"></div>
                  </div>
                  <div class="w-16 h-2 bg-blue-400 rounded"></div>
                </div>

                <!-- File Item 3 -->
                <div
                  class="flex items-center space-x-3 p-2 bg-green-50 rounded-lg"
                >
                  <div
                    class="w-8 h-8 bg-green-200 rounded flex items-center justify-center"
                  >
                    <svg
                      class="w-4 h-4 text-green-600"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"
                      />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-2 bg-green-300 rounded w-28 mb-1"></div>
                    <div class="h-1 bg-gray-300 rounded w-18"></div>
                  </div>
                  <div class="w-14 h-2 bg-green-400 rounded"></div>
                </div>
              </div>

              <!-- Audio Waveform Visualization -->
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-end justify-center space-x-1 h-16">
                  <div
                    class="w-1 bg-purple-400 rounded-full"
                    style="height: 20%"
                  ></div>
                  <div
                    class="w-1 bg-purple-500 rounded-full"
                    style="height: 60%"
                  ></div>
                  <div
                    class="w-1 bg-purple-600 rounded-full"
                    style="height: 100%"
                  ></div>
                  <div
                    class="w-1 bg-purple-500 rounded-full"
                    style="height: 40%"
                  ></div>
                  <div
                    class="w-1 bg-purple-400 rounded-full"
                    style="height: 80%"
                  ></div>
                  <div
                    class="w-1 bg-purple-600 rounded-full"
                    style="height: 30%"
                  ></div>
                  <div
                    class="w-1 bg-purple-500 rounded-full"
                    style="height: 90%"
                  ></div>
                  <div
                    class="w-1 bg-purple-400 rounded-full"
                    style="height: 50%"
                  ></div>
                  <div
                    class="w-1 bg-purple-600 rounded-full"
                    style="height: 70%"
                  ></div>
                  <div
                    class="w-1 bg-purple-500 rounded-full"
                    style="height: 35%"
                  ></div>
                  <div
                    class="w-1 bg-purple-400 rounded-full"
                    style="height: 85%"
                  ></div>
                  <div
                    class="w-1 bg-purple-600 rounded-full"
                    style="height: 45%"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Second Window (Behind) -->
          <div
            class="absolute -top-4 -left-4 w-80 h-96 bg-white/90 rounded-2xl shadow-xl transform -rotate-1 -z-10"
          >
            <div
              class="h-16 bg-gray-50 rounded-t-2xl border-b border-gray-200"
            ></div>
          </div>

          <!-- Third Window (Behind) -->
          <div
            class="absolute -top-8 -left-8 w-80 h-96 bg-white/80 rounded-2xl shadow-lg transform rotate-1 -z-20"
          >
            <div class="h-16 bg-gray-50 rounded-t-2xl"></div>
          </div>

          <!-- Floating Microphone Icon -->
          <div
            class="absolute -top-6 -right-6 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center animate-pulse"
          >
            <svg
              class="w-8 h-8 text-purple-600"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"
              />
              <path
                d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"
              />
            </svg>
          </div>

          <!-- Headphones Icon -->
          <div
            class="absolute -bottom-6 -left-6 w-14 h-14 bg-white rounded-full shadow-lg flex items-center justify-center"
          >
            <svg
              class="w-7 h-7 text-purple-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
              ></path>
            </svg>
          </div>

          <!-- Audio Wave Indicators -->
          <div class="absolute -bottom-8 -right-8 flex space-x-1">
            <div
              class="w-2 h-6 bg-white/80 rounded-full animate-bounce"
              style="animation-delay: 0s"
            ></div>
            <div
              class="w-2 h-8 bg-white/80 rounded-full animate-bounce"
              style="animation-delay: 0.1s"
            ></div>
            <div
              class="w-2 h-4 bg-white/80 rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            ></div>
            <div
              class="w-2 h-7 bg-white/80 rounded-full animate-bounce"
              style="animation-delay: 0.3s"
            ></div>
          </div>
        </div>

        <!-- Text Content -->
        <div class="text-center mt-8">
          <h2 class="text-4xl font-bold mb-8">Audio Vault Dashboard</h2>

          <!-- Feature Icons -->
          <div class="flex justify-center space-x-8 mt-8">
            <!-- Audio Analysis -->
            <div class="text-center">
              <div
                class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-3 backdrop-blur-sm hover:bg-opacity-30 transition-all duration-300"
              >
                <svg
                  class="w-8 h-8 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
                  />
                </svg>
              </div>
              <p class="text-sm text-white text-opacity-80 font-medium">
                Audio Analysis
              </p>
            </div>

            <!-- Cloud Storage -->
            <div class="text-center">
              <div
                class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-3 backdrop-blur-sm hover:bg-opacity-30 transition-all duration-300"
              >
                <svg
                  class="w-8 h-8 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"
                  />
                </svg>
              </div>
              <p class="text-sm text-white text-opacity-80 font-medium">
                Cloud Storage
              </p>
            </div>

            <!-- File Management -->
            <div class="text-center">
              <div
                class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-3 backdrop-blur-sm hover:bg-opacity-30 transition-all duration-300"
              >
                <svg
                  class="w-8 h-8 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"
                  />
                </svg>
              </div>
              <p class="text-sm text-white text-opacity-80 font-medium">
                File Management
              </p>
            </div>
          </div>
        </div>

        <!-- Floating Audio Elements -->
        <div
          class="absolute top-20 left-20 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center animate-bounce"
        >
          <svg
            class="w-6 h-6 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
            />
          </svg>
        </div>

        <div
          class="absolute bottom-32 left-16 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center animate-pulse"
        >
          <svg
            class="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useToast } from "vue-toastification";

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();



// Reactive state
const loading = ref(false);
const showPassword = ref(false);
const errorMessage = ref("");
const testingConnection = ref(false);
const connectionStatus = ref(null);

const form = reactive({
  username: "",
  password: "",
  rememberMe: false,
});

const errors = reactive({
  username: "",
  password: "",
});

// Computed properties
const isFormValid = computed(() => {
  return (
    form.username.trim() !== "" &&
    form.password.trim() !== "" &&
    !errors.username &&
    !errors.password
  );
});

// Validation functions
const validateUsername = () => {
  if (!form.username.trim()) {
    errors.username = "Username is required";
    return false;
  }
  if (form.username.length < 3) {
    errors.username = "Username must be at least 3 characters";
    return false;
  }
  errors.username = "";
  return true;
};

const validatePassword = () => {
  if (!form.password.trim()) {
    errors.password = "Password is required";
    return false;
  }
  if (form.password.length < 6) {
    errors.password = "Password must be at least 6 characters";
    return false;
  }
  errors.password = "";
  return true;
};

const clearError = (field) => {
  errors[field] = "";
  errorMessage.value = "";
};

const validateForm = () => {
  const isUsernameValid = validateUsername();
  const isPasswordValid = validatePassword();
  return isUsernameValid && isPasswordValid;
};

// Login handler with enhanced error handling
const handleLogin = async () => {
  // Clear previous errors
  errorMessage.value = "";

  // Validate form
  if (!validateForm()) {
    errorMessage.value = "Please fix the errors above";
    return;
  }

  try {
    loading.value = true;
    // Attempt login
    const loginResult = await authStore.login({
      email: form.username.trim(), // Backend expects 'email' field
      password: form.password,
    });

    // Success feedback
    toast.success("Welcome back! Login successful.");

    // Store remember me preference
    if (form.rememberMe) {
      localStorage.setItem("rememberMe", "true");
      localStorage.setItem("lastUsername", form.username.trim());
    } else {
      localStorage.removeItem("rememberMe");
      localStorage.removeItem("lastUsername");
    }

    // Check if password reset is required
    if (loginResult.requiresPasswordReset) {
      toast.info("Please change your password to continue.");
      router.push({ name: "PasswordReset" });
    } else {
      // Redirect to dashboard
      router.push({ name: "Dashboard" });
    }
  } catch (error) {
    console.error("Login error:", error);

    // Handle different error types
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.message || "Login failed";

      switch (status) {
        case 401:
          errorMessage.value =
            "Invalid username or password. Please try again.";
          break;
        case 429: 
          errorMessage.value =
            "Too many login attempts. Please try again later.";
          break;
        case 500:
          errorMessage.value = "Server error. Please try again later.";
          break;
        default:
          errorMessage.value = message;
      }
    } else if (error.request) {
      errorMessage.value =
        "Unable to connect to server. Please check your internet connection.";
    } else {
      errorMessage.value = "An unexpected error occurred. Please try again.";
    }

    // Also show toast for immediate feedback
    toast.error(errorMessage.value);
  } finally {
    loading.value = false;
  }
};

// Load remembered credentials on mount
onMounted(() => {
  const rememberMe = localStorage.getItem("rememberMe");
  const lastUsername = localStorage.getItem("lastUsername");

  if (rememberMe === "true" && lastUsername) {
    form.username = lastUsername;
    form.rememberMe = true;
  }

});
</script>
