<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Mobile Navigation -->
    <MobileNavigation
      title="Dashboard"
      subtitle="Overview & stats"
    />

    <!-- Desktop Hero Header Section -->
    <div class="hidden lg:block relative overflow-hidden bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-r from-indigo-50/50 to-purple-50/50"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between py-8">
          <!-- Brand & Welcome -->
          <div class="flex items-center space-x-6 mb-6 lg:mb-0">
            <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold font-display bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                Audio Vault
              </h1>
              <p class="text-gray-600 font-medium">Secure Audio File Management System</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <!-- Search Bar -->
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search files..."
                class="block w-full sm:w-80 pl-12 pr-4 py-3 border border-gray-200 rounded-xl bg-white/70 backdrop-blur-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent shadow-sm transition-all duration-200"
              />
            </div>

            <!-- Upload Button -->
            <router-link
              to="/upload"
              class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Upload Files
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-4 lg:py-8 px-4 sm:px-6 lg:px-8">
      <!-- Welcome Section -->
      <div class="mb-8">
        <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-50/30 to-purple-50/30"></div>
          <div class="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-xl"></div>

          <div class="relative p-8">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-3xl font-bold font-display bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                  Welcome back!
                </h2>
                <p class="text-gray-600 text-lg">Manage your audio files with ease and security.</p>
                <div class="mt-4 flex items-center space-x-6 text-base text-gray-500">
                  <div class="flex items-center">
                    <div :class="loading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'" class="w-2 h-2 rounded-full mr-2"></div>
                    {{ loading ? 'Syncing...' : 'System Online' }}
                  </div>
                  <div class="flex items-center">
                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                    Last sync: {{ getRelativeTime(stats.lastUpdated) }}
                  </div>
                  <div v-if="!loading" class="flex items-center">
                    <button @click="loadDashboardData" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                      <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                      Refresh
                    </button>
                  </div>
                </div>
              </div>
              <div class="hidden lg:block">
                <div class="w-32 h-32 bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 rounded-3xl flex items-center justify-center shadow-inner">
                  <svg class="w-16 h-16 text-indigo-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Stats Cards -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- Total Files Card -->
        <div class="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50"></div>
          <div class="relative p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-base font-semibold text-gray-600 uppercase tracking-wide">Total Files</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ stats.totalFiles }}</p>
                <div class="flex items-center mt-2">
                  <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                  <span class="text-base text-green-600 font-medium">+12% from last month</span>
                </div>
              </div>
              <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-lg">
                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Storage Used Card -->
        <div class="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div class="absolute inset-0 bg-gradient-to-br from-green-50/50 to-emerald-50/50"></div>
          <div class="relative p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-base font-semibold text-gray-600 uppercase tracking-wide">Storage Used</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ formatBytes(stats.totalSize) }}</p>
                <div class="flex items-center mt-2">
                  <div class="w-20 h-2 bg-gray-200 rounded-full mr-2">
                    <div class="h-2 bg-gradient-to-r from-green-400 to-green-500 rounded-full" :style="{ width: `${Math.min((stats.totalSize / (1024 * 1024 * 1024)) * 100 / 10, 100)}%` }"></div>
                  </div>
                  <span class="text-base text-gray-600 font-medium">{{ ((stats.totalSize / (1024 * 1024 * 1024)) * 100 / 10).toFixed(1) }}%</span>
                </div>
              </div>
              <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-lg">
                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Last Updated Card -->
        <div class="card-premium group">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-base font-medium text-gray-600">Last Updated</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatDate(stats.lastUpdated) }}</p>
              <p class="text-sm text-orange-600 font-medium">2 hours ago</p>
            </div>
          </div>
        </div>

        <!-- Active Users Card -->
        <div class="card-premium group">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-base font-medium text-gray-600">Active Users</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.activeUsers }}</p>
              <p class="text-sm text-purple-600 font-medium">Online now</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Storage Usage Chart -->
      <div class="mb-8">
        <div class="card-premium">
          <div class="card-header-premium">
            <h3 class="card-title-premium">Storage Usage</h3>
            <p class="card-subtitle-premium">Monitor your storage consumption and optimize file management</p>
          </div>

          <div class="space-y-4">
            <!-- Storage Progress Bar -->
            <div>
              <div class="flex justify-between text-base font-medium text-gray-700 mb-2">
                <span>Used Storage</span>
                <span>{{ formatBytes(stats.totalSize) }} / 10 GB</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div
                  class="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-500"
                  :style="{ width: `${Math.min((stats.totalSize / (10 * 1024 * 1024 * 1024)) * 100, 100)}%` }"
                ></div>
              </div>
            </div>

            <!-- File Type Breakdown -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <div class="text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                  </svg>
                </div>
                <p class="text-base font-medium text-gray-900">MP3</p>
                <p class="text-sm text-gray-600">{{ stats.fileTypes?.mp3 || 0 }} files</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                  </svg>
                </div>
                <p class="text-base font-medium text-gray-900">WAV</p>
                <p class="text-sm text-gray-600">{{ stats.fileTypes?.wav || 0 }} files</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                  </svg>
                </div>
                <p class="text-base font-medium text-gray-900">FLAC</p>
                <p class="text-sm text-gray-600">{{ stats.fileTypes?.flac || 0 }} files</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                  </svg>
                </div>
                <p class="text-base font-medium text-gray-900">Other</p>
                <p class="text-sm text-gray-600">{{ Object.values(stats.fileTypes || {}).reduce((sum, count) => sum + count, 0) - (stats.fileTypes?.mp3 || 0) - (stats.fileTypes?.wav || 0) - (stats.fileTypes?.flac || 0) }} files</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card-premium mb-8">
        <div class="card-header-premium">
          <h3 class="card-title-premium">Quick Actions</h3>
          <p class="card-subtitle-premium">Streamline your workflow with these common tasks</p>
        </div>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Browse Files Action -->
          <router-link
            to="/files"
            class="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="relative">
              <div class="w-14 h-14 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200">
                <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
                Browse Files
              </h3>
              <p class="text-gray-600 group-hover:text-gray-700">
                View and manage your audio files with advanced filtering and search
              </p>
              <div class="mt-4 flex items-center text-indigo-600 font-medium">
                <span class="text-base">Explore now</span>
                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </router-link>

          <!-- Upload Files Action -->
          <router-link
            to="/upload"
            class="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer block"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="relative">
              <div class="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200">
                <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">
                Upload Files
              </h3>
              <p class="text-gray-600 group-hover:text-gray-700">
                Drag and drop or browse to upload your audio files securely
              </p>
              <div class="mt-4 flex items-center text-green-600 font-medium">
                <span class="text-base">Start Upload</span>
                <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </router-link>

          <!-- Analytics Action -->
          <router-link to="/analytics" class="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-purple-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer block">
            <div class="absolute inset-0 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="relative">
              <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200">
                <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
                Analytics
              </h3>
              <p class="text-gray-600 group-hover:text-gray-700">
                View detailed usage statistics and insights about your files
              </p>
              <div class="mt-4 flex items-center text-purple-600 font-medium">
                <span class="text-base">View Analytics</span>
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </router-link>

          <!-- Audit Trail Action (Admin/Compliance Only) -->
          <router-link
            v-if="canViewAudit"
            to="/audit"
            class="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-red-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer block"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-red-50 to-orange-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="relative">
              <div class="w-14 h-14 bg-gradient-to-r from-red-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200">
                <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-red-600 transition-colors">
                Audit Trail
              </h3>
              <p class="text-gray-600 group-hover:text-gray-700">
                Monitor security events and compliance activities across the system
              </p>
              <div class="mt-4 flex items-center text-red-600 font-medium">
                <span class="text-base">View Audit Logs</span>
                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </router-link>
        </div>
      </div>

      <!-- Recent Files Section -->
      <div class="card-premium">
        <div class="card-header-premium">
          <h3 class="card-title-premium">Recent Files</h3>
          <p class="card-subtitle-premium">Your most recently accessed audio files</p>
        </div>

        <div class="space-y-4">
          <!-- Loading State -->
          <div v-if="loading" class="space-y-4">
            <div v-for="i in 3" :key="i" class="flex items-center p-4 bg-gray-50 rounded-xl animate-pulse">
              <div class="w-12 h-12 bg-gray-300 rounded-xl mr-4"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-300 rounded w-1/2"></div>
              </div>
              <div class="text-right">
                <div class="h-3 bg-gray-300 rounded w-20 mb-2"></div>
                <div class="h-3 bg-gray-300 rounded w-16"></div>
              </div>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="text-center py-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <p class="text-gray-600 mb-4">{{ error }}</p>
            <button @click="loadDashboardData" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
              Try Again
            </button>
          </div>

          <!-- Empty State -->
          <div v-else-if="recentFiles.length === 0" class="text-center py-8">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <p class="text-gray-600 mb-4">No audio files found</p>
            <router-link to="/upload" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
              Upload Your First File
            </router-link>
          </div>

          <!-- Recent Files List -->
          <div v-else>
            <div v-for="file in recentFiles" :key="file.id" class="flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer">
              <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900">{{ file.name }}</h4>
                <p class="text-base text-gray-600">{{ file.size }} • {{ file.duration || 'Unknown duration' }}</p>
              </div>
              <div class="text-right">
                <p class="text-base text-gray-500">{{ file.lastAccessed }}</p>
                <div class="flex items-center mt-1">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span class="text-sm text-gray-500">Available</span>
                </div>
              </div>
            </div>

            <!-- View All Files Link -->
            <div class="text-center pt-4">
              <router-link to="/files" class="inline-flex items-center text-indigo-600 hover:text-indigo-500 font-medium">
                View all files
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { dashboardAPI } from '@/services/api'
import { useToast } from 'vue-toastification'
import { useAuthStore } from '@/stores/auth'
import MobileNavigation from '@/components/MobileNavigation.vue'

const toast = useToast()
const authStore = useAuthStore()

// Check if user has audit access
const canViewAudit = computed(() => {
  return authStore.user?.roles?.some(role =>
    ['ADMIN', 'COMPLIANCE'].includes(role.code)
  )
})

// Simple date formatter
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Reactive state
const stats = ref({
  totalFiles: 0,
  totalSize: 0,
  lastUpdated: new Date(),
  activeUsers: 1,
  fileTypes: {}
})

const recentFiles = ref([])
const loading = ref(true)
const error = ref(null)

// Auto-refresh interval
let refreshInterval = null

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Get relative time
const getRelativeTime = (date) => {
  const now = new Date()
  const diffMs = now - new Date(date)
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
  return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
}

// Fetch dashboard statistics
const fetchStats = async () => {
  try {
    const response = await dashboardAPI.getStats()
    if (response.data.success) {
      stats.value = {
        ...response.data.data,
        lastUpdated: new Date(response.data.data.lastUpdated)
      }
    }
  } catch (err) {
    console.error('Error fetching stats:', err)
    error.value = 'Failed to load dashboard statistics'
    toast.error('Failed to load dashboard statistics')
  }
}

// Fetch recent files
const fetchRecentFiles = async () => {
  try {
    const response = await dashboardAPI.getRecentFiles(5)
    if (response.data.success) {
      recentFiles.value = response.data.data
    }
  } catch (err) {
    console.error('Error fetching recent files:', err)
    error.value = 'Failed to load recent files'
    toast.error('Failed to load recent files')
  }
}

// Load all dashboard data
const loadDashboardData = async () => {
  loading.value = true
  error.value = null

  try {
    await Promise.all([
      fetchStats(),
      fetchRecentFiles()
    ])
  } catch (err) {
    console.error('Error loading dashboard data:', err)
  } finally {
    loading.value = false
  }
}

// Setup auto-refresh
const setupAutoRefresh = () => {
  // Refresh every 30 seconds
  refreshInterval = setInterval(() => {
    loadDashboardData()
  }, 30000)
}

onMounted(async () => {
  await loadDashboardData()
  setupAutoRefresh()
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
