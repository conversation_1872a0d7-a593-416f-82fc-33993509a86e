<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <router-link
              to="/dashboard"
              class="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to Dashboard
            </router-link>
          </div>
          
          <h1 class="text-xl font-semibold text-gray-900">Upload Audio Files</h1>
          
          <div class="w-32"></div> <!-- Spacer for centering -->
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- S3 Status Check -->
      <div v-if="!s3Available" class="mb-6">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div class="flex-1">
              <h3 class="text-sm font-medium text-yellow-800">S3 Connection Required</h3>
              <p class="text-sm text-yellow-700 mt-1">
                File upload requires S3 to be configured. Please check your S3 connection.
              </p>
            </div>
            <button
              @click="checkS3Status"
              :disabled="checkingS3"
              class="ml-4 px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-md hover:bg-yellow-200 transition-colors disabled:opacity-50"
            >
              {{ checkingS3 ? 'Checking...' : 'Retry' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Advanced Upload Component -->
      <div v-if="s3Available">
        <AdvancedFileUpload
          @upload-complete="handleUploadComplete"
          @cancel="handleCancel"
        />
      </div>

      <!-- Upload Disabled Message -->
      <div v-else class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 text-center">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Upload Unavailable</h3>
        <p class="text-gray-600 mb-4">
          File upload is currently unavailable due to S3 connection issues.
        </p>
        <button
          @click="checkS3Status"
          :disabled="checkingS3"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
        >
          <svg v-if="checkingS3" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ checkingS3 ? 'Checking Connection...' : 'Check S3 Connection' }}
        </button>
      </div>

      <!-- Recent Uploads -->
      <div v-if="recentUploads.length > 0" class="mt-8">
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Recent Uploads
            </h3>
            <p class="text-sm text-gray-600 mt-1">Files uploaded in this session</p>
          </div>
          
          <div class="p-6">
            <div class="space-y-3">
              <div
                v-for="upload in recentUploads"
                :key="upload.id"
                class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200"
              >
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">{{ upload.name }}</p>
                    <p class="text-xs text-gray-500">{{ formatTime(upload.uploadedAt) }}</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Uploaded
                  </span>
                </div>
              </div>
            </div>
            
            <div class="mt-4 text-center">
              <router-link
                to="/files"
                class="inline-flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                View all files
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useFilesStore } from '@/stores/files'
import AdvancedFileUpload from '@/components/AdvancedFileUpload.vue'

const router = useRouter()
const toast = useToast()
const filesStore = useFilesStore()

// State
const s3Available = ref(false)
const checkingS3 = ref(false)
const recentUploads = ref([])

// Check S3 status on mount
onMounted(async () => {
  await checkS3Status()
})

const checkS3Status = async () => {
  try {
    checkingS3.value = true
    await filesStore.checkS3Status()
    s3Available.value = filesStore.isS3Available
    
    if (s3Available.value) {
      toast.success('S3 connection verified!')
    } else {
      toast.warning('S3 connection unavailable')
    }
  } catch (error) {
    console.error('S3 status check error:', error)
    toast.error('Failed to check S3 status')
    s3Available.value = false
  } finally {
    checkingS3.value = false
  }
}

const handleUploadComplete = (result) => {
  // Add successful uploads to recent uploads list
  if (result.success > 0) {
    // In a real app, you'd get the actual upload details from the API response
    // For now, we'll create mock entries
    const newUploads = Array.from({ length: result.success }, (_, i) => ({
      id: Date.now() + i,
      name: `Uploaded file ${i + 1}`,
      uploadedAt: new Date()
    }))
    
    recentUploads.value.unshift(...newUploads)
    
    // Refresh the files list in the store
    filesStore.fetchFiles()
  }
}

const handleCancel = () => {
  router.push('/dashboard')
}

const formatTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}
</script>
