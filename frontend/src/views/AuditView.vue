<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Mobile Navigation -->
    <MobileNavigation
      title="Audit Trail"
      subtitle="Security & compliance monitoring"
    />

    <!-- Desktop Header -->
    <div class="hidden lg:block relative overflow-hidden bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-r from-red-50/50 to-orange-50/50"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between py-8">
          <!-- Brand & Title -->
          <div class="flex items-center space-x-6 mb-6 lg:mb-0">
            <div class="w-16 h-16 bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 rounded-2xl flex items-center justify-center shadow-xl">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold font-display bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                Audit Trail
              </h1>
              <p class="text-gray-600 font-medium">Security & Compliance Monitoring</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <!-- Quick Actions -->
            <div class="flex space-x-2">
              <button
                @click="refreshData"
                :disabled="loading"
                class="inline-flex items-center justify-center px-4 py-2 bg-white/70 backdrop-blur-sm text-gray-700 font-medium rounded-xl hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50"
              >
                <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-4 lg:py-8 px-4 sm:px-6 lg:px-8">
      <!-- Access Check -->
      <div v-if="!hasAuditAccess" class="text-center py-12">
        <div class="max-w-md mx-auto">
          <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h3>
          <p class="text-gray-600 mb-6">
            You need Admin or Compliance Officer privileges to access the audit trail.
          </p>
          <router-link
            to="/dashboard"
            class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
          </router-link>
        </div>
      </div>

      <!-- Audit Dashboard -->
      <div v-else>
        <AuditDashboard />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import MobileNavigation from '@/components/MobileNavigation.vue'
import AuditDashboard from '@/components/AuditDashboard.vue'

export default {
  name: 'AuditView',
  components: {
    MobileNavigation,
    AuditDashboard
  },
  setup() {
    const authStore = useAuthStore()
    const loading = ref(false)

    // Check if user has audit access
    const hasAuditAccess = computed(() => {
      return authStore.user?.roles?.some(role => 
        ['ADMIN', 'COMPLIANCE'].includes(role.code)
      )
    })

    const refreshData = async () => {
      loading.value = true
      try {
        // Trigger refresh in the audit dashboard component
        // This could be done via event bus or refs
        await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate refresh
      } catch (error) {
        console.error('Failed to refresh audit data:', error)
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      // Any initialization logic
    })

    return {
      loading,
      hasAuditAccess,
      refreshData
    }
  }
}
</script>

<style scoped>
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
