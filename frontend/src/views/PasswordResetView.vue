<template>
  <div class="min-h-screen flex bg-white">
    <!-- Left Side - Password Reset Form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8 lg:p-16">
      <div class="w-full max-w-md">
        <!-- Audio Logo/Brand -->
        <div class="text-center mb-10">
          <div
            class="w-20 h-20 bg-gradient-to-r from-purple-600 to-purple-400 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"
          >
            <svg
              class="w-10 h-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
              ></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Audio Vault</h1>
          <p class="text-gray-600 text-lg">Change Your Password</p>
        </div>

        <!-- Alert Message -->
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Password Reset Required</h3>
              <p class="mt-1 text-sm text-yellow-700">
                For security reasons, you must change your password before continuing.
              </p>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div
          v-if="errorMessage"
          class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <form class="space-y-6" @submit.prevent="handlePasswordReset">
          <!-- Current Password Field -->
          <div class="space-y-2">
            <label for="currentPassword" class="block text-sm font-medium text-gray-700">
              Current Password
            </label>
            <div class="relative">
              <input
                id="currentPassword"
                v-model="form.currentPassword"
                name="currentPassword"
                type="password"
                required
                :class="[
                  'w-full px-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.currentPassword ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Enter your current password"
                @input="clearError('currentPassword')"
              />
            </div>
            <p
              v-if="errors.currentPassword"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.currentPassword }}
            </p>
          </div>

          <!-- New Password Field -->
          <div class="space-y-2">
            <label for="newPassword" class="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <div class="relative">
              <input
                id="newPassword"
                v-model="form.newPassword"
                name="newPassword"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'w-full px-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.newPassword ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Enter your new password"
                @input="clearError('newPassword')"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-4 flex items-center"
              >
                <svg
                  :class="showPassword ? 'text-purple-600' : 'text-gray-400'"
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    v-if="!showPassword"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    v-if="!showPassword"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                  <path
                    v-if="showPassword"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                  />
                </svg>
              </button>
            </div>
            
            <!-- Password Requirements -->
            <div class="mt-2 space-y-1">
              <p class="text-xs text-gray-600">Password must contain:</p>
              <div class="grid grid-cols-2 gap-1 text-xs">
                <div :class="passwordChecks.length ? 'text-green-600' : 'text-gray-400'">
                  ✓ At least 8 characters
                </div>
                <div :class="passwordChecks.uppercase ? 'text-green-600' : 'text-gray-400'">
                  ✓ One uppercase letter
                </div>
                <div :class="passwordChecks.lowercase ? 'text-green-600' : 'text-gray-400'">
                  ✓ One lowercase letter
                </div>
                <div :class="passwordChecks.number ? 'text-green-600' : 'text-gray-400'">
                  ✓ One number
                </div>
              </div>
            </div>
            
            <p
              v-if="errors.newPassword"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.newPassword }}
            </p>
          </div>

          <!-- Confirm Password Field -->
          <div class="space-y-2">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <div class="relative">
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                name="confirmPassword"
                type="password"
                required
                :class="[
                  'w-full px-4 py-4 border border-gray-200 rounded-full text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200',
                  errors.confirmPassword ? 'border-red-300 text-red-900' : '',
                ]"
                placeholder="Confirm your new password"
                @input="clearError('confirmPassword')"
              />
            </div>
            <p
              v-if="errors.confirmPassword"
              class="text-sm text-red-600 font-medium ml-4"
            >
              {{ errors.confirmPassword }}
            </p>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-full transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
          >
            <svg
              v-if="loading"
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ loading ? 'Updating Password...' : 'Update Password' }}
          </button>
        </form>
      </div>
    </div>

    <!-- Right Side - Illustration -->
    <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-600 via-purple-500 to-purple-400 items-center justify-center p-16">
      <div class="text-center text-white">
        <div class="mb-8">
          <svg class="w-32 h-32 mx-auto mb-6 opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
        </div>
        <h2 class="text-4xl font-bold mb-4">Secure Your Account</h2>
        <p class="text-xl opacity-90 leading-relaxed max-w-md">
          Update your password to ensure your audio files and data remain protected.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useToast } from "vue-toastification";

const router = useRouter();
const authStore = useAuthStore();
const toast = useToast();

// Reactive state
const loading = ref(false);
const showPassword = ref(false);
const errorMessage = ref("");

const form = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

const errors = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// Password validation
const passwordChecks = computed(() => ({
  length: form.newPassword.length >= 8,
  lowercase: /[a-z]/.test(form.newPassword),
  uppercase: /[A-Z]/.test(form.newPassword),
  number: /\d/.test(form.newPassword)
}));

const isPasswordValid = computed(() => {
  return Object.values(passwordChecks.value).every(check => check);
});

const isFormValid = computed(() => {
  return form.currentPassword && 
         form.newPassword && 
         form.confirmPassword &&
         isPasswordValid.value &&
         form.newPassword === form.confirmPassword;
});

const clearError = (field) => {
  errors[field] = "";
  errorMessage.value = "";
};

const validateForm = () => {
  let isValid = true;

  if (!form.currentPassword) {
    errors.currentPassword = "Current password is required";
    isValid = false;
  }

  if (!form.newPassword) {
    errors.newPassword = "New password is required";
    isValid = false;
  } else if (!isPasswordValid.value) {
    errors.newPassword = "Password does not meet requirements";
    isValid = false;
  }

  if (!form.confirmPassword) {
    errors.confirmPassword = "Please confirm your new password";
    isValid = false;
  } else if (form.newPassword !== form.confirmPassword) {
    errors.confirmPassword = "Passwords do not match";
    isValid = false;
  }

  return isValid;
};

const handlePasswordReset = async () => {
  errorMessage.value = "";

  if (!validateForm()) {
    errorMessage.value = "Please fix the errors above";
    return;
  }

  try {
    loading.value = true;
    
    await authStore.resetPassword({
      currentPassword: form.currentPassword,
      newPassword: form.newPassword,
    });

    toast.success("Password updated successfully!");
    router.push({ name: "Dashboard" });
  } catch (error) {
    console.error("Password reset error:", error);
    
    if (error.response?.data?.message) {
      errorMessage.value = error.response.data.message;
    } else if (error.message) {
      errorMessage.value = error.message;
    } else {
      errorMessage.value = "Failed to update password. Please try again.";
    }

    toast.error(errorMessage.value);
  } finally {
    loading.value = false;
  }
};
</script>
