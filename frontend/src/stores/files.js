import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useFilesStore = defineStore('files', () => {
  // State
  const files = ref([])
  const loading = ref(false)
  const error = ref(null)
  const s3Status = ref({
    connected: false,
    message: '',
    lastChecked: null
  })
  const pagination = ref({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false
  })
  const filters = ref({
    search: '',
    sortBy: 'name',
    sortOrder: 'asc',
    fileTypes: [], // Array of file extensions to filter by
    sizeRange: { min: null, max: null }, // Size range in bytes
    dateRange: { start: null, end: null }, // Date range for lastModified
    duration: { min: null, max: null } // Duration range in seconds (if available)
  })
  const selectedFiles = ref([])

  // Getters
  const hasFiles = computed(() => files.value.length > 0)
  const selectedCount = computed(() => selectedFiles.value.length)
  const isAllSelected = computed(() =>
    files.value.length > 0 && selectedFiles.value.length === files.value.length
  )
  const hasError = computed(() => error.value !== null)
  const isS3Available = computed(() => s3Status.value.connected)

  // Actions
  const checkS3Status = async () => {
    try {
      const response = await api.get('/files/s3-status')

      if (response.data.success) {
        s3Status.value = {
          connected: response.data.s3Status.success,
          message: response.data.s3Status.message,
          lastChecked: new Date().toISOString()
        }
        return s3Status.value
      }
    } catch (error) {
      console.error('Error checking S3 status:', error)
      s3Status.value = {
        connected: false,
        message: 'Failed to check S3 status',
        lastChecked: new Date().toISOString()
      }
      throw error
    }
  }

  const fetchFiles = async (page = 1) => {
    try {
      loading.value = true
      error.value = null

      // Check S3 status first
      await checkS3Status()

      const params = {
        page,
        limit: pagination.value.itemsPerPage,
        search: filters.value.search,
        sortBy: filters.value.sortBy,
        sortOrder: filters.value.sortOrder
      }

      // Add advanced filter parameters
      if (filters.value.fileTypes.length > 0) {
        params.fileTypes = filters.value.fileTypes.join(',')
      }

      if (filters.value.sizeRange.min !== null) {
        params.minSize = filters.value.sizeRange.min
      }

      if (filters.value.sizeRange.max !== null) {
        params.maxSize = filters.value.sizeRange.max
      }

      if (filters.value.dateRange.start) {
        params.startDate = filters.value.dateRange.start
      }

      if (filters.value.dateRange.end) {
        params.endDate = filters.value.dateRange.end
      }

      console.log('Fetching files with params:', params)

      const response = await api.get('/files', { params })

      if (response.data.success) {
        const data = response.data.data
        files.value = data.files
        pagination.value = data.pagination

        // Clear selection when fetching new data
        selectedFiles.value = []

        // Check if this is a loading state (cache miss)
        if (data.loading) {
          console.log('Cache miss detected, files are loading in background...')
          error.value = {
            message: data.message || 'Loading files from S3... This may take a moment.',
            type: 'loading',
            details: 'Files are being cached in the background. This page will refresh automatically when ready.'
          }

          // Start polling for cache readiness
          pollForCacheReady(params, page)
        } else {
          console.log(`Loaded ${files.value.length} files (page ${page})`)
        }

        return data
      }
    } catch (err) {
      console.error('Error fetching files:', err)
      error.value = {
        message: err.response?.data?.message || 'Failed to fetch files',
        type: err.response?.status === 503 ? 's3_unavailable' : 'general_error',
        details: err.response?.data?.error || err.message
      }

      // Clear files on error
      files.value = []
      pagination.value = {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPrevPage: false
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  const pollForCacheReady = async (params, page, maxAttempts = 30, interval = 2000) => {
    let attempts = 0

    const poll = async () => {
      try {
        attempts++
        console.log(`🔄 Polling for cache readiness (attempt ${attempts}/${maxAttempts})...`)

        const response = await api.get('/files/cache-status')

        if (response.data.success && response.data.data.cacheReady) {
          console.log('✅ Cache is ready! Refetching files...')
          // Cache is ready, refetch files
          await fetchFiles(page)
          return
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, interval)
        } else {
          console.warn('⚠️ Max polling attempts reached. Cache may still be loading.')
          error.value = {
            message: 'Files are taking longer than expected to load. Please try refreshing the page.',
            type: 'timeout',
            details: 'The S3 cache is still being populated. This can take several minutes for large buckets.'
          }
        }
      } catch (err) {
        console.error('❌ Error polling cache status:', err)
        if (attempts < maxAttempts) {
          setTimeout(poll, interval)
        }
      }
    }

    setTimeout(poll, interval)
  }

  const searchFiles = async (searchTerm) => {
    filters.value.search = searchTerm
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const sortFiles = async (sortBy, sortOrder = 'asc') => {
    filters.value.sortBy = sortBy
    filters.value.sortOrder = sortOrder
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const syncFiles = async () => {
    try {
      loading.value = true
      console.log('🔄 Manually syncing files from S3...')

      const response = await api.post('/files/sync')

      if (response.data.success) {
        console.log('✅ Files synced successfully:', response.data.data)
        // Refresh the current page
        await fetchFiles(pagination.value.currentPage)
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to sync files')
      }
    } catch (err) {
      console.error('❌ Error syncing files:', err)
      error.value = {
        message: err.response?.data?.message || 'Failed to sync files',
        type: 'sync_error',
        details: err.response?.data?.error || err.message
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  // Advanced filtering methods
  const setFileTypeFilter = async (fileTypes) => {
    filters.value.fileTypes = Array.isArray(fileTypes) ? fileTypes : [fileTypes]
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const setSizeRangeFilter = async (minSize, maxSize) => {
    filters.value.sizeRange = { min: minSize, max: maxSize }
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const setDateRangeFilter = async (startDate, endDate) => {
    filters.value.dateRange = { start: startDate, end: endDate }
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const clearFilters = async () => {
    filters.value = {
      search: '',
      sortBy: 'name',
      sortOrder: 'asc',
      fileTypes: [],
      sizeRange: { min: null, max: null },
      dateRange: { start: null, end: null },
      duration: { min: null, max: null }
    }
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const clearSpecificFilter = async (filterType) => {
    switch (filterType) {
      case 'search':
        filters.value.search = ''
        break
      case 'fileTypes':
        filters.value.fileTypes = []
        break
      case 'sizeRange':
        filters.value.sizeRange = { min: null, max: null }
        break
      case 'dateRange':
        filters.value.dateRange = { start: null, end: null }
        break
      case 'duration':
        filters.value.duration = { min: null, max: null }
        break
    }
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const generateDownloadUrl = async (fileKey) => {
    try {
      console.log('Generating download URL for:', fileKey)
      const response = await api.get(`/files/${encodeURIComponent(fileKey)}/download`)

      if (response.data.success) {
        console.log('Download URL generated successfully')
        return {
          url: response.data.downloadUrl,
          expires: response.data.expires,
          downloadType: response.data.downloadType
        }
      }
    } catch (error) {
      console.error('Error generating download URL:', error)
      console.error('Error details:', error.response?.data)

      // Handle rate limiting
      if (error.response?.status === 429) {
        const details = error.response.data.details
        throw new Error(`Rate limit exceeded. Try again at ${new Date(details.resetTime).toLocaleTimeString()}`)
      }

      throw error
    }
  }

  const generateStreamUrl = async (fileKey) => {
    try {
      console.log('Generating stream URL for:', fileKey)
      const response = await api.get(`/files/${encodeURIComponent(fileKey)}/stream-url`)

      if (response.data.success) {
        console.log('Stream URL generated successfully')
        return {
          url: response.data.streamUrl,
          expires: response.data.expires,
          downloadType: response.data.downloadType
        }
      }
    } catch (error) {
      console.error('Error generating stream URL:', error)
      console.error('Error details:', error.response?.data)
      throw error
    }
  }

  const generateBulkDownloadUrls = async (fileKeys) => {
    try {
      const response = await api.post('/files/bulk-download', { fileKeys })

      if (response.data.success) {
        return {
          downloadUrls: response.data.downloadUrls,
          totalFiles: response.data.totalFiles,
          successfulUrls: response.data.successfulUrls,
          failedUrls: response.data.failedUrls
        }
      }
    } catch (error) {
      console.error('Error generating bulk download URLs:', error)

      // Handle rate limiting
      if (error.response?.status === 429) {
        const details = error.response.data.details
        throw new Error(`Bulk download rate limit exceeded. Try again at ${new Date(details.resetTime).toLocaleTimeString()}`)
      }

      throw error
    }
  }

  const downloadFile = async (file) => {
    try {
      console.log('Starting download for file:', file.name)
      const downloadResult = await generateDownloadUrl(file.key)
      console.log('Download URL generated, starting download')

      // Create temporary link and trigger download
      const link = document.createElement('a')
      link.href = downloadResult.url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      console.log(`📥 Download initiated for ${file.name} (expires: ${downloadResult.expires})`)
    } catch (error) {
      console.error('Error downloading file:', error)
      console.error('Error details:', error.response?.data)
      throw error
    }
  }

  const downloadSelectedFiles = async () => {
    try {
      const fileKeys = selectedFiles.value.map(file => file.key)

      // Use zip download for multiple files
      if (fileKeys.length > 1) {
        await downloadSelectedFilesAsZip(fileKeys)
      } else {
        // For single file, use regular download
        const bulkResult = await generateBulkDownloadUrls(fileKeys)
        bulkResult.downloadUrls.forEach((result, index) => {
          if (result.success) {
            const link = document.createElement('a')
            link.href = result.url
            link.download = selectedFiles.value[index].name
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }
        })

        if (bulkResult.failedUrls > 0) {
          console.warn(`${bulkResult.failedUrls} files failed to generate download URLs`)
        }
      }
    } catch (error) {
      console.error('Error downloading selected files:', error)
      throw error
    }
  }

  const downloadSelectedFilesAsZip = async (fileKeys) => {
    try {
      const archiveName = `audio-files-${new Date().toISOString().split('T')[0]}.zip`

      // Add longer timeout for ZIP creation
      const response = await api.post('/files/bulk-download-zip', {
        fileKeys,
        archiveName
      }, {
        responseType: 'blob',
        timeout: 300000, // 5 minutes timeout
        onDownloadProgress: (progressEvent) => {
          // Optional: You can add progress tracking here
          console.log('ZIP download progress:', progressEvent)
        }
      })

      // Validate response
      if (!response.data || response.data.size === 0) {
        throw new Error('Empty ZIP file received')
      }

      // Create download link for the zip file
      const blob = new Blob([response.data], { type: 'application/zip' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = archiveName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      console.log(`✅ ZIP download completed: ${archiveName} (${(blob.size / 1024 / 1024).toFixed(2)} MB)`)
    } catch (error) {
      console.error('Error downloading files as zip:', error)

      // Provide more specific error messages
      if (error.code === 'ECONNABORTED') {
        throw new Error('ZIP download timed out. Please try with fewer files or check your connection.')
      } else if (error.response?.status === 500) {
        throw new Error('Server error while creating ZIP. Please try again or contact support.')
      } else if (error.message?.includes('socket hang up')) {
        throw new Error('Connection lost during ZIP creation. Please try again.')
      }

      throw error
    }
  }

  const selectFile = (file) => {
    const index = selectedFiles.value.findIndex(f => f.key === file.key)
    if (index === -1) {
      selectedFiles.value.push(file)
    } else {
      selectedFiles.value.splice(index, 1)
    }
  }

  const selectAllFiles = () => {
    if (isAllSelected.value) {
      selectedFiles.value = []
    } else {
      selectedFiles.value = [...files.value]
    }
  }

  const clearSelection = () => {
    selectedFiles.value = []
  }

  const isFileSelected = (file) => {
    return selectedFiles.value.some(f => f.key === file.key)
  }

  const getDownloadStats = async (days = 30) => {
    try {
      const response = await api.get(`/files/download-stats?days=${days}`)

      if (response.data.success) {
        return response.data.stats
      }
    } catch (error) {
      console.error('Error getting download stats:', error)
      throw error
    }
  }

  const getSearchSuggestions = async (query = '', limit = 10) => {
    try {
      const response = await api.get(`/files/search-suggestions?q=${encodeURIComponent(query)}&limit=${limit}`)

      if (response.data.success) {
        return response.data.suggestions
      }
      return []
    } catch (error) {
      console.error('Error getting search suggestions:', error)
      return []
    }
  }

  const getFileStatistics = async () => {
    try {
      const response = await api.get('/files/file-statistics')

      if (response.data.success) {
        return response.data.statistics
      }
    } catch (error) {
      console.error('Error getting file statistics:', error)
      throw error
    }
  }

  const getFileMetadata = async (fileKey) => {
    try {
      const response = await api.get(`/files/${encodeURIComponent(fileKey)}/metadata`)

      if (response.data.success) {
        return response.data.metadata
      }
    } catch (error) {
      console.error('Error getting file metadata:', error)
      throw error
    }
  }

  // Enhanced filter methods with better UX
  const setDurationFilter = async (minDuration, maxDuration) => {
    filters.value.duration = { min: minDuration, max: maxDuration }
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const toggleFileType = async (fileType) => {
    const currentTypes = [...filters.value.fileTypes]
    const index = currentTypes.indexOf(fileType)

    if (index > -1) {
      currentTypes.splice(index, 1)
    } else {
      currentTypes.push(fileType)
    }

    filters.value.fileTypes = currentTypes
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const setQuickFilter = async (filterType) => {
    const now = new Date()

    switch (filterType) {
      case 'today':
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        filters.value.dateRange = {
          start: today.toISOString(),
          end: now.toISOString()
        }
        break
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        filters.value.dateRange = {
          start: weekAgo.toISOString(),
          end: now.toISOString()
        }
        break
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        filters.value.dateRange = {
          start: monthAgo.toISOString(),
          end: now.toISOString()
        }
        break
      case 'large':
        filters.value.sizeRange = { min: 50 * 1024 * 1024, max: null } // > 50MB
        break
      case 'small':
        filters.value.sizeRange = { min: null, max: 5 * 1024 * 1024 } // < 5MB
        break
    }

    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  return {
    // State
    files,
    loading,
    error,
    s3Status,
    pagination,
    filters,
    selectedFiles,

    // Getters
    hasFiles,
    selectedCount,
    isAllSelected,
    hasError,
    isS3Available,

    // Actions
    checkS3Status,
    fetchFiles,
    searchFiles,
    sortFiles,
    setFileTypeFilter,
    setSizeRangeFilter,
    setDateRangeFilter,
    clearFilters,
    clearSpecificFilter,
    generateDownloadUrl,
    generateStreamUrl,
    generateBulkDownloadUrls,
    downloadFile,
    downloadSelectedFiles,
    downloadSelectedFilesAsZip,
    selectFile,
    selectAllFiles,
    clearSelection,
    isFileSelected,
    getDownloadStats,
    getSearchSuggestions,
    getFileStatistics,
    getFileMetadata,
    setDurationFilter,
    toggleFileType,
    setQuickFilter,
    syncFiles,
    pollForCacheReady
  }
})
