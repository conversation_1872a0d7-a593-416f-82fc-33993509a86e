import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const loading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.roles?.some(role => role.code === 'ADMIN'))
  const permissions = computed(() => {
    if (!user.value?.roles) return []
    const allPermissions = new Set()
    user.value.roles.forEach(role => {
      role.permissions?.forEach(permission => {
        allPermissions.add(permission)
      })
    })
    return Array.from(allPermissions)
  })

  // Actions
  const login = async (credentials) => {
    try {
      loading.value = true
      console.log('Attempting login with:', { email: credentials.email, subsidiaryId: credentials.subsidiaryId })
      console.log('API instance baseURL:', api.defaults.baseURL)

      const response = await api.post('/auth/login', credentials)

      if (response.data.success) {
        token.value = response.data.token
        user.value = response.data.user

        // Store token in localStorage
        localStorage.setItem('token', token.value)

        // Set default authorization header
        api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`

        // Ensure user data includes permissions
        console.log('Login successful, user data:', user.value)
        console.log('User permissions:', permissions.value)
        console.log('Is admin:', isAdmin.value)

        // Wait a moment to ensure all reactive properties are updated
        await new Promise(resolve => setTimeout(resolve, 100))

        // Trigger a custom event to notify components that user is fully loaded
        window.dispatchEvent(new CustomEvent('userLoaded', { detail: user.value }))

        // Return response data including requiresPasswordReset flag
        return response.data
      }
    } catch (error) {
      console.error('Login error:', error)
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          method: error.config?.method
        }
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (passwordData) => {
    try {
      loading.value = true
      const response = await api.post('/auth/reset-password', passwordData)

      if (response.data.success) {
        // Update user to reflect password has been changed
        if (user.value) {
          user.value.requiresPasswordReset = false
        }
        return response.data
      }
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      // Call logout endpoint if token exists
      if (token.value) {
        await api.post('/auth/logout')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local state regardless of API call success
      user.value = null
      token.value = null
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
      loading.value = false
    }
  }

  const fetchUser = async () => {
    try {
      loading.value = true
      const response = await api.get('/auth/me')
      
      if (response.data.success) {
        user.value = response.data.user
        return response.data.user
      }
    } catch (error) {
      // If fetching user fails, clear auth state
      await logout()
      throw error
    } finally {
      loading.value = false
    }
  }

  const initializeAuth = async () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`

      // If we have a token but no user data, fetch it
      if (!user.value) {
        try {
          await fetchUser()
        } catch (error) {
          console.error('Failed to fetch user data on initialization:', error)
        }
      }
    }
  }

  // Initialize auth on store creation
  initializeAuth()

  return {
    // State
    user,
    token,
    loading,
    
    // Getters
    isAuthenticated,
    isAdmin,
    permissions,
    
    // Actions
    login,
    logout,
    fetchUser,
    initializeAuth,
    resetPassword
  }
})
