<template>
  <div id="app" class="min-h-screen bg-gray-50 flex flex-col">
    <!-- Premium Navigation Header -->
    <nav v-if="authStore.isAuthenticated" class="bg-white/95 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Brand -->
          <div class="flex items-center">
            <router-link to="/dashboard" class="flex items-center group">
              <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200 shadow-lg">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-lg font-bold font-display text-gray-900 group-hover:text-indigo-600 transition-colors">
                  Audio Vault
                </h1>
                <p class="text-xs text-gray-500 font-medium">File Management</p>
              </div>
            </router-link>
          </div>

          <!-- Navigation Links -->
          <div :key="`nav-${navigationKey}`" class="hidden md:flex items-center space-x-8">
            <router-link
              to="/dashboard"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/dashboard' }"
            >
              Dashboard
            </router-link>
            <router-link
              to="/files"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/files' }"
            >
              Files
            </router-link>
            <router-link
              v-if="canManageUsers"
              to="/users"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50 flex items-center space-x-2"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/users' }"
            >
              <i class="fas fa-users"></i>
              <span>Users</span>
            </router-link>
            <router-link
              to="/analytics"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/analytics' }"
            >
              <i class="fas fa-chart-bar mr-1"></i>
              Analytics
            </router-link>
            <router-link
              v-if="canViewAudit"
              to="/audit"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/audit' }"
            >
              <i class="fas fa-clipboard-list mr-1"></i>
              Audit Trail
            </router-link>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25V12l2.25-2.25V9.75a6 6 0 0 1 6-6z"></path>
              </svg>
            </button>

            <!-- User Profile -->
            <div class="flex items-center space-x-3 bg-gray-50 rounded-xl px-3 py-2">
              <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-sm font-bold text-white">
                  {{ authStore.user?.username?.charAt(0).toUpperCase() || 'A' }}
                </span>
              </div>
              <div class="hidden sm:block">
                <p class="text-sm font-semibold text-gray-900">{{ authStore.user?.username || 'Admin' }}</p>
                <p class="text-xs text-gray-500">{{ authStore.user?.role || 'Administrator' }}</p>
              </div>
            </div>

            <!-- Logout Button -->
            <button
              @click="handleLogout"
              class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span class="hidden sm:inline">Logout</span>
            </button>
          </div>
        </div>
      </div>
    </nav>



    <!-- Main Content with Sidebar Layout -->
    <div class="flex flex-1 relative overflow-hidden">
      <!-- Permanent Admin Sidebar -->
      <div
        v-if="authStore.isAuthenticated && canManageUsers"
        :key="`sidebar-${sidebarKey}-${authStore.user?.id}-${authStore.permissions.length}`"
        class="w-80 bg-white border-r border-gray-200 shadow-sm flex-shrink-0 overflow-hidden"
        style="min-height: 100vh;"
      >
        <AdminSidebar
          :users="users"
          :roles="roles"
          :loading="sidebarLoading"
          @add-user="handleAddUser"
          @view-all="handleViewAllUsers"
          @view-user="handleViewUser"
          @refresh="refreshSidebarData"
        />
      </div>

      <!-- Main Content Area -->
      <main class="flex-1 min-w-0 overflow-auto">
        <router-view />
      </main>
    </div>



    <!-- Modern Loading Overlay -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div class="card-glass p-8 flex flex-col items-center space-y-4 animate-fadeInUp">
        <!-- Modern Spinner -->
        <div class="relative">
          <div class="w-12 h-12 rounded-full border-4 border-white/20"></div>
          <div class="absolute top-0 left-0 w-12 h-12 rounded-full border-4 border-transparent border-t-white animate-spin"></div>
        </div>
        <div class="text-center">
          <p class="text-white font-medium">Loading...</p>
          <p class="text-white/70 text-sm">Please wait a moment</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import UserManagementSidebar from '@/components/UserManagementSidebar.vue'
import AdminSidebar from '@/components/AdminSidebar.vue'
import userService from '@/services/userService'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
const loading = ref(false)

// Sidebar state
const showUserSidebar = ref(false)
const users = ref([])
const roles = ref([])
const sidebarLoading = ref(false)
const sidebarKey = ref(0) // Force re-render key

// Force reactivity key for navigation updates
const navigationKey = ref(0)

// Computed properties
const canManageUsers = computed(() => {
  // Force reactivity by including navigationKey
  navigationKey.value

  const hasPermission = authStore.permissions.includes('users:read')
  console.log('🔄 canManageUsers computed:', {
    isAuthenticated: authStore.isAuthenticated,
    permissions: authStore.permissions,
    hasPermission,
    user: authStore.user,
    navigationKey: navigationKey.value
  })
  return hasPermission
})

const isAdminUser = computed(() => {
  return authStore.isAdmin && authStore.permissions.includes('users:read')
})

const canViewAudit = computed(() => {
  // Force reactivity by including navigationKey
  navigationKey.value

  const userRoles = authStore.user?.roles?.map(role => role.code) || []
  return userRoles.includes('ADMIN') || userRoles.includes('COMPLIANCE')
})

// Methods
const toggleUserSidebar = async () => {
  showUserSidebar.value = !showUserSidebar.value

  // Load users and roles when opening sidebar
  if (showUserSidebar.value && users.value.length === 0) {
    try {
      const [usersResponse, rolesResponse] = await Promise.all([
        userService.getUsers({ page: 1, limit: 50 }),
        userService.getRoles()
      ])
      users.value = usersResponse.data.users || []
      roles.value = rolesResponse.data || []
    } catch (error) {
      console.error('Error loading sidebar data:', error)
    }
  }
}

// Auto-load sidebar data for admin users
const loadSidebarData = async (force = false) => {
  console.log('loadSidebarData called:', {
    canManageUsers: canManageUsers.value,
    isAuthenticated: authStore.isAuthenticated,
    hasUsers: users.value.length > 0,
    force
  })

  if (canManageUsers.value && authStore.isAuthenticated && (users.value.length === 0 || force)) {
    sidebarLoading.value = true
    try {
      const [usersResponse, rolesResponse] = await Promise.all([
        userService.getUsers({ page: 1, limit: 50 }),
        userService.getRoles()
      ])

      // Extract data from API responses
      users.value = usersResponse?.users || []
      roles.value = rolesResponse?.roles || []

      // Force sidebar re-render
      sidebarKey.value++

      console.log('Sidebar data loaded:', {
        usersCount: users.value.length,
        rolesCount: roles.value.length,
        sidebarKey: sidebarKey.value
      })
    } catch (error) {
      console.error('Error loading sidebar data:', error)
      toast.error('Failed to load user data')
    } finally {
      sidebarLoading.value = false
    }
  }
}

// Refresh sidebar data
const refreshSidebarData = async () => {
  await loadSidebarData(true)
}



const handleAddUser = () => {
  showUserSidebar.value = false
  router.push('/users')
  // You can emit an event or use a global state to trigger the add user modal
}

const handleViewAllUsers = () => {
  showUserSidebar.value = false
  router.push('/users')
}

const handleViewUser = (user) => {
  showUserSidebar.value = false
  router.push(`/users?view=${user.id}`)
}

const handleLogout = async () => {
  try {
    loading.value = true
    await authStore.logout()
    router.push({ name: 'Login' })
    toast.success('Logged out successfully')
  } catch (error) {
    toast.error('Error logging out')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  console.log('App mounted, checking auth state:', {
    token: !!authStore.token,
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user,
    permissions: authStore.permissions
  })

  // Check if user is already authenticated
  if (authStore.token) {
    try {
      await authStore.fetchUser()
      console.log('User fetched on mount:', authStore.user)
      // Wait for reactivity to update
      await new Promise(resolve => setTimeout(resolve, 300))
      // Load sidebar data for admin users
      await loadSidebarData(true) // Force load on mount
    } catch (error) {
      console.error('Error fetching user:', error)
      authStore.logout()
    }
  }

  // Listen for user loaded event from login
  const handleUserLoaded = async (event) => {
    console.log('User loaded event received:', event.detail)
    // Wait a moment for Vue reactivity to update
    await new Promise(resolve => setTimeout(resolve, 300))
    await loadSidebarData(true)
  }

  window.addEventListener('userLoaded', handleUserLoaded)

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('userLoaded', handleUserLoaded)
  })
})

// Watch for authentication changes
watch(() => authStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    // Wait a bit for user data to be fully loaded
    await new Promise(resolve => setTimeout(resolve, 200))
    await loadSidebarData(true) // Force load on authentication
  } else {
    showUserSidebar.value = false
    users.value = []
    roles.value = []
  }
})

// Watch for user changes (when user data is loaded after login)
watch(() => authStore.user, async (newUser) => {
  if (newUser && authStore.isAuthenticated) {
    console.log('🔄 User data changed, updating navigation...')
    // Force navigation update
    navigationKey.value++

    // Wait a bit for computed properties to update
    await new Promise(resolve => setTimeout(resolve, 200))
    await loadSidebarData(true)
  }
}, { deep: true })

// Watch for permissions changes specifically
watch(() => authStore.permissions, async (newPermissions) => {
  if (newPermissions.length > 0 && authStore.isAuthenticated) {
    console.log('🔄 Permissions updated:', newPermissions)
    // Force navigation and sidebar updates
    navigationKey.value++
    sidebarKey.value++

    // Wait a bit for computed properties to update
    await new Promise(resolve => setTimeout(resolve, 100))
    await loadSidebarData(true)
  }
}, { deep: true })

// Watch for authentication state changes
watch(() => authStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    console.log('🔄 User authenticated, updating navigation...')
    // Force navigation update immediately
    navigationKey.value++

    // Wait for user data to be loaded, then update again
    setTimeout(() => {
      console.log('🔄 Delayed navigation update after login...')
      navigationKey.value++
    }, 500)
  }
})



// Auto-refresh sidebar data every 30 seconds for admin users
let refreshInterval = null
watch(() => canManageUsers.value, async (canManage) => {
  if (canManage && authStore.isAuthenticated) {
    // Load data immediately when user gets permissions
    await loadSidebarData(true)

    // Clear any existing interval
    if (refreshInterval) {
      clearInterval(refreshInterval)
    }
    // Set up auto-refresh every 30 seconds
    refreshInterval = setInterval(() => {
      if (authStore.isAuthenticated && canManageUsers.value) {
        refreshSidebarData()
      }
    }, 30000)
  } else if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
})

// Clean up interval on unmount
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
