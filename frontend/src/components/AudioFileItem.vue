<template>
  <div
    class="group relative px-6 py-5 transition-all duration-300 border-l-4"
    :class="[
      selected
        ? 'bg-gradient-to-r from-indigo-50/60 to-purple-50/60 border-indigo-500 shadow-md'
        : 'border-transparent hover:border-indigo-400 hover:bg-gradient-to-r hover:from-indigo-50/30 hover:to-purple-50/30'
    ]"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center flex-1 min-w-0">
        <!-- Enhanced Checkbox -->
        <label class="inline-flex items-center cursor-pointer group/checkbox">
          <div class="relative">
            <input
              type="checkbox"
              :checked="selected"
              @change="$emit('select')"
              class="h-5 w-5 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 border-2 border-gray-300 rounded transition-all duration-200 transform group-hover/checkbox:scale-110"
              :class="[
                selected
                  ? 'bg-indigo-600 border-indigo-600 shadow-lg'
                  : 'bg-white hover:border-indigo-400'
              ]"
            />
            <!-- Selection indicator -->
            <div
              v-if="selected"
              class="absolute inset-0 flex items-center justify-center pointer-events-none"
            >
              <svg class="w-3 h-3 text-white animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
          <span class="sr-only">Select {{ file.name }}</span>
        </label>

        <!-- File Icon with Audio Waveform -->
        <div class="ml-5 flex-shrink-0">
          <div
            class="relative w-14 h-14 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300"
            :class="[
              selected
                ? 'bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 shadow-xl scale-105 ring-2 ring-indigo-400 ring-offset-2'
                : 'bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 group-hover:shadow-xl group-hover:scale-105'
            ]"
          >
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
            <!-- Audio format badge -->
            <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
              <span class="text-xs font-bold text-gray-700">{{ getFileExtension(file.name) }}</span>
            </div>
          </div>
        </div>

        <!-- File Info -->
        <div class="ml-6 flex-1 min-w-0">
          <div class="flex items-start justify-between">
            <div class="min-w-0 flex-1">
              <!-- File Name -->
              <h3 class="text-lg font-semibold text-gray-900 truncate group-hover:text-indigo-700 transition-colors">
                {{ file.name }}
              </h3>

              <!-- Enhanced File Details with Metadata -->
              <div class="flex flex-wrap items-center mt-2 text-sm text-gray-600 gap-4">
                <!-- Duration -->
                <div v-if="file.metadata?.formattedDuration" class="flex items-center">
                  <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="font-medium">{{ file.metadata.formattedDuration }}</span>
                </div>

                <!-- File Size -->
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                  <span class="font-medium">{{ file.metadata?.formattedSize || formatFileSize(file.size) }}</span>
                </div>

                <!-- Bitrate -->
                <div v-if="file.metadata?.formattedBitrate" class="flex items-center">
                  <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                  <span class="font-medium">{{ file.metadata.formattedBitrate }}</span>
                </div>

                <!-- Last Modified -->
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v6a2 2 0 01-2 2H10a2 2 0 01-2-2v-6"></path>
                  </svg>
                  <span>{{ formatDate(file.lastModified) }}</span>
                </div>
              </div>

              <!-- Format and Quality Badges -->
              <div class="flex flex-wrap items-center gap-2 mt-3">
                <!-- Format Badge -->
                <div :class="[
                  'inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full',
                  getFormatBadgeClass(file.extension || getFileExtension(file.name))
                ]">
                  {{ file.metadata?.format?.name || (file.extension?.toUpperCase() || getFileExtension(file.name).toUpperCase()) }}
                </div>

                <!-- Quality Badges -->
                <div v-if="file.metadata?.isLossless" class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Lossless
                </div>
                <div v-else-if="file.metadata?.isHighQuality" class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                  High Quality
                </div>

                <!-- Quality Score -->
                <div v-if="file.metadata?.qualityScore" class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  {{ file.metadata.qualityScore }}/100
                </div>

                <!-- Format Type -->
                <div v-if="file.metadata?.format?.type" :class="[
                  'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
                  file.metadata.format.type === 'lossless' ? 'bg-emerald-100 text-emerald-800' : 'bg-orange-100 text-orange-800'
                ]">
                  {{ file.metadata.format.type === 'lossless' ? 'Lossless' : 'Lossy' }}
                </div>
              </div>

              <!-- Additional metadata if available -->
              <div v-if="file.metadata" class="mt-3 space-y-2">
                <div v-if="file.metadata.title && file.metadata.title !== file.name" class="flex items-center text-sm text-gray-600">
                  <span class="font-medium text-gray-700 mr-2">Title:</span>
                  <span class="truncate">{{ file.metadata.title }}</span>
                </div>
                <div v-if="file.metadata.description" class="flex items-center text-sm text-gray-600">
                  <span class="font-medium text-gray-700 mr-2">Description:</span>
                  <span class="truncate">{{ file.metadata.description }}</span>
                </div>
                <div v-if="file.metadata.tags" class="flex items-center text-sm text-gray-600">
                  <span class="font-medium text-gray-700 mr-2">Tags:</span>
                  <span class="truncate">{{ file.metadata.tags }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="ml-6 flex items-center space-x-3">
        <!-- Metadata Button -->
        <button
          @click="showMetadata = true"
          class="group/btn relative inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 text-white rounded-full hover:from-indigo-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          title="View metadata"
        >
          <svg class="w-5 h-5 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </button>

        <!-- Play Button -->
        <button
          @click="handlePlay"
          class="group/btn relative inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-orange-500 via-pink-500 to-purple-600 text-white rounded-full hover:from-orange-600 hover:via-pink-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          title="Play audio"
        >
          <svg class="w-5 h-5 group-hover/btn:scale-110 transition-transform ml-0.5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z"/>
          </svg>
        </button>

        <!-- Download Button -->
        <button
          @click="handleDownload"
          :disabled="downloading"
          class="group/btn relative inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-full hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          title="Download file"
        >
          <svg v-if="downloading" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <svg v-else class="w-5 h-5 group-hover/btn:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Metadata Modal -->
    <MetadataModalSimple
      :show="showMetadata"
      :file="file"
      @close="showMetadata = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MetadataModalSimple from './MetadataModalSimple.vue'

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getFileExtension = (filename) => {
  return filename.split('.').pop()?.substring(0, 3) || 'AUD'
}

const getFormatBadgeClass = (extension) => {
  const ext = extension?.toLowerCase() || ''
  const formatClasses = {
    'mp3': 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800',
    'wav': 'bg-gradient-to-r from-green-100 to-green-200 text-green-800',
    'flac': 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800',
    'ogg': 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800',
    'm4a': 'bg-gradient-to-r from-indigo-100 to-indigo-200 text-indigo-800',
    'aac': 'bg-gradient-to-r from-cyan-100 to-cyan-200 text-cyan-800',
    'wma': 'bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800'
  }

  return formatClasses[ext] || 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800'
}

const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['select', 'download', 'play'])

const downloading = ref(false)
const showMetadata = ref(false)

const handlePlay = () => {
  console.log('Play button clicked for file:', props.file.name)
  console.log('Emitting play event with file:', props.file)
  emit('play', props.file)
}

const handleDownload = () => {
  console.log('Download button clicked for file:', props.file.name)
  console.log('Emitting download event with file:', props.file)
  emit('download', props.file)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
