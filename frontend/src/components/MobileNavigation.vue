<template>
  <!-- Mobile Navigation Component -->
  <div class="lg:hidden">
    <!-- Mobile Header -->
    <div class="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 px-4 py-3">
      <div class="flex items-center justify-between">
        <!-- Logo & Title -->
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">{{ title }}</h1>
            <p v-if="subtitle" class="text-xs text-gray-500">{{ subtitle }}</p>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button
          @click="toggleMenu"
          class="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors"
          :class="{ 'bg-indigo-100': isMenuOpen }"
        >
          <svg v-if="!isMenuOpen" class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg v-else class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="isMenuOpen" class="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm" @click="closeMenu"></div>
    </Transition>

    <!-- Mobile Menu Panel -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="transform translate-x-full"
      enter-to-class="transform translate-x-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="transform translate-x-0"
      leave-to-class="transform translate-x-full"
    >
      <div v-if="isMenuOpen" class="fixed top-0 right-0 z-50 w-80 h-full bg-white shadow-2xl">
        <!-- Menu Header -->
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-bold text-gray-900">Audio Vault</h2>
                <p class="text-sm text-gray-500">{{ userEmail }}</p>
              </div>
            </div>
            <button @click="closeMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Menu Items -->
        <div class="p-4 space-y-2">
          <router-link
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            @click="closeMenu"
            class="flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors group"
            :class="{ 'bg-indigo-50 border border-indigo-200': $route.path === item.path }"
          >
            <div 
              class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors"
              :class="$route.path === item.path ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600 group-hover:bg-indigo-100 group-hover:text-indigo-600'"
            >
              <component :is="item.icon" class="w-5 h-5" />
            </div>
            <div class="flex-1">
              <p class="font-medium text-gray-900">{{ item.name }}</p>
              <p class="text-sm text-gray-500">{{ item.description }}</p>
            </div>
            <svg v-if="$route.path === item.path" class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </router-link>
        </div>

        <!-- Menu Footer -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
          <button
            @click="handleLogout"
            class="w-full flex items-center justify-center space-x-2 p-3 bg-red-50 hover:bg-red-100 text-red-600 rounded-xl transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span class="font-medium">Sign Out</span>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps({
  title: {
    type: String,
    default: 'Audio Vault'
  },
  subtitle: {
    type: String,
    default: ''
  }
})

// Composables
const router = useRouter()
const authStore = useAuthStore()

// State
const isMenuOpen = ref(false)

// Computed
const userEmail = computed(() => authStore.user?.email || 'User')

// Computed
const canManageUsers = computed(() => {
  return authStore.permissions.includes('users:read')
})

const canViewAudit = computed(() => {
  return authStore.user?.roles?.some(role =>
    ['ADMIN', 'COMPLIANCE'].includes(role.code)
  )
})

// Menu items
const menuItems = computed(() => {
  const items = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      description: 'Overview & stats',
      icon: 'HomeIcon'
    },
    {
      name: 'Audio Files',
      path: '/files',
      description: 'Browse & manage',
      icon: 'MusicIcon'
    },
    {
      name: 'Upload',
      path: '/upload',
      description: 'Add new files',
      icon: 'UploadIcon'
    }
  ]

  // Add user management if user has permission
  if (canManageUsers.value) {
    items.push({
      name: 'User Management',
      path: '/users',
      description: 'Manage users & roles',
      icon: 'UsersIcon'
    })
  }

  // Add audit trail if user has permission
  if (canViewAudit.value) {
    items.push({
      name: 'Audit Trail',
      path: '/audit',
      description: 'Security & compliance',
      icon: 'AuditIcon'
    })
  }

  // Add analytics
  items.push({
    name: 'Analytics',
    path: '/analytics',
    description: 'Reports & insights',
    icon: 'ChartIcon'
  })

  return items
})

// Methods
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

const handleLogout = async () => {
  closeMenu()
  await authStore.logout()
  router.push('/login')
}

// Icons (simple SVG components)
const HomeIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>`
}

const MusicIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>`
}

const UploadIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg>`
}

const UsersIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>`
}

const ChartIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>`
}

const AuditIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>`
}
</script>

<style scoped>
/* Mobile-specific styles */
@media (max-width: 1024px) {
  .mobile-nav {
    display: block;
  }
}

/* Touch-friendly tap targets */
button, a {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling for mobile */
.mobile-menu {
  -webkit-overflow-scrolling: touch;
}
</style>
