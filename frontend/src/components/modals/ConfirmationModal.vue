<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container confirmation-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i :class="iconClass"></i>
          {{ title }}
        </h3>
      </div>

      <div class="modal-body">
        <p class="confirmation-message">{{ message }}</p>
      </div>

      <div class="modal-footer">
        <button @click="handleCancel" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          {{ cancelText }}
        </button>
        <button @click="handleConfirm" class="btn" :class="confirmButtonClass">
          <i :class="confirmIconClass"></i>
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfirmationModal',
  props: {
    title: {
      type: String,
      default: 'Confirm Action'
    },
    message: {
      type: String,
      required: true
    },
    confirmText: {
      type: String,
      default: 'Confirm'
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    type: {
      type: String,
      default: 'warning', // warning, danger, info
      validator: (value) => ['warning', 'danger', 'info'].includes(value)
    }
  },
  emits: ['confirm', 'cancel', 'close'],
  computed: {
    iconClass() {
      const icons = {
        warning: 'fas fa-exclamation-triangle',
        danger: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle'
      }
      return icons[this.type]
    },
    confirmButtonClass() {
      const classes = {
        warning: 'btn-warning',
        danger: 'btn-danger',
        info: 'btn-primary'
      }
      return classes[this.type]
    },
    confirmIconClass() {
      const icons = {
        warning: 'fas fa-check',
        danger: 'fas fa-trash',
        info: 'fas fa-check'
      }
      return icons[this.type]
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm')
    },
    handleCancel() {
      this.$emit('cancel')
      this.$emit('close')
    },
    handleOverlayClick() {
      this.$emit('cancel')
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.confirmation-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-title i {
  font-size: 1.125rem;
}

.modal-body {
  padding: 1.5rem;
}

.confirmation-message {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;
}

.modal-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background: #d97706;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn i {
  font-size: 0.75rem;
}
</style>
