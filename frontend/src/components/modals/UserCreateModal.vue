<template>
  <div class="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50" @click="handleOverlayClick">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Create New User</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Success Message with Temporary Password -->
      <div v-if="createdUser && createdUser.tempPassword" class="p-6 bg-green-50 border-b border-green-200">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-green-800">
              User Created Successfully!
            </h3>
            <div class="mt-2 text-sm text-green-700">
              <p class="mb-2">The user has been created with a temporary password:</p>
              <div class="bg-white border border-green-300 rounded-md p-3 flex items-center justify-between">
                <code class="text-lg font-mono text-gray-900 select-all">{{ createdUser.tempPassword }}</code>
                <button
                  type="button"
                  @click="copyTempPassword"
                  class="ml-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <svg v-if="!passwordCopied" class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <svg v-else class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ passwordCopied ? 'Copied!' : 'Copy' }}
                </button>
              </div>
              <p class="mt-2 text-xs">
                <strong>Important:</strong> Share this password securely with the user. They will be required to change it on first login.
              </p>
            </div>
          </div>
        </div>
        <div class="px-6 pb-4">
          <button
            type="button"
            @click="handleClose"
            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Close
          </button>
        </div>
      </div>

      <!-- Form -->
      <form v-if="!createdUser" @submit.prevent="handleSubmit" class="p-6 space-y-4">
        <!-- First Name & Last Name -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
              First Name
            </label>
            <input
              id="firstName"
              v-model="form.firstName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'border-red-300': errors.firstName }"
              placeholder="First Name"
            />
            <span v-if="errors.firstName" class="text-red-500 text-xs mt-1">{{ errors.firstName }}</span>
          </div>

          <div>
            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
              Last Name
            </label>
            <input
              id="lastName"
              v-model="form.lastName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'border-red-300': errors.lastName }"
              placeholder="Last Name"
            />
            <span v-if="errors.lastName" class="text-red-500 text-xs mt-1">{{ errors.lastName }}</span>
          </div>
        </div>

        <!-- Email -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :class="{ 'border-red-300': errors.email }"
            placeholder="Enter email address"
          />
          <span v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email }}</span>
        </div>

        <!-- Generate Temporary Password Option -->
        <div class="flex items-center">
          <input
            id="generateTempPassword"
            v-model="form.generateTempPassword"
            type="checkbox"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            @change="handleTempPasswordToggle"
          />
          <label for="generateTempPassword" class="ml-2 block text-sm text-gray-700">
            Generate temporary password (user will be required to change on first login)
          </label>
        </div>

        <!-- Password -->
        <div v-if="!form.generateTempPassword">
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <div class="relative">
            <input
              id="password"
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              :required="!form.generateTempPassword"
              class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :class="{ 'border-red-300': errors.password }"
              placeholder="Enter password"
            />
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </button>
          </div>
          <span v-if="errors.password" class="text-red-500 text-xs mt-1">{{ errors.password }}</span>
          <p class="text-xs text-gray-500 mt-1">Password must be at least 8 characters with uppercase, lowercase, and number</p>
        </div>

        <!-- Temporary Password Info -->
        <div v-if="form.generateTempPassword" class="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div class="flex">
            <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="text-sm text-blue-700">
              <p class="font-medium">Temporary Password</p>
              <p>A secure temporary password will be generated automatically. The user will be required to change it on their first login.</p>
            </div>
          </div>
        </div>

        <!-- User Role -->
        <div>
          <label for="userRole" class="block text-sm font-medium text-gray-700 mb-1">
            User Role
          </label>
          <select
            id="userRole"
            v-model="form.roleId"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :class="{ 'border-red-300': errors.roleId }"
          >
            <option value="">Select role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </option>
          </select>
          <span v-if="errors.roleId" class="text-red-500 text-xs mt-1">{{ errors.roleId }}</span>
          <p class="text-xs text-gray-500 mt-1">Selected role: None</p>
        </div>

        <!-- Account State -->
        <div>
          <label for="accountState" class="block text-sm font-medium text-gray-700 mb-1">
            Account State
          </label>
          <select
            id="accountState"
            v-model="form.isActive"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option :value="true">Active</option>
            <option :value="false">Inactive</option>
          </select>
        </div>

        <!-- Force Password Reset Option -->
        <div class="flex items-center">
          <input
            id="forcePasswordReset"
            v-model="form.forcePasswordReset"
            type="checkbox"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="forcePasswordReset" class="ml-2 block text-sm text-gray-700">
            Require user to change password on first login
          </label>
        </div>

        <!-- Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-md p-3">
          <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ submitError }}</p>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          :disabled="loading"
          class="w-full bg-black text-white py-3 px-4 rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          {{ loading ? 'Creating User...' : 'Create User' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/userService'

export default {
  name: 'UserCreateModal',
  emits: ['close', 'created'],
  setup(_, { emit }) {
    const authStore = useAuthStore()

    // Reactive data
    const loading = ref(false)
    const submitError = ref(null)
    const showPassword = ref(false)
    const roles = ref([])
    const createdUser = ref(null)
    const passwordCopied = ref(false)

    const form = reactive({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      roleId: '',
      isActive: true,
      forcePasswordReset: false,
      generateTempPassword: false
    })

    const errors = reactive({})

    // Computed properties
    const isFormValid = computed(() => {
      const basicValid = form.firstName &&
                         form.lastName &&
                         form.email &&
                         form.roleId

      if (form.generateTempPassword) {
        return basicValid
      }

      return basicValid &&
             form.password &&
             form.password.length >= 8 &&
             /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)
    })

    // Methods
    const loadRoles = async () => {
      try {
        const response = await userService.getRoles()
        roles.value = response.roles || []
      } catch (error) {
        console.error('Error loading roles:', error)
        submitError.value = 'Failed to load roles'
      }
    }

    const validateForm = () => {
      const newErrors = {}

      if (!form.firstName) {
        newErrors.firstName = 'First name is required'
      }

      if (!form.lastName) {
        newErrors.lastName = 'Last name is required'
      }

      if (!form.email || !/\S+@\S+\.\S+/.test(form.email)) {
        newErrors.email = 'Please enter a valid email address'
      }

      if (!form.generateTempPassword) {
        if (!form.password) {
          newErrors.password = 'Password is required'
        } else if (form.password.length < 8) {
          newErrors.password = 'Password must be at least 8 characters'
        } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)) {
          newErrors.password = 'Password must contain uppercase, lowercase, and number'
        }
      }

      if (!form.roleId) {
        newErrors.roleId = 'Please select a role'
      }

      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleSubmit = async () => {
      if (!validateForm()) return

      try {
        loading.value = true
        submitError.value = null

        // Prepare user data
        const userData = {
          username: form.email.split('@')[0], // Generate username from email
          firstName: form.firstName,
          lastName: form.lastName,
          email: form.email,
          subsidiaryId: authStore.user?.subsidiary?.id, // Use current user's subsidiary
          roleIds: [parseInt(form.roleId)], // Convert single role to array and ensure it's a number
          isActive: form.isActive,
          forcePasswordReset: form.forcePasswordReset,
          generateTempPassword: form.generateTempPassword
        }

        // Only include password if not generating temporary password
        if (!form.generateTempPassword) {
          userData.password = form.password
        }

        console.log('Creating user with data:', userData)
        const response = await userService.createUser(userData)

        // If temporary password was generated, show it to admin
        if (response.user && response.user.tempPassword) {
          createdUser.value = response.user
        } else {
          // If no temp password, close modal immediately
          emit('created')
        }
      } catch (error) {
        submitError.value = error.response?.data?.message || 'Failed to create user'
        console.error('Error creating user:', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleTempPasswordToggle = () => {
      if (form.generateTempPassword) {
        // Clear password field when switching to temp password
        form.password = ''
        // Clear password errors
        delete errors.password
      }
    }

    const copyTempPassword = async () => {
      if (createdUser.value?.tempPassword) {
        try {
          await navigator.clipboard.writeText(createdUser.value.tempPassword)
          passwordCopied.value = true
          setTimeout(() => {
            passwordCopied.value = false
          }, 2000)
        } catch (err) {
          console.error('Failed to copy password:', err)
        }
      }
    }

    const handleOverlayClick = () => {
      emit('close')
    }

    const handleClose = () => {
      emit('created')
      emit('close')
    }

    // Lifecycle
    onMounted(async () => {
      await loadRoles()
    })

    return {
      loading,
      submitError,
      showPassword,
      roles,
      form,
      errors,
      isFormValid,
      createdUser,
      passwordCopied,
      handleSubmit,
      handleTempPasswordToggle,
      copyTempPassword,
      handleOverlayClick,
      handleClose
    }
  }
}
</script>
