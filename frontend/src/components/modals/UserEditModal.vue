<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-user-edit"></i>
          Edit User: {{ user.username }}
        </h2>
        <button @click="$emit('close')" class="modal-close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-body">
        <!-- Basic Information -->
        <div class="form-section">
          <h3 class="section-title">Basic Information</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label for="username" class="form-label">Username *</label>
              <input
                id="username"
                v-model="form.username"
                type="text"
                class="form-input"
                :class="{ 'error': errors.username }"
                placeholder="Enter username"
                required
              />
              <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
            </div>
            
            <div class="form-group">
              <label for="email" class="form-label">Email *</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                class="form-input"
                :class="{ 'error': errors.email }"
                placeholder="Enter email address"
                required
              />
              <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="firstName" class="form-label">First Name</label>
              <input
                id="firstName"
                v-model="form.firstName"
                type="text"
                class="form-input"
                placeholder="Enter first name"
              />
            </div>

            <div class="form-group">
              <label for="lastName" class="form-label">Last Name</label>
              <input
                id="lastName"
                v-model="form.lastName"
                type="text"
                class="form-input"
                placeholder="Enter last name"
              />
            </div>
          </div>
        </div>

        <!-- Password Change -->
        <div class="form-section">
          <h3 class="section-title">
            Change Password
            <span class="optional-label">(Optional)</span>
          </h3>
          
          <div class="form-group">
            <label for="password" class="form-label">New Password</label>
            <div class="password-input-container">
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                :class="{ 'error': errors.password }"
                placeholder="Leave blank to keep current password"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="password-toggle"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div v-if="form.password" class="password-requirements">
              <p class="requirements-title">Password must contain:</p>
              <ul class="requirements-list">
                <li :class="{ 'valid': passwordChecks.length }">At least 8 characters</li>
                <li :class="{ 'valid': passwordChecks.lowercase }">One lowercase letter</li>
                <li :class="{ 'valid': passwordChecks.uppercase }">One uppercase letter</li>
                <li :class="{ 'valid': passwordChecks.number }">One number</li>
              </ul>
            </div>
            <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
          </div>
        </div>

        <!-- Status -->
        <div class="form-section">
          <h3 class="section-title">Account Status</h3>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="form.isActive"
                type="checkbox"
                class="checkbox-input"
              />
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">Account is active</span>
            </label>
            <p class="form-help">Inactive users cannot log in to the system</p>
          </div>
        </div>

        <!-- Roles -->
        <div class="form-section">
          <h3 class="section-title">Roles & Permissions</h3>
          
          <div class="roles-grid">
            <div
              v-for="role in roles"
              :key="role.id"
              class="role-card"
              :class="{ 'selected': form.roleIds.includes(role.id) }"
              @click="toggleRole(role.id)"
            >
              <div class="role-header">
                <div class="role-info">
                  <i :class="getRoleIcon(role.hierarchy_level)"></i>
                  <span class="role-name">{{ role.name }}</span>
                </div>
                <div class="role-checkbox">
                  <input
                    type="checkbox"
                    :checked="form.roleIds.includes(role.id)"
                    @change="toggleRole(role.id)"
                  />
                </div>
              </div>
              
              <p class="role-description">{{ role.description }}</p>
              
              <div class="role-permissions">
                <div class="permission-badges">
                  <span
                    v-for="permission in role.permissions.slice(0, 3)"
                    :key="permission"
                    class="permission-badge"
                  >
                    {{ formatPermission(permission) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="submitError" class="error-alert">
          <i class="fas fa-exclamation-triangle"></i>
          {{ submitError }}
        </div>

        <!-- Form Actions -->
        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn btn-secondary">
            Cancel
          </button>
          <button type="submit" :disabled="loading || !isFormValid" class="btn btn-primary">
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-save"></i>
            {{ loading ? 'Updating...' : 'Update User' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import userService from '@/services/userService'

export default {
  name: 'UserEditModal',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    // Reactive data
    const loading = ref(false)
    const submitError = ref(null)
    const showPassword = ref(false)
    const roles = ref([])
    
    const form = reactive({
      username: props.user.username || '',
      email: props.user.email || '',
      firstName: props.user.first_name || '',
      lastName: props.user.last_name || '',
      password: '',
      isActive: props.user.is_active !== false,
      roleIds: props.user.roles?.map(role => role.id) || []
    })
    
    const errors = reactive({})
    
    // Computed properties
    const passwordChecks = computed(() => ({
      length: form.password.length >= 8,
      lowercase: /[a-z]/.test(form.password),
      uppercase: /[A-Z]/.test(form.password),
      number: /\d/.test(form.password)
    }))
    
    const isPasswordValid = computed(() => {
      if (!form.password) return true // Password is optional for updates
      return Object.values(passwordChecks.value).every(check => check)
    })
    
    const isFormValid = computed(() => {
      return form.username && 
             form.email && 
             isPasswordValid.value &&
             form.roleIds.length > 0
    })
    
    // Methods
    const loadRoles = async () => {
      try {
        const response = await userService.getRoles()
        roles.value = response.roles
      } catch (error) {
        console.error('Error loading roles:', error)
        submitError.value = 'Failed to load roles'
      }
    }
    
    const toggleRole = (roleId) => {
      const index = form.roleIds.indexOf(roleId)
      if (index > -1) {
        form.roleIds.splice(index, 1)
      } else {
        form.roleIds.push(roleId)
      }
    }
    
    const validateForm = () => {
      const newErrors = {}
      
      if (!form.username || form.username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters'
      }
      
      if (!form.email || !/\S+@\S+\.\S+/.test(form.email)) {
        newErrors.email = 'Please enter a valid email address'
      }
      
      if (form.password && !isPasswordValid.value) {
        newErrors.password = 'Password does not meet requirements'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleSubmit = async () => {
      if (!validateForm()) return
      
      try {
        loading.value = true
        submitError.value = null
        
        // Prepare update data
        const updateData = {
          username: form.username,
          email: form.email,
          firstName: form.firstName,
          lastName: form.lastName,
          isActive: form.isActive
        }
        
        // Only include password if it's being changed
        if (form.password) {
          updateData.password = form.password
        }
        
        // Update user basic info
        await userService.updateUser(props.user.id, updateData)
        
        // Update roles if they've changed
        const currentRoleIds = props.user.roles?.map(role => role.id) || []
        const roleIdsChanged = JSON.stringify(currentRoleIds.sort()) !== JSON.stringify(form.roleIds.sort())
        
        if (roleIdsChanged) {
          await userService.assignRoles(props.user.id, form.roleIds)
        }
        
        emit('updated')
      } catch (error) {
        submitError.value = error.response?.data?.message || 'Failed to update user'
        console.error('Error updating user:', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleOverlayClick = () => {
      emit('close')
    }
    
    const getRoleIcon = (hierarchyLevel) => {
      const hierarchy = userService.getRoleHierarchy()
      return `fas fa-${hierarchy[hierarchyLevel]?.icon || 'user'}`
    }
    
    const formatPermission = (permission) => {
      const permissionMap = {
        'users:read': 'View Users',
        'users:write': 'Create/Edit Users',
        'users:delete': 'Delete Users',
        'files:read': 'View Files',
        'files:write': 'Upload/Edit Files',
        'files:delete': 'Delete Files',
        'files:download': 'Download Files',
        'analytics:read': 'View Analytics',
        'system:admin': 'System Administration',
        'audit:read': 'View Audit Logs',
        'roles:read': 'View Roles',
        'roles:write': 'Manage Roles',
        'subsidiaries:read': 'View Subsidiaries',
        'subsidiaries:write': 'Manage Subsidiaries'
      }
      return permissionMap[permission] || permission.split(':')[1]?.replace('_', ' ') || permission
    }
    
    // Lifecycle
    onMounted(() => {
      loadRoles()
    })
    
    return {
      loading,
      submitError,
      showPassword,
      roles,
      form,
      errors,
      passwordChecks,
      isFormValid,
      toggleRole,
      handleSubmit,
      handleOverlayClick,
      getRoleIcon,
      formatPermission
    }
  }
}
</script>

<style scoped>
/* Import shared modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-title i {
  color: #f59e0b;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 2rem;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.optional-label {
  font-size: 0.875rem;
  font-weight: 400;
  color: #6b7280;
  background: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input, .form-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input.error, .form-select.error {
  border-color: #ef4444;
}

.form-help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
  margin-bottom: 0;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
}

.password-requirements {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.requirements-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.requirements-list li::before {
  content: '✗';
  color: #ef4444;
  font-weight: bold;
}

.requirements-list li.valid::before {
  content: '✓';
  color: #10b981;
}

.requirements-list li.valid {
  color: #10b981;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-input:checked + .checkbox-custom {
  background: #4f46e5;
  border-color: #4f46e5;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.error-message {
  display: block;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.role-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.role-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.role-card.selected {
  border-color: #4f46e5;
  background: #f0f9ff;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.role-info i {
  color: #4f46e5;
}

.role-name {
  font-weight: 600;
  color: #1f2937;
}

.role-checkbox input {
  width: 18px;
  height: 18px;
  accent-color: #4f46e5;
}

.role-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.role-permissions {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
}

.permissions-count {
  font-size: 0.75rem;
  font-weight: 500;
  color: #4f46e5;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
  display: block;
}

.permission-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.permission-badge {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-transform: capitalize;
}

.more-permissions {
  background: #e5e7eb;
  color: #6b7280;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.error-alert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .modal-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .modal-header, .modal-footer {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }
}
</style>
