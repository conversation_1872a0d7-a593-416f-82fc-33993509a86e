<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-user"></i>
          User Details
        </h2>
        <button @click="$emit('close')" class="modal-close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- User Profile -->
        <div class="user-profile">
          <div class="user-avatar-large">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-info">
            <h3 class="user-name">{{ getFullName(user) }}</h3>
            <p class="user-email">{{ user.email }}</p>
            <div class="user-status">
              <span :class="['status-badge', user.is_active ? 'active' : 'inactive']">
                <i :class="user.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                {{ user.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="details-section">
          <h3 class="section-title">
            <i class="fas fa-info-circle"></i>
            Basic Information
          </h3>
          <div class="details-grid">
            <div class="detail-item">
              <label class="detail-label">Username</label>
              <span class="detail-value">{{ user.username }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Email</label>
              <span class="detail-value">{{ user.email }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">First Name</label>
              <span class="detail-value">{{ user.first_name || 'Not provided' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Last Name</label>
              <span class="detail-value">{{ user.last_name || 'Not provided' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Phone</label>
              <span class="detail-value">{{ user.phone || 'Not provided' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Subsidiary</label>
              <span class="detail-value">
                {{ user.subsidiary?.name || 'N/A' }}
                <span v-if="user.subsidiary?.code" class="subsidiary-code">
                  ({{ user.subsidiary.code }})
                </span>
              </span>
            </div>
          </div>
        </div>

        <!-- Account Information -->
        <div class="details-section">
          <h3 class="section-title">
            <i class="fas fa-clock"></i>
            Account Information
          </h3>
          <div class="details-grid">
            <div class="detail-item">
              <label class="detail-label">Created</label>
              <span class="detail-value">{{ formatDate(user.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Last Updated</label>
              <span class="detail-value">{{ formatDate(user.updated_at) }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Last Login</label>
              <span class="detail-value">{{ formatDate(user.last_login_at) || 'Never' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Login Attempts</label>
              <span class="detail-value">{{ user.login_attempts || 0 }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Account Status</label>
              <span class="detail-value">
                <span :class="['status-badge', user.is_active ? 'active' : 'inactive']">
                  {{ user.is_active ? 'Active' : 'Inactive' }}
                </span>
              </span>
            </div>
            <div class="detail-item">
              <label class="detail-label">Account Locked</label>
              <span class="detail-value">
                <span :class="['status-badge', isAccountLocked ? 'inactive' : 'active']">
                  {{ isAccountLocked ? 'Yes' : 'No' }}
                </span>
              </span>
            </div>
          </div>
        </div>

        <!-- Roles and Permissions -->
        <div class="details-section">
          <h3 class="section-title">
            <i class="fas fa-shield-alt"></i>
            Roles & Permissions
          </h3>
          
          <div v-if="user.roles && user.roles.length > 0" class="roles-container">
            <div v-for="role in user.roles" :key="role.id" class="role-detail-card">
              <div class="role-header">
                <div class="role-info">
                  <i :class="getRoleIcon(role.hierarchy_level)"></i>
                  <span class="role-name">{{ role.name }}</span>
                  <span class="role-hierarchy">Level {{ role.hierarchy_level }}</span>
                </div>
              </div>
              
              <p class="role-description">{{ role.description }}</p>
              
              <div class="permissions-section">
                <h4 class="permissions-title">Permissions ({{ role.permissions.length }})</h4>
                <div class="permissions-grid">
                  <div
                    v-for="(permissions, category) in groupedPermissions(role.permissions)"
                    :key="category"
                    class="permission-category"
                  >
                    <h5 class="category-title">
                      <i :class="getPermissionCategoryIcon(category)"></i>
                      {{ getPermissionCategoryName(category) }}
                    </h5>
                    <div class="permission-list">
                      <span
                        v-for="permission in permissions"
                        :key="permission"
                        class="permission-badge"
                      >
                        {{ formatPermission(permission) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="no-roles">
            <i class="fas fa-exclamation-triangle"></i>
            <p>No roles assigned to this user</p>
          </div>
        </div>

        <!-- Loading Permissions -->
        <div v-if="loadingPermissions" class="loading-section">
          <div class="loading-spinner"></div>
          <p>Loading detailed permissions...</p>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="error-alert">
          <i class="fas fa-exclamation-triangle"></i>
          {{ error }}
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">
          Close
        </button>
        <button 
          v-if="canEdit"
          @click="$emit('edit', user)" 
          class="btn btn-primary"
        >
          <i class="fas fa-edit"></i>
          Edit User
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/userService'

export default {
  name: 'UserDetailsModal',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'edit'],
  setup(props, { emit }) {
    const authStore = useAuthStore()

    // Reactive data
    const loadingPermissions = ref(false)
    const error = ref(null)
    const userPermissions = ref([])
    
    // Computed properties
    const currentUser = computed(() => authStore.user)
    const currentUserPermissions = computed(() => authStore.permissions)
    
    const canEdit = computed(() => {
      return currentUserPermissions.value.includes('users:write') && 
             (currentUser.value.id !== props.user.id || currentUserPermissions.value.includes('system:admin'))
    })
    
    const isAccountLocked = computed(() => {
      return props.user.locked_until && new Date(props.user.locked_until) > new Date()
    })
    
    // Methods
    const handleOverlayClick = () => {
      emit('close')
    }

    const getFullName = (user) => {
      if (user.first_name && user.last_name) {
        return `${user.first_name} ${user.last_name}`
      } else if (user.first_name) {
        return user.first_name
      } else if (user.last_name) {
        return user.last_name
      }
      return user.username
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return null
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const getRoleIcon = (hierarchyLevel) => {
      const hierarchy = userService.getRoleHierarchy()
      return `fas fa-${hierarchy[hierarchyLevel]?.icon || 'user'}`
    }
    
    const formatPermission = (permission) => {
      const permissionMap = {
        'users:read': 'View Users',
        'users:write': 'Create/Edit Users',
        'users:delete': 'Delete Users',
        'files:read': 'View Files',
        'files:write': 'Upload/Edit Files',
        'files:delete': 'Delete Files',
        'files:download': 'Download Files',
        'analytics:read': 'View Analytics',
        'system:admin': 'System Administration',
        'audit:read': 'View Audit Logs',
        'roles:read': 'View Roles',
        'roles:write': 'Manage Roles',
        'subsidiaries:read': 'View Subsidiaries',
        'subsidiaries:write': 'Manage Subsidiaries'
      }
      return permissionMap[permission] || permission.split(':')[1]?.replace('_', ' ') || permission
    }
    
    const groupedPermissions = (permissions) => {
      try {
        const categories = userService.getPermissionCategories()
        const grouped = {}

        permissions.forEach(permission => {
          const category = permission.split(':')[0]
          if (!grouped[category]) {
            grouped[category] = []
          }
          grouped[category].push(permission)
        })

        return grouped
      } catch (error) {
        console.error('Error grouping permissions:', error)
        return {}
      }
    }

    const getPermissionCategoryName = (category) => {
      try {
        const categories = userService.getPermissionCategories()
        return categories[category]?.name || category.charAt(0).toUpperCase() + category.slice(1)
      } catch (error) {
        console.error('Error getting category name:', error)
        return category.charAt(0).toUpperCase() + category.slice(1)
      }
    }

    const getPermissionCategoryIcon = (category) => {
      try {
        const categories = userService.getPermissionCategories()
        return `fas fa-${categories[category]?.icon || 'cog'}`
      } catch (error) {
        console.error('Error getting category icon:', error)
        return 'fas fa-cog'
      }
    }
    
    const loadUserPermissions = async () => {
      try {
        loadingPermissions.value = true
        const response = await userService.getUserPermissions(props.user.id)
        userPermissions.value = response.permissions
      } catch (err) {
        error.value = 'Failed to load user permissions'
        console.error('Error loading user permissions:', err)
      } finally {
        loadingPermissions.value = false
      }
    }
    
    // Lifecycle
    onMounted(() => {
      console.log('UserDetailsModal mounted with user:', props.user)
      if (props.user.id) {
        loadUserPermissions()
      }
    })
    
    return {
      loadingPermissions,
      error,
      userPermissions,
      canEdit,
      isAccountLocked,
      handleOverlayClick,
      getFullName,
      formatDate,
      getRoleIcon,
      formatPermission,
      groupedPermissions,
      getPermissionCategoryName,
      getPermissionCategoryIcon
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-title i {
  color: #3b82f6;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 2rem;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* User Profile Section */
.user-profile {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 2rem;
  color: white;
}

.user-avatar-large {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  backdrop-filter: blur(10px);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.user-email {
  font-size: 1.1rem;
  margin: 0 0 0.75rem 0;
  opacity: 0.9;
}

.user-status .status-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Details Sections */
.details-section {
  margin-bottom: 2rem;
}

.details-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #4f46e5;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.detail-item {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.detail-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
}

.subsidiary-code {
  color: #6b7280;
  font-weight: 400;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.active {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Roles Section */
.roles-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.role-detail-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.role-header {
  margin-bottom: 1rem;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.role-info i {
  color: #4f46e5;
  font-size: 1.25rem;
}

.role-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.role-hierarchy {
  background: #e0e7ff;
  color: #4338ca;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.role-description {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.permissions-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.permissions-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.permission-category {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.category-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-title i {
  color: #6b7280;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.permission-badge {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-transform: capitalize;
  border: 1px solid #e5e7eb;
}

.no-roles {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-roles i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #f59e0b;
}

.loading-section {
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-alert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .modal-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .user-profile {
    flex-direction: column;
    text-align: center;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .permissions-grid {
    grid-template-columns: 1fr;
  }

  .modal-header, .modal-footer {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }
}
</style>
