<template>
  <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
    <!-- Header -->
    <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
        </svg>
        Upload Audio Files
      </h3>
      <p class="text-sm text-gray-600 mt-1">Drag and drop your audio files or click to browse</p>
    </div>

    <!-- Upload Area -->
    <div class="p-6">
      <div
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
        :class="[
          'relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300',
          isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        ]"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac,.wma"
          @change="handleFileSelect"
          class="hidden"
        />

        <div class="space-y-4">
          <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          
          <div>
            <p class="text-lg font-medium text-gray-900">
              {{ isDragging ? 'Drop files here' : 'Choose audio files or drag them here' }}
            </p>
            <p class="text-sm text-gray-500 mt-1">
              Supports MP3, WAV, OGG, M4A, AAC, FLAC, WMA (max 100MB each)
            </p>
          </div>

          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Browse Files
          </button>
        </div>
      </div>

      <!-- File List -->
      <div v-if="files.length > 0" class="mt-6 space-y-3">
        <h4 class="text-sm font-medium text-gray-900">Selected Files ({{ files.length }})</h4>
        
        <div class="space-y-2 max-h-60 overflow-y-auto">
          <div
            v-for="(file, index) in files"
            :key="index"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
          >
            <div class="flex items-center flex-1 min-w-0">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              
              <div class="ml-3 flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
              </div>
            </div>

            <!-- Upload Progress -->
            <div v-if="file.uploading" class="flex items-center ml-3">
              <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                <div
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: file.progress + '%' }"
                ></div>
              </div>
              <span class="text-xs text-gray-500 w-8">{{ file.progress }}%</span>
            </div>

            <!-- Status Icons -->
            <div class="flex items-center ml-3 space-x-2">
              <svg v-if="file.uploaded" class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              
              <svg v-else-if="file.error" class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>

              <button
                @click="removeFile(index)"
                :disabled="file.uploading"
                class="text-gray-400 hover:text-red-500 transition-colors disabled:opacity-50"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div v-if="files.length > 0" class="mt-6 flex items-center justify-between">
        <button
          @click="clearFiles"
          :disabled="isUploading"
          class="text-sm text-gray-500 hover:text-gray-700 transition-colors disabled:opacity-50"
        >
          Clear All
        </button>

        <div class="flex space-x-3">
          <button
            @click="$emit('cancel')"
            :disabled="isUploading"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          
          <button
            @click="startUpload"
            :disabled="!canUpload"
            class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:bg-gray-400"
          >
            <svg v-if="isUploading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isUploading ? 'Uploading...' : `Upload ${files.length} File${files.length > 1 ? 's' : ''}` }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

const toast = useToast()

// Props & Emits
const emit = defineEmits(['upload-complete', 'cancel'])

// State
const files = ref([])
const isDragging = ref(false)
const fileInput = ref(null)

// Computed
const isUploading = computed(() => files.value.some(f => f.uploading))
const canUpload = computed(() => files.value.length > 0 && !isUploading.value)

// File validation
const ALLOWED_TYPES = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/aac', 'audio/flac', 'audio/x-ms-wma']
const ALLOWED_EXTENSIONS = ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac', 'wma']
const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

// Drag & Drop handlers
const handleDragEnter = (e) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e) => {
  e.preventDefault()
}

const handleDragLeave = (e) => {
  e.preventDefault()
  if (!e.currentTarget.contains(e.relatedTarget)) {
    isDragging.value = false
  }
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragging.value = false
  
  const droppedFiles = Array.from(e.dataTransfer.files)
  addFiles(droppedFiles)
}

// File selection
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (e) => {
  const selectedFiles = Array.from(e.target.files)
  addFiles(selectedFiles)
  e.target.value = '' // Reset input
}

// File management
const addFiles = (newFiles) => {
  const validFiles = newFiles.filter(validateFile)
  
  validFiles.forEach(file => {
    // Check for duplicates
    const exists = files.value.some(f => f.name === file.name && f.size === file.size)
    if (!exists) {
      files.value.push({
        file,
        name: file.name,
        size: file.size,
        progress: 0,
        uploading: false,
        uploaded: false,
        error: null
      })
    }
  })
}

const validateFile = (file) => {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    toast.error(`File "${file.name}" is too large. Maximum size is 100MB.`)
    return false
  }

  // Check file type
  const extension = file.name.split('.').pop()?.toLowerCase()
  if (!ALLOWED_EXTENSIONS.includes(extension) && !ALLOWED_TYPES.includes(file.type)) {
    toast.error(`File "${file.name}" is not a supported audio format.`)
    return false
  }

  return true
}

const removeFile = (index) => {
  files.value.splice(index, 1)
}

const clearFiles = () => {
  files.value = []
}

// Upload functionality
const startUpload = async () => {
  const filesToUpload = files.value.filter(f => !f.uploaded && !f.error)
  
  for (const fileItem of filesToUpload) {
    await uploadFile(fileItem)
  }

  // Check if all uploads completed successfully
  const successCount = files.value.filter(f => f.uploaded).length
  const errorCount = files.value.filter(f => f.error).length

  if (successCount > 0) {
    toast.success(`Successfully uploaded ${successCount} file${successCount > 1 ? 's' : ''}`)
    emit('upload-complete', { success: successCount, errors: errorCount })
  }

  if (errorCount > 0) {
    toast.error(`Failed to upload ${errorCount} file${errorCount > 1 ? 's' : ''}`)
  }
}

const uploadFile = async (fileItem) => {
  try {
    fileItem.uploading = true
    fileItem.error = null

    const formData = new FormData()
    formData.append('audioFile', fileItem.file)
    formData.append('title', fileItem.name)

    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        fileItem.progress = progress
      }
    })

    if (response.data.success) {
      fileItem.uploaded = true
      fileItem.progress = 100
    }
  } catch (error) {
    console.error('Upload error:', error)
    fileItem.error = error.response?.data?.message || 'Upload failed'
    toast.error(`Failed to upload ${fileItem.name}: ${fileItem.error}`)
  } finally {
    fileItem.uploading = false
  }
}

// Utility functions
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
