<template>
  <div class="admin-sidebar h-full flex flex-col bg-white">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-bold text-gray-900">User Management</h2>
          <p class="text-sm text-gray-600">Manage users & roles</p>
        </div>
      </div>
      <button
        @click="$emit('refresh')"
        :disabled="loading"
        class="p-2 text-gray-500 hover:text-indigo-600 hover:bg-indigo-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        title="Refresh data"
      >
        <svg
          class="w-4 h-4"
          :class="{ 'animate-spin': loading }"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
      </button>
    </div>

    <!-- Sidebar Content -->
    <div class="flex-1 overflow-y-auto p-6 space-y-6">
      <!-- Stats Cards -->
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-4 rounded-xl border border-blue-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-700">Total Users</p>
              <p class="text-2xl font-bold text-blue-900">{{ stats.totalUsers }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-4 rounded-xl border border-green-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-green-700">Active Users</p>
              <p class="text-2xl font-bold text-green-900">{{ stats.activeUsers }}</p>
            </div>
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div>
        <h3 class="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h3>
        <div class="space-y-2">
          <button 
            @click="$emit('add-user')"
            class="w-full flex items-center space-x-3 p-3 text-left bg-gradient-to-r from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 rounded-xl border border-indigo-100 transition-all duration-200 group"
          >
            <div class="w-8 h-8 bg-indigo-100 group-hover:bg-indigo-200 rounded-lg flex items-center justify-center transition-colors">
              <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            <div>
              <p class="font-medium text-gray-900">Add New User</p>
              <p class="text-sm text-gray-600">Create a new user account</p>
            </div>
          </button>

          <button 
            @click="$emit('view-all')"
            class="w-full flex items-center space-x-3 p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-xl border border-gray-200 transition-all duration-200 group"
          >
            <div class="w-8 h-8 bg-gray-100 group-hover:bg-gray-200 rounded-lg flex items-center justify-center transition-colors">
              <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
              </svg>
            </div>
            <div>
              <p class="font-medium text-gray-900">View All Users</p>
              <p class="text-sm text-gray-600">Browse complete user list</p>
            </div>
          </button>
        </div>
      </div>

      <!-- Recent Users -->
      <div>
        <h3 class="text-sm font-semibold text-gray-900 mb-3">Recent Users</h3>
        <div class="space-y-3">
          <!-- Loading state -->
          <div v-if="loading" class="space-y-3">
            <div v-for="i in 3" :key="i" class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl animate-pulse">
              <div class="w-10 h-10 bg-gray-300 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                <div class="h-3 bg-gray-300 rounded w-1/2"></div>
              </div>
              <div class="w-16 h-6 bg-gray-300 rounded-full"></div>
            </div>
          </div>

          <!-- Actual users -->
          <div v-else>
            <div
              v-for="user in recentUsers"
              :key="user.id"
              class="flex items-center space-x-3 p-3 bg-white rounded-xl border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all duration-200 cursor-pointer"
              @click="$emit('view-user', user)"
            >
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                {{ getUserInitials(user) }}
              </div>
              <div class="flex-1 min-w-0">
                <p class="font-medium text-gray-900 truncate">{{ user.username }}</p>
                <p class="text-sm text-gray-600 truncate">{{ user.email }}</p>
              </div>
              <div class="flex-shrink-0">
                <span
                  :class="[
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    user.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ user.is_active ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>

            <div v-if="recentUsers.length === 0 && !loading" class="text-center py-8 text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
              <p class="text-sm">No users found</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Distribution -->
      <div>
        <h3 class="text-sm font-semibold text-gray-900 mb-3">Role Distribution</h3>
        <div class="space-y-2">
          <div 
            v-for="role in roleStats" 
            :key="role.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-2">
              <div 
                :class="[
                  'w-3 h-3 rounded-full',
                  getRoleColor(role.hierarchy_level)
                ]"
              ></div>
              <span class="text-sm font-medium text-gray-900">{{ role.name }}</span>
            </div>
            <span class="text-sm text-gray-600">{{ role.userCount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  users: {
    type: Array,
    default: () => []
  },
  roles: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['add-user', 'view-all', 'view-user', 'refresh'])

// Computed properties
const stats = computed(() => ({
  totalUsers: props.users.length,
  activeUsers: props.users.filter(user => user.is_active).length
}))

const recentUsers = computed(() => {
  return props.users
    .slice()
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    .slice(0, 5)
})

const roleStats = computed(() => {
  const roleCounts = {}
  
  // Initialize role counts
  props.roles.forEach(role => {
    roleCounts[role.id] = {
      ...role,
      userCount: 0
    }
  })
  
  // Count users per role
  props.users.forEach(user => {
    if (user.roles && user.roles.length > 0) {
      user.roles.forEach(role => {
        if (roleCounts[role.id]) {
          roleCounts[role.id].userCount++
        }
      })
    }
  })
  
  return Object.values(roleCounts).sort((a, b) => b.userCount - a.userCount)
})

// Methods
const getUserInitials = (user) => {
  if (user.first_name && user.last_name) {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase()
  }
  return user.username?.charAt(0).toUpperCase() || 'U'
}

const getRoleColor = (hierarchyLevel) => {
  const colors = {
    4: 'bg-red-500',    // Admin
    3: 'bg-yellow-500', // Manager
    2: 'bg-blue-500',   // User
    1: 'bg-gray-500'    // Basic
  }
  return colors[hierarchyLevel] || 'bg-gray-500'
}
</script>

<style scoped>
.admin-sidebar {
  min-height: calc(100vh - 4rem); /* Adjust based on header height */
}

/* Custom scrollbar */
.admin-sidebar ::-webkit-scrollbar {
  width: 4px;
}

.admin-sidebar ::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.admin-sidebar ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.admin-sidebar ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
