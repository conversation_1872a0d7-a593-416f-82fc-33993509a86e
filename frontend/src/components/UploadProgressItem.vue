<template>
  <div class="bg-white/95 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
    <!-- File Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3 flex-1 min-w-0">
        <!-- File Icon -->
        <div :class="[
          'w-12 h-12 rounded-xl flex items-center justify-center shadow-md transition-all duration-300',
          getStatusIconClass()
        ]">
          <svg v-if="file.status === 'uploading'" class="w-6 h-6 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
          </svg>
          <svg v-else-if="file.status === 'completed'" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <svg v-else-if="file.status === 'error'" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          <svg v-else-if="file.status === 'paused'" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <svg v-else class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
          </svg>
        </div>

        <!-- File Info -->
        <div class="flex-1 min-w-0">
          <h4 class="font-semibold text-gray-900 truncate">{{ file.name }}</h4>
          <div class="flex items-center space-x-4 text-sm text-gray-500 mt-1">
            <span>{{ formatFileSize(file.size) }}</span>
            <span v-if="file.format" class="px-2 py-1 bg-gray-100 rounded-full text-xs font-medium">
              {{ file.format.toUpperCase() }}
            </span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center space-x-2 ml-4">
        <!-- Pause/Resume Button -->
        <button
          v-if="file.status === 'uploading'"
          @click="$emit('pause', file.id)"
          class="p-2 text-gray-500 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
          title="Pause upload"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </button>

        <button
          v-else-if="file.status === 'paused'"
          @click="$emit('resume', file.id)"
          class="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
          title="Resume upload"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </button>

        <!-- Retry Button -->
        <button
          v-if="file.status === 'error'"
          @click="$emit('retry', file.id)"
          class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
          title="Retry upload"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>

        <!-- Remove Button -->
        <button
          @click="$emit('remove', file.id)"
          class="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          title="Remove file"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Progress Section -->
    <div v-if="file.status !== 'pending'" class="space-y-3">
      <!-- Progress Bar -->
      <div class="relative">
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm font-medium text-gray-700">
            {{ getStatusText() }}
          </span>
          <span class="text-sm font-semibold text-gray-900">
            {{ file.progress }}%
          </span>
        </div>
        
        <!-- Progress Bar Container -->
        <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden shadow-inner">
          <div 
            class="h-full rounded-full transition-all duration-500 ease-out relative overflow-hidden"
            :class="getProgressBarClass()"
            :style="{ width: `${file.progress}%` }"
          >
            <!-- Animated shine effect -->
            <div v-if="file.status === 'uploading'" class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
          </div>
        </div>
      </div>

      <!-- Upload Stats -->
      <div v-if="file.status === 'uploading' || file.status === 'paused'" class="flex justify-between items-center text-sm">
        <div class="flex items-center space-x-4">
          <!-- Upload Speed -->
          <div v-if="file.uploadSpeed" class="flex items-center space-x-1 text-blue-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <span class="font-medium">{{ file.uploadSpeed }}</span>
          </div>

          <!-- Uploaded Amount -->
          <div class="text-gray-600">
            <span class="font-medium">{{ formatFileSize(file.uploadedBytes || 0) }}</span>
            <span class="text-gray-400"> / {{ formatFileSize(file.size) }}</span>
          </div>
        </div>

        <!-- Time Remaining -->
        <div v-if="file.timeRemaining" class="flex items-center space-x-1 text-gray-600">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">{{ file.timeRemaining }}</span>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="file.status === 'error' && file.error" class="p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-start space-x-2">
          <svg class="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <p class="text-sm font-medium text-red-800">Upload failed</p>
            <p class="text-sm text-red-600 mt-1">{{ file.error }}</p>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="file.status === 'completed'" class="p-3 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <div>
            <p class="text-sm font-medium text-green-800">Upload completed successfully</p>
            <p v-if="file.uploadDuration" class="text-sm text-green-600">
              Completed in {{ file.uploadDuration }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  file: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['pause', 'resume', 'retry', 'remove'])

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getStatusIconClass = () => {
  switch (props.file.status) {
    case 'uploading':
      return 'bg-gradient-to-br from-blue-500 to-indigo-600'
    case 'completed':
      return 'bg-gradient-to-br from-green-500 to-emerald-600'
    case 'error':
      return 'bg-gradient-to-br from-red-500 to-red-600'
    case 'paused':
      return 'bg-gradient-to-br from-orange-500 to-orange-600'
    default:
      return 'bg-gradient-to-br from-gray-500 to-gray-600'
  }
}

const getProgressBarClass = () => {
  switch (props.file.status) {
    case 'uploading':
      return 'bg-gradient-to-r from-blue-500 to-indigo-600'
    case 'completed':
      return 'bg-gradient-to-r from-green-500 to-emerald-600'
    case 'error':
      return 'bg-gradient-to-r from-red-500 to-red-600'
    case 'paused':
      return 'bg-gradient-to-r from-orange-500 to-orange-600'
    default:
      return 'bg-gradient-to-r from-gray-500 to-gray-600'
  }
}

const getStatusText = () => {
  switch (props.file.status) {
    case 'uploading':
      return 'Uploading...'
    case 'completed':
      return 'Upload completed'
    case 'error':
      return 'Upload failed'
    case 'paused':
      return 'Upload paused'
    default:
      return 'Preparing...'
  }
}
</script>
