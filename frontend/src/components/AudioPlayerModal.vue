<template>
  <div class="fixed inset-0 backdrop-blur-[1px] overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-8 w-11/12 md:w-3/4 lg:w-1/2 xl:w-2/5 shadow-2xl rounded-2xl bg-gradient-to-br from-white/95 to-gray-50/95 backdrop-blur-xl border border-gray-200/50" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900">Audio Player</h3>
        </div>
        <button
          @click="$emit('close')"
          class="w-10 h-10 bg-gray-100/80 hover:bg-gray-200/80 rounded-xl flex items-center justify-center transition-all duration-200 transform hover:scale-105 group backdrop-blur-sm"
          title="Close"
        >
          <svg class="w-5 h-5 text-gray-600 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- File Info Card -->
      <div class="mb-8 p-6 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-2xl border border-gray-200/50 backdrop-blur-sm">
        <div class="flex items-center space-x-5">
          <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center shadow-inner">
            <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          <div class="flex-1">
            <h4 class="text-xl font-semibold text-gray-900 mb-2 truncate">{{ file.name }}</h4>
            <div class="flex items-center space-x-6 text-sm text-gray-600">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <span class="font-medium">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4h10v16l-5-3-5 3V4z"></path>
                </svg>
                <span class="uppercase font-medium">{{ file.extension }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m4 0H4a1 1 0 00-1 1v10a1 1 0 001 1h16a1 1 0 001-1V8a1 1 0 00-1-1z"></path>
                </svg>
                <span>{{ formatDate(file.lastModified) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Custom Audio Player -->
      <div class="mb-8">
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="relative">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200"></div>
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
          </div>
          <span class="ml-4 text-gray-700 font-medium">Loading audio...</span>
        </div>

        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-500 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-600 font-semibold text-lg mb-2">Failed to load audio</p>
          <p class="text-gray-500 mb-6">{{ error }}</p>
          <button
            @click="loadAudio"
            class="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            Try Again
          </button>
        </div>

        <!-- Custom Audio Player Controls -->
        <div v-else class="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-6 shadow-2xl">
          <!-- Hidden HTML5 Audio Element -->
          <audio
            ref="audioPlayer"
            :src="audioUrl"
            @canplay="handleCanPlay"
            @error="handleAudioError"
            @timeupdate="updateProgress"
            @loadedmetadata="updateDuration"
            @ended="handleAudioEnded"
            preload="metadata"
            class="hidden"
          >
            Your browser does not support the audio element.
          </audio>

          <!-- Waveform Visualization (Placeholder) -->
          <div class="mb-6">
            <div class="flex items-center justify-center h-20 bg-gray-800/50 rounded-xl border border-gray-700/50">
              <div class="flex items-end space-x-1 h-12">
                <div v-for="i in 40" :key="i"
                     class="bg-gradient-to-t from-blue-500 to-purple-500 rounded-full transition-all duration-300"
                     :class="isPlaying ? 'animate-pulse' : ''"
                     :style="{
                       width: '3px',
                       height: `${Math.random() * 100 + 20}%`,
                       animationDelay: `${i * 50}ms`
                     }">
                </div>
              </div>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="mb-6">
            <div class="flex items-center justify-between text-sm text-gray-400 mb-2">
              <span class="font-mono">{{ formatTime(currentTime) }}</span>
              <span class="font-mono">{{ formatTime(duration) }}</span>
            </div>
            <div class="relative">
              <div class="w-full h-2 bg-gray-700 rounded-full cursor-pointer" @click="seekAudio">
                <div
                  class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-150 relative"
                  :style="{ width: progressPercentage + '%' }"
                >
                  <div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-blue-500"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Control Buttons -->
          <div class="flex items-center justify-center space-x-6">
            <!-- Previous Button (Placeholder) -->
            <button class="w-12 h-12 bg-gray-700/50 hover:bg-gray-600/50 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105" disabled>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <!-- Play/Pause Button -->
            <button
              @click="togglePlayPause"
              class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <svg v-if="!isPlaying" class="w-7 h-7 text-white ml-1 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
              <svg v-else class="w-7 h-7 text-white transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
              </svg>
            </button>

            <!-- Next Button (Placeholder) -->
            <button class="w-12 h-12 bg-gray-700/50 hover:bg-gray-600/50 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105" disabled>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>

          <!-- Volume Control -->
          <div class="flex items-center justify-center mt-6 space-x-4">
            <button @click="toggleMute" class="text-gray-400 hover:text-white transition-colors">
              <svg v-if="!isMuted && volume > 0" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
              </svg>
            </button>
            <div class="w-24 h-1 bg-gray-700 rounded-full cursor-pointer" @click="setVolume">
              <div
                class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                :style="{ width: (isMuted ? 0 : volume * 100) + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Actions -->
      <div class="flex justify-end items-center space-x-4">
        <!-- Download Button -->
        <div class="flex flex-col items-center space-y-1">
          <button
            @click="downloadFile"
            :disabled="downloading"
            class="w-12 h-12 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl group"
            title="Download audio file"
          >
            <svg class="w-5 h-5 text-white transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 10v6m0 0l-3-3m3 3l3-3" />
            </svg>
          </button>
          <span class="text-xs text-gray-600 font-medium">Download</span>
        </div>

        <!-- Close Button -->
        <div class="flex flex-col items-center space-y-1">
          <button
            @click="$emit('close')"
            class="w-12 h-12 bg-gray-500 hover:bg-gray-600 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl group"
            title="Close audio player"
          >
            <svg class="w-5 h-5 text-white transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <span class="text-xs text-gray-600 font-medium">Close</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useFilesStore } from '@/stores/files'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const props = defineProps({
  file: {
    type: Object,
    required: true
  }
})

defineEmits(['close'])

const filesStore = useFilesStore()
const toast = useToast()

const audioPlayer = ref(null)
const audioUrl = ref('')
const loading = ref(true)
const error = ref('')
const downloading = ref(false)

// Audio player state
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(false)

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (seconds) => {
  if (isNaN(seconds)) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const progressPercentage = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})



const loadAudio = async () => {
  try {
    console.log('loadAudio called for file:', props.file.name)
    loading.value = true
    error.value = ''

    // Use the new streaming URL endpoint that generates signed URLs without attachment headers
    const streamResult = await filesStore.generateStreamUrl(props.file.key)
    console.log('Stream URL generated:', streamResult.url)

    // Set the audio source to the signed streaming URL
    audioUrl.value = streamResult.url
    console.log('Audio source set to:', audioUrl.value)

  } catch (err) {
    console.error('Error loading audio:', err)
    error.value = 'Failed to load audio file'
  } finally {
    loading.value = false
  }
}

const handleCanPlay = () => {
  console.log('Audio can play - ready to use')
  loading.value = false
  error.value = ''
  if (audioPlayer.value) {
    audioPlayer.value.volume = volume.value
  }
}

const handleAudioError = (event) => {
  console.error('Audio playback error:', event)
  console.error('Audio error details:', event.target.error)
  console.error('Audio src:', event.target.src)
  error.value = `Unable to play this audio file: ${event.target.error?.message || 'MEDIA_ELEMENT_ERROR: Format error'}`
  loading.value = false
}

const togglePlayPause = () => {
  if (!audioPlayer.value) return

  if (isPlaying.value) {
    audioPlayer.value.pause()
    isPlaying.value = false
  } else {
    audioPlayer.value.play()
    isPlaying.value = true
  }
}

const updateProgress = () => {
  if (audioPlayer.value) {
    currentTime.value = audioPlayer.value.currentTime
  }
}

const updateDuration = () => {
  if (audioPlayer.value) {
    duration.value = audioPlayer.value.duration
  }
}

const handleAudioEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
}

const seekAudio = (event) => {
  if (!audioPlayer.value) return

  const rect = event.target.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * duration.value

  audioPlayer.value.currentTime = newTime
  currentTime.value = newTime
}

const toggleMute = () => {
  if (!audioPlayer.value) return

  isMuted.value = !isMuted.value
  audioPlayer.value.muted = isMuted.value
}

const setVolume = (event) => {
  if (!audioPlayer.value) return

  const rect = event.target.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const newVolume = clickX / rect.width

  volume.value = Math.max(0, Math.min(1, newVolume))
  audioPlayer.value.volume = volume.value

  if (volume.value > 0) {
    isMuted.value = false
    audioPlayer.value.muted = false
  }
}

const downloadFile = async () => {
  try {
    downloading.value = true
    await filesStore.downloadFile(props.file)
    toast.success(`Downloading ${props.file.name}`)
  } catch (err) {
    console.error('Download error:', err)
    toast.error('Failed to download file')
  } finally {
    downloading.value = false
  }
}

onMounted(() => {
  loadAudio()

  // Add keyboard shortcuts
  const handleKeyPress = (event) => {
    if (event.code === 'Space') {
      event.preventDefault()
      togglePlayPause()
    } else if (event.code === 'ArrowLeft') {
      event.preventDefault()
      if (audioPlayer.value) {
        audioPlayer.value.currentTime = Math.max(0, audioPlayer.value.currentTime - 10)
      }
    } else if (event.code === 'ArrowRight') {
      event.preventDefault()
      if (audioPlayer.value) {
        audioPlayer.value.currentTime = Math.min(duration.value, audioPlayer.value.currentTime + 10)
      }
    }
  }

  document.addEventListener('keydown', handleKeyPress)

  // Cleanup function
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyPress)
  })
})

onUnmounted(() => {
  // Pause audio when component is destroyed
  if (audioPlayer.value) {
    audioPlayer.value.pause()
    isPlaying.value = false
  }
})
</script>
