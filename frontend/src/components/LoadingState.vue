<template>
  <!-- Enhanced Loading State Component -->
  <div class="loading-state" :class="containerClasses">
    <!-- Skeleton Loading -->
    <div v-if="type === 'skeleton'" class="space-y-4">
      <div v-for="n in skeletonLines" :key="n" class="animate-pulse">
        <div class="h-4 bg-gray-200 rounded" :style="{ width: getSkeletonWidth(n) }"></div>
      </div>
    </div>

    <!-- Spinner Loading -->
    <div v-else-if="type === 'spinner'" class="flex items-center justify-center">
      <div class="relative">
        <!-- Outer ring -->
        <div class="w-12 h-12 rounded-full border-4 border-gray-200"></div>
        <!-- Inner spinning ring -->
        <div class="absolute inset-0 w-12 h-12 rounded-full border-4 border-transparent border-t-indigo-600 animate-spin"></div>
      </div>
      <div v-if="message" class="ml-4">
        <p class="text-gray-700 font-medium">{{ message }}</p>
        <p v-if="submessage" class="text-sm text-gray-500">{{ submessage }}</p>
      </div>
    </div>

    <!-- Dots Loading -->
    <div v-else-if="type === 'dots'" class="flex items-center justify-center space-x-2">
      <div
        v-for="n in 3"
        :key="n"
        class="w-3 h-3 bg-indigo-600 rounded-full animate-bounce"
        :style="{ animationDelay: `${(n - 1) * 0.2}s` }"
      ></div>
      <span v-if="message" class="ml-4 text-gray-700">{{ message }}</span>
    </div>

    <!-- Progress Bar Loading -->
    <div v-else-if="type === 'progress'" class="w-full">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">{{ message || 'Loading...' }}</span>
        <span class="text-sm text-gray-500">{{ Math.round(progress) }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-indigo-600 h-2 rounded-full transition-all duration-300 ease-out"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
      <p v-if="submessage" class="text-xs text-gray-500 mt-1">{{ submessage }}</p>
    </div>

    <!-- Pulse Loading -->
    <div v-else-if="type === 'pulse'" class="flex items-center justify-center">
      <div class="relative">
        <div class="w-16 h-16 bg-indigo-100 rounded-full animate-pulse"></div>
        <div class="absolute inset-0 w-16 h-16 bg-indigo-200 rounded-full animate-ping opacity-75"></div>
        <div class="absolute inset-4 w-8 h-8 bg-indigo-600 rounded-full"></div>
      </div>
      <div v-if="message" class="ml-4">
        <p class="text-gray-700 font-medium">{{ message }}</p>
        <p v-if="submessage" class="text-sm text-gray-500">{{ submessage }}</p>
      </div>
    </div>

    <!-- Card Loading (for file lists) -->
    <div v-else-if="type === 'cards'" class="space-y-4">
      <div v-for="n in cardCount" :key="n" class="animate-pulse">
        <div class="bg-white rounded-xl border border-gray-200 p-4">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table Loading -->
    <div v-else-if="type === 'table'" class="space-y-3">
      <div class="animate-pulse">
        <!-- Table header -->
        <div class="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-t-xl">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
        </div>
        <!-- Table rows -->
        <div v-for="n in tableRows" :key="n" class="grid grid-cols-4 gap-4 p-4 border-b border-gray-100">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <!-- Audio Loading -->
    <div v-else-if="type === 'audio'" class="flex items-center justify-center p-8">
      <div class="text-center">
        <div class="relative mb-4">
          <div class="w-20 h-20 mx-auto">
            <!-- Audio wave animation -->
            <div class="flex items-end justify-center h-full space-x-1">
              <div
                v-for="n in 5"
                :key="n"
                class="w-2 bg-indigo-600 rounded-full animate-pulse"
                :style="{
                  height: `${20 + Math.sin((Date.now() / 200) + n) * 15}px`,
                  animationDelay: `${n * 0.1}s`
                }"
              ></div>
            </div>
          </div>
        </div>
        <p class="text-lg font-medium text-gray-900">{{ message || 'Loading audio...' }}</p>
        <p v-if="submessage" class="text-sm text-gray-500 mt-1">{{ submessage }}</p>
      </div>
    </div>

    <!-- Default Spinner -->
    <div v-else class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      <span v-if="message" class="ml-3 text-gray-600">{{ message }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'spinner',
    validator: (value) => [
      'spinner', 'dots', 'progress', 'pulse', 'skeleton', 
      'cards', 'table', 'audio'
    ].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  submessage: {
    type: String,
    default: ''
  },
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  skeletonLines: {
    type: Number,
    default: 3
  },
  cardCount: {
    type: Number,
    default: 3
  },
  tableRows: {
    type: Number,
    default: 5
  },
  fullHeight: {
    type: Boolean,
    default: false
  },
  overlay: {
    type: Boolean,
    default: false
  }
})

const containerClasses = computed(() => {
  const classes = []
  
  // Size classes
  if (props.size === 'small') {
    classes.push('text-sm')
  } else if (props.size === 'large') {
    classes.push('text-lg')
  }
  
  // Layout classes
  if (props.fullHeight) {
    classes.push('min-h-screen flex items-center justify-center')
  }
  
  if (props.overlay) {
    classes.push('fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center')
  }
  
  return classes
})

const getSkeletonWidth = (index) => {
  const widths = ['100%', '85%', '70%', '90%', '60%']
  return widths[(index - 1) % widths.length]
}
</script>

<style scoped>
@keyframes wave {
  0%, 100% { height: 20px; }
  50% { height: 40px; }
}

.animate-wave {
  animation: wave 1s ease-in-out infinite;
}

/* Custom bounce animation for dots */
@keyframes bounce-custom {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce-custom {
  animation: bounce-custom 1.4s infinite ease-in-out both;
}
</style>
