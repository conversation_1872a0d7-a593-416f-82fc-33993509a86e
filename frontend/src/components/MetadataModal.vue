<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" @click="$emit('close')">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay - transparent as per user preference -->
      <div class="fixed inset-0 transition-opacity bg-gray-900 bg-opacity-20 backdrop-blur-sm"></div>

      <!-- Modal panel -->
      <div
        class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl relative z-10"
        @click.stop
      >
        <!-- Debug: Show modal is rendering -->
        <div class="mb-4 p-2 bg-green-100 text-green-800 text-sm rounded">
          ✅ Modal is rendering! Show: {{ show }}, File: {{ file?.name }}
        </div>
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Audio Metadata</h3>
              <p class="text-sm text-gray-500">Detailed file information</p>
            </div>
          </div>
          <button
            @click="$emit('close')"
            class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="flex items-center space-x-3">
            <svg class="w-6 h-6 animate-spin text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span class="text-gray-600">Loading metadata...</span>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="flex items-center justify-center py-12">
          <div class="text-center max-w-md mx-auto">
            <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-red-100 to-pink-100 rounded-full flex items-center justify-center shadow-lg">
              <svg class="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">Unable to load metadata</h3>
            <p class="text-gray-600 mb-2 text-sm">{{ error }}</p>
            <p class="text-gray-500 mb-6 text-xs">The file may not exist in the current subsidiary's storage or there may be a connection issue.</p>
            <button
              @click="loadMetadata(file)"
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Try Again
            </button>
          </div>
        </div>

        <!-- Debug Info (remove in production) -->
        <div v-if="!loading && !error && !metadata" class="p-4 bg-yellow-50 rounded-lg">
          <p class="text-sm text-yellow-800">
            Debug: loading={{ loading }}, error={{ error }}, metadata={{ !!metadata }}, file={{ !!file }}
          </p>
          <p class="text-xs text-yellow-600 mt-1">File key: {{ file?.key }}</p>
          <button
            @click="loadMetadata(file)"
            class="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
          >
            Retry Loading Metadata
          </button>
        </div>

        <!-- Metadata Content -->
        <div v-else-if="metadata" class="space-y-6">
          <!-- File Information -->
          <div class="bg-gray-50 rounded-xl p-4">
            <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
              File Information
            </h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-700">Name:</span>
                <p class="text-gray-900 mt-1 break-all">{{ file?.name }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-700">Size:</span>
                <p class="text-gray-900 mt-1">{{ metadata.formattedSize }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-700">Extension:</span>
                <p class="text-gray-900 mt-1">{{ metadata.extension?.toUpperCase() }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-700">Last Modified:</span>
                <p class="text-gray-900 mt-1">{{ formatDate(file?.lastModified) }}</p>
              </div>
            </div>
          </div>

          <!-- Audio Properties -->
          <div class="bg-blue-50 rounded-xl p-4">
            <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
              Audio Properties
            </h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div v-if="metadata.formattedDuration">
                <span class="font-medium text-gray-700">Duration:</span>
                <p class="text-gray-900 mt-1">{{ metadata.formattedDuration }}</p>
              </div>
              <div v-if="metadata.formattedBitrate">
                <span class="font-medium text-gray-700">Bitrate:</span>
                <p class="text-gray-900 mt-1">{{ metadata.formattedBitrate }}</p>
              </div>
              <div v-if="metadata.format">
                <span class="font-medium text-gray-700">Format:</span>
                <p class="text-gray-900 mt-1">{{ metadata.format.fullName || metadata.format.name }}</p>
              </div>
              <div v-if="metadata.format?.type">
                <span class="font-medium text-gray-700">Compression:</span>
                <p class="text-gray-900 mt-1 capitalize">{{ metadata.format.type }}</p>
              </div>
            </div>
          </div>

          <!-- Quality Information -->
          <div class="bg-green-50 rounded-xl p-4">
            <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Quality Assessment
            </h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="font-medium text-gray-700">Quality Score:</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getQualityBarClass(metadata.qualityScore)"
                      :style="{ width: `${metadata.qualityScore}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-semibold text-gray-900">{{ metadata.qualityScore }}/100</span>
                </div>
              </div>
              
              <div class="flex flex-wrap gap-2">
                <span v-if="metadata.isLossless" class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Lossless
                </span>
                <span v-else-if="metadata.isHighQuality" class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                  High Quality
                </span>
                <span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                  {{ metadata.sizeCategory }} file
                </span>
              </div>
            </div>
          </div>

          <!-- Technical Details -->
          <div v-if="metadata.contentType || metadata.storageClass" class="bg-purple-50 rounded-xl p-4">
            <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Technical Details
            </h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div v-if="metadata.contentType">
                <span class="font-medium text-gray-700">MIME Type:</span>
                <p class="text-gray-900 mt-1 font-mono text-xs">{{ metadata.contentType }}</p>
              </div>
              <div v-if="metadata.storageClass">
                <span class="font-medium text-gray-700">Storage Class:</span>
                <p class="text-gray-900 mt-1">{{ metadata.storageClass }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useFilesStore } from '../stores/files'

const filesStore = useFilesStore()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close'])

const loading = ref(false)
const error = ref(null)
const metadata = ref(null)

// Watch for file changes and load metadata
watch(() => props.file, async (newFile) => {
  if (newFile && props.show) {
    await loadMetadata(newFile)
  }
}, { immediate: true })

watch(() => props.show, async (show) => {
  if (show && props.file) {
    await loadMetadata(props.file)
  }
})

const loadMetadata = async (file) => {
  if (!file) {
    console.log('No file provided to loadMetadata')
    return
  }

  console.log('Loading metadata for file:', file)
  loading.value = true
  error.value = null
  metadata.value = null

  try {
    console.log('Calling getFileMetadata with key:', file.key)
    const result = await filesStore.getFileMetadata(file.key)
    console.log('Metadata result:', result)
    metadata.value = result
  } catch (err) {
    console.error('Error loading metadata:', err)
    error.value = err.response?.data?.message || err.message || 'Failed to load metadata'
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getQualityBarClass = (score) => {
  if (score >= 80) return 'bg-green-500'
  if (score >= 60) return 'bg-yellow-500'
  if (score >= 40) return 'bg-orange-500'
  return 'bg-red-500'
}
</script>
