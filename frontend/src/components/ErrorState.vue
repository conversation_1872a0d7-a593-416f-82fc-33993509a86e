<template>
  <!-- Enhanced Error State Component -->
  <div class="error-state" :class="containerClasses">
    <!-- Error Icon -->
    <div class="text-center">
      <div class="mx-auto mb-4" :class="iconSizeClasses">
        <!-- Network Error -->
        <svg v-if="type === 'network'" class="text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Server Error -->
        <svg v-else-if="type === 'server'" class="text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Auth Error -->
        <svg v-else-if="type === 'auth'" class="text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>

        <!-- Validation Error -->
        <svg v-else-if="type === 'validation'" class="text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>

        <!-- File Error -->
        <svg v-else-if="type === 'file'" class="text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Generic Error -->
        <svg v-else class="text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>

      <!-- Error Title -->
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        {{ title || getDefaultTitle() }}
      </h3>

      <!-- Error Message -->
      <p class="text-gray-600 mb-6 max-w-md mx-auto">
        {{ message || getDefaultMessage() }}
      </p>

      <!-- Error Details (collapsible) -->
      <div v-if="details && showDetails" class="mb-6">
        <button
          @click="detailsExpanded = !detailsExpanded"
          class="text-sm text-gray-500 hover:text-gray-700 transition-colors flex items-center mx-auto"
        >
          <span>{{ detailsExpanded ? 'Hide' : 'Show' }} details</span>
          <svg
            class="w-4 h-4 ml-1 transition-transform"
            :class="{ 'rotate-180': detailsExpanded }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-if="detailsExpanded" class="mt-4 p-4 bg-gray-50 rounded-lg text-left overflow-hidden">
            <pre class="text-xs text-gray-600 whitespace-pre-wrap">{{ details }}</pre>
          </div>
        </Transition>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <!-- Retry Button -->
        <button
          v-if="canRetry"
          @click="handleRetry"
          :disabled="retrying"
          class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="retrying" class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          {{ retrying ? 'Retrying...' : 'Try Again' }}
        </button>

        <!-- Secondary Action -->
        <button
          v-if="secondaryAction"
          @click="handleSecondaryAction"
          class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
        >
          <component v-if="secondaryAction.icon" :is="secondaryAction.icon" class="w-4 h-4 mr-2" />
          {{ secondaryAction.label }}
        </button>

        <!-- Dismiss Button -->
        <button
          v-if="dismissible"
          @click="handleDismiss"
          class="inline-flex items-center px-4 py-2 text-gray-500 font-medium rounded-lg hover:text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          Dismiss
        </button>
      </div>

      <!-- Help Text -->
      <div v-if="helpText" class="mt-6 text-sm text-gray-500">
        <p>{{ helpText }}</p>
      </div>

      <!-- Contact Support -->
      <div v-if="showSupport" class="mt-4 text-xs text-gray-400">
        <p>
          If this problem persists, please 
          <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-700">contact support</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'generic',
    validator: (value) => ['network', 'server', 'auth', 'validation', 'file', 'generic'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  details: {
    type: String,
    default: ''
  },
  canRetry: {
    type: Boolean,
    default: true
  },
  retrying: {
    type: Boolean,
    default: false
  },
  dismissible: {
    type: Boolean,
    default: true
  },
  showDetails: {
    type: Boolean,
    default: true
  },
  showSupport: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  secondaryAction: {
    type: Object,
    default: null
  },
  helpText: {
    type: String,
    default: ''
  },
  fullHeight: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['retry', 'dismiss', 'secondary-action'])

const detailsExpanded = ref(false)

const containerClasses = computed(() => {
  const classes = ['p-8']
  
  if (props.fullHeight) {
    classes.push('min-h-screen flex items-center justify-center')
  }
  
  return classes
})

const iconSizeClasses = computed(() => {
  const sizeMap = {
    small: 'w-12 h-12',
    medium: 'w-16 h-16',
    large: 'w-20 h-20'
  }
  return sizeMap[props.size]
})

const getDefaultTitle = () => {
  const titles = {
    network: 'Connection Problem',
    server: 'Server Error',
    auth: 'Authentication Required',
    validation: 'Invalid Input',
    file: 'File Error',
    generic: 'Something Went Wrong'
  }
  return titles[props.type]
}

const getDefaultMessage = () => {
  const messages = {
    network: 'Unable to connect to the server. Please check your internet connection and try again.',
    server: 'The server encountered an error. Please try again in a moment.',
    auth: 'Your session has expired or you don\'t have permission to access this resource.',
    validation: 'Please check your input and try again.',
    file: 'There was a problem with the file operation.',
    generic: 'An unexpected error occurred. Please try again.'
  }
  return messages[props.type]
}

const handleRetry = () => {
  emit('retry')
}

const handleDismiss = () => {
  emit('dismiss')
}

const handleSecondaryAction = () => {
  emit('secondary-action', props.secondaryAction)
}
</script>

<style scoped>
/* Custom transitions for details expansion */
.max-h-0 {
  max-height: 0;
}

.max-h-96 {
  max-height: 24rem;
}
</style>
