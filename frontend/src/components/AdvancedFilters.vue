<template>
  <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
    <div class="absolute inset-0 bg-gradient-to-r from-gray-50/30 to-slate-50/30"></div>
    
    <div class="relative p-6">
      <!-- Filter Header -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Advanced Filters</h3>
          <span v-if="activeFiltersCount > 0" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
            {{ activeFiltersCount }} active
          </span>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            v-if="activeFiltersCount > 0"
            @click="clearAllFilters"
            class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Clear All
          </button>
          
          <button
            @click="toggleExpanded"
            class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': expanded }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Quick Filter Buttons -->
      <div class="flex flex-wrap gap-2 mb-4">
        <button
          @click="filesStore.setQuickFilter('today')"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg border border-gray-200 transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          Today
        </button>
        <button
          @click="filesStore.setQuickFilter('week')"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg border border-gray-200 transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          This Week
        </button>
        <button
          @click="filesStore.setQuickFilter('month')"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg border border-gray-200 transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          This Month
        </button>
        <button
          @click="filesStore.setQuickFilter('large')"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg border border-gray-200 transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
          </svg>
          Large Files (&gt;50MB)
        </button>
        <button
          @click="filesStore.setQuickFilter('small')"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg border border-gray-200 transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          Small Files (&lt;5MB)
        </button>
      </div>

      <!-- Active Filter Chips -->
      <div v-if="activeFiltersCount > 0" class="flex flex-wrap gap-2 mb-4">
        <!-- Search Filter Chip -->
        <div v-if="filters.search" class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          "{{ filters.search }}"
          <button @click="clearFilter('search')" class="ml-1 hover:text-blue-600">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- File Type Filter Chips -->
        <div v-for="fileType in filters.fileTypes" :key="fileType" class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
          </svg>
          {{ fileType.toUpperCase() }}
          <button @click="removeFileType(fileType)" class="ml-1 hover:text-green-600">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Size Range Filter Chip -->
        <div v-if="filters.sizeRange.min !== null || filters.sizeRange.max !== null" class="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
          </svg>
          Size: {{ formatSizeRange() }}
          <button @click="clearFilter('sizeRange')" class="ml-1 hover:text-purple-600">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Date Range Filter Chip -->
        <div v-if="filters.dateRange.start || filters.dateRange.end" class="inline-flex items-center px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          Date: {{ formatDateRange() }}
          <button @click="clearFilter('dateRange')" class="ml-1 hover:text-orange-600">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Expanded Filter Controls -->
      <div v-show="expanded" class="space-y-6">
        <!-- File Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">File Types</label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="fileType in availableFileTypes"
              :key="fileType.value"
              @click="toggleFileType(fileType.value)"
              :class="[
                'inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                filters.fileTypes.includes(fileType.value)
                  ? 'bg-indigo-100 text-indigo-800 border-2 border-indigo-200'
                  : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
              ]"
            >
              <component :is="fileType.icon" class="w-4 h-4 mr-2" />
              {{ fileType.label }}
              <span class="ml-2 text-xs opacity-75">({{ fileType.count }})</span>
            </button>
          </div>
        </div>

        <!-- Size Range Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">File Size Range</label>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-xs font-medium text-gray-600 mb-1">Minimum Size</label>
              <select
                v-model="localSizeRange.min"
                @change="updateSizeRange"
                class="block w-full px-3 py-2 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option :value="null">No minimum</option>
                <option :value="1024">1 KB</option>
                <option :value="1024 * 1024">1 MB</option>
                <option :value="5 * 1024 * 1024">5 MB</option>
                <option :value="10 * 1024 * 1024">10 MB</option>
                <option :value="50 * 1024 * 1024">50 MB</option>
                <option :value="100 * 1024 * 1024">100 MB</option>
              </select>
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-600 mb-1">Maximum Size</label>
              <select
                v-model="localSizeRange.max"
                @change="updateSizeRange"
                class="block w-full px-3 py-2 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option :value="null">No maximum</option>
                <option :value="1024 * 1024">1 MB</option>
                <option :value="5 * 1024 * 1024">5 MB</option>
                <option :value="10 * 1024 * 1024">10 MB</option>
                <option :value="50 * 1024 * 1024">50 MB</option>
                <option :value="100 * 1024 * 1024">100 MB</option>
                <option :value="500 * 1024 * 1024">500 MB</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Date Range Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">Date Range</label>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-xs font-medium text-gray-600 mb-1">From Date</label>
              <input
                v-model="localDateRange.start"
                @change="updateDateRange"
                type="date"
                class="block w-full px-3 py-2 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-600 mb-1">To Date</label>
              <input
                v-model="localDateRange.end"
                @change="updateDateRange"
                type="date"
                class="block w-full px-3 py-2 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useFilesStore } from '@/stores/files'

const filesStore = useFilesStore()

// Component state
const expanded = ref(false)
const localSizeRange = ref({ min: null, max: null })
const localDateRange = ref({ start: null, end: null })

// Available file types with icons and counts
const availableFileTypes = computed(() => [
  {
    value: 'mp3',
    label: 'MP3',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.mp3')).length
  },
  {
    value: 'wav',
    label: 'WAV',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.wav')).length
  },
  {
    value: 'flac',
    label: 'FLAC',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.flac')).length
  },
  {
    value: 'm4a',
    label: 'M4A',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.m4a')).length
  },
  {
    value: 'aac',
    label: 'AAC',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.aac')).length
  },
  {
    value: 'ogg',
    label: 'OGG',
    icon: 'svg',
    count: filesStore.files.filter(f => f.name?.toLowerCase().endsWith('.ogg')).length
  }
])

// Computed properties
const filters = computed(() => filesStore.filters)

const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.value.search) count++
  if (filters.value.fileTypes.length > 0) count++
  if (filters.value.sizeRange.min !== null || filters.value.sizeRange.max !== null) count++
  if (filters.value.dateRange.start || filters.value.dateRange.end) count++
  return count
})

// Methods
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const toggleFileType = async (fileType) => {
  const currentTypes = [...filters.value.fileTypes]
  const index = currentTypes.indexOf(fileType)
  
  if (index > -1) {
    currentTypes.splice(index, 1)
  } else {
    currentTypes.push(fileType)
  }
  
  await filesStore.setFileTypeFilter(currentTypes)
}

const removeFileType = async (fileType) => {
  const currentTypes = filters.value.fileTypes.filter(type => type !== fileType)
  await filesStore.setFileTypeFilter(currentTypes)
}

const updateSizeRange = async () => {
  await filesStore.setSizeRangeFilter(localSizeRange.value.min, localSizeRange.value.max)
}

const updateDateRange = async () => {
  await filesStore.setDateRangeFilter(localDateRange.value.start, localDateRange.value.end)
}

const clearFilter = async (filterType) => {
  await filesStore.clearSpecificFilter(filterType)
  
  // Reset local state
  if (filterType === 'sizeRange') {
    localSizeRange.value = { min: null, max: null }
  } else if (filterType === 'dateRange') {
    localDateRange.value = { start: null, end: null }
  }
}

const clearAllFilters = async () => {
  await filesStore.clearFilters()
  localSizeRange.value = { min: null, max: null }
  localDateRange.value = { start: null, end: null }
}

// Format helpers
const formatSizeRange = () => {
  const { min, max } = filters.value.sizeRange
  const formatBytes = (bytes) => {
    if (!bytes) return ''
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }
  
  if (min && max) return `${formatBytes(min)} - ${formatBytes(max)}`
  if (min) return `> ${formatBytes(min)}`
  if (max) return `< ${formatBytes(max)}`
  return ''
}

const formatDateRange = () => {
  const { start, end } = filters.value.dateRange
  if (start && end) return `${start} to ${end}`
  if (start) return `from ${start}`
  if (end) return `until ${end}`
  return ''
}

// Watch for external filter changes
watch(() => filters.value.sizeRange, (newRange) => {
  localSizeRange.value = { ...newRange }
}, { deep: true })

watch(() => filters.value.dateRange, (newRange) => {
  localDateRange.value = { ...newRange }
}, { deep: true })
</script>
