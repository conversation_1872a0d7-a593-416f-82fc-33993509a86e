<template>
  <div class="audit-dashboard">
    <!-- Modern Header Section -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-icon">
          <div class="icon-wrapper">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
        </div>
        <div class="header-text">
          <h1 class="header-title">Audit Trail Dashboard</h1>
          <p class="header-subtitle">Monitor system activity and security events in real-time</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          @click="refreshData"
          :disabled="loading"
          class="refresh-btn"
        >
          <svg class="w-4 h-4" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <span>Refresh</span>
        </button>
      </div>
    </div>

    <!-- Modern Summary Cards -->
    <div class="summary-grid">
      <div class="summary-card primary">
        <div class="card-background">
          <div class="card-pattern"></div>
        </div>
        <div class="card-content">
          <div class="card-icon">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div class="card-data">
            <div class="card-number">{{ formatNumber(summary.totalLogs || 0) }}</div>
            <div class="card-label">Total Audit Logs</div>
          </div>
        </div>
      </div>

      <div class="summary-card success">
        <div class="card-background">
          <div class="card-pattern"></div>
        </div>
        <div class="card-content">
          <div class="card-icon">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l7-7 4 4-11 11z"></path>
            </svg>
          </div>
          <div class="card-data">
            <div class="card-number">{{ formatNumber(summary.eventCounts?.authentication || 0) }}</div>
            <div class="card-label">Authentication Events</div>
          </div>
        </div>
      </div>

      <div class="summary-card info">
        <div class="card-background">
          <div class="card-pattern"></div>
        </div>
        <div class="card-content">
          <div class="card-icon">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div class="card-data">
            <div class="card-number">{{ formatNumber(summary.eventCounts?.downloads || 0) }}</div>
            <div class="card-label">Download Events</div>
          </div>
        </div>
      </div>

      <div class="summary-card warning">
        <div class="card-background">
          <div class="card-pattern"></div>
        </div>
        <div class="card-content">
          <div class="card-icon">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div class="card-data">
            <div class="card-number">{{ formatNumber(summary.eventCounts?.failures || 0) }}</div>
            <div class="card-label">Failed Events</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Filters Section -->
    <div class="filters-container">
      <div class="filters-header">
        <h3 class="filters-title">Filter & Search</h3>
        <p class="filters-subtitle">Refine your audit trail view</p>
      </div>

      <div class="filters-grid">
        <div class="filter-group">
          <label class="filter-label">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Date Range
          </label>
          <div class="date-range-inputs">
            <input
              type="date"
              v-model="filters.startDate"
              @change="loadAuditLogs"
              class="modern-input"
              placeholder="Start date"
            >
            <span class="date-separator">to</span>
            <input
              type="date"
              v-model="filters.endDate"
              @change="loadAuditLogs"
              class="modern-input"
              placeholder="End date"
            >
          </div>
        </div>

        <div class="filter-group">
          <label class="filter-label">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Action Type
          </label>
          <select v-model="filters.action" @change="loadAuditLogs" class="modern-select">
            <option value="">All Actions</option>
            <option value="LOGIN_SUCCESS">Login Success</option>
            <option value="LOGIN_FAILURE">Login Failure</option>
            <option value="SINGLE_DOWNLOAD">Single Download</option>
            <option value="BULK_DOWNLOAD">Bulk Download</option>
            <option value="SEARCH_QUERY">Search Query</option>
            <option value="AUDIO_PLAY">Audio Play</option>
          </select>
        </div>

        <div class="filter-group">
          <label class="filter-label">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Status
          </label>
          <select v-model="filters.status" @change="loadAuditLogs" class="modern-select">
            <option value="">All Status</option>
            <option value="success">Success</option>
            <option value="failure">Failure</option>
            <option value="warning">Warning</option>
          </select>
        </div>

        <div class="filter-actions">
          <button @click="exportLogs" class="export-button">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span>Export CSV</span>
          </button>
          <button @click="clearFilters" class="clear-button">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span>Clear</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Modern Audit Logs Table -->
    <div class="logs-container">
      <div class="logs-header">
        <div class="logs-title-section">
          <h3 class="logs-title">Recent Audit Logs</h3>
          <div class="logs-count" v-if="auditLogs.totalLogs">
            <span class="count-badge">{{ formatNumber(auditLogs.totalLogs) }}</span>
            <span class="count-label">total logs</span>
          </div>
        </div>
        <div class="pagination-info" v-if="auditLogs.totalLogs">
          Showing {{ ((auditLogs.currentPage - 1) * 50) + 1 }} -
          {{ Math.min(auditLogs.currentPage * 50, auditLogs.totalLogs) }}
          of {{ formatNumber(auditLogs.totalLogs) }} logs
        </div>
      </div>

      <div class="table-wrapper">
        <div class="table-container">
          <table class="modern-table">
            <thead>
              <tr>
                <th class="timestamp-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Timestamp
                  </div>
                </th>
                <th class="user-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    User
                  </div>
                </th>
                <th class="action-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Action
                  </div>
                </th>
                <th class="resource-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Resource
                  </div>
                </th>
                <th class="status-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Status
                  </div>
                </th>
                <th class="ip-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                    </svg>
                    IP Address
                  </div>
                </th>
                <th class="details-col">
                  <div class="th-content">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Details
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="log in auditLogs.logs" :key="log.id" class="table-row" :class="getRowClass(log.status)">
                <td class="timestamp-cell">
                  <div class="timestamp-content">
                    <div class="timestamp-date">{{ formatDate(log.created_at) }}</div>
                    <div class="timestamp-time">{{ formatTime(log.created_at) }}</div>
                  </div>
                </td>
                <td class="user-cell">
                  <div class="user-content" v-if="log.user">
                    <div class="user-avatar">
                      <span>{{ getUserInitials(log.user) }}</span>
                    </div>
                    <div class="user-details">
                      <div class="user-name">{{ log.user.first_name }} {{ log.user.last_name }}</div>
                      <div class="user-email">{{ log.user.email }}</div>
                    </div>
                  </div>
                  <div class="system-user" v-else>
                    <div class="system-avatar">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <span>System</span>
                  </div>
                </td>
                <td class="action-cell">
                  <span class="modern-action-badge" :class="getActionClass(log.action)">
                    <span class="action-icon" v-html="getActionIcon(log.action)"></span>
                    <span class="action-text">{{ formatAction(log.action) }}</span>
                  </span>
                </td>
                <td class="resource-cell">
                  <div class="resource-content">
                    <div class="resource-type">{{ log.resource_type }}</div>
                    <div class="resource-id" v-if="log.resource_id">{{ truncateText(log.resource_id, 20) }}</div>
                  </div>
                </td>
                <td class="status-cell">
                  <span class="modern-status-badge" :class="log.status">
                    <span class="status-dot"></span>
                    <span class="status-text">{{ capitalizeFirst(log.status) }}</span>
                  </span>
                </td>
                <td class="ip-cell">
                  <div class="ip-content">
                    <span class="ip-address">{{ log.ip_address || 'N/A' }}</span>
                  </div>
                </td>
                <td class="details-cell">
                  <button
                    @click="showDetails(log)"
                    class="modern-details-btn"
                    v-if="log.details && Object.keys(log.details).length > 0"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Pagination -->
      <div class="pagination" v-if="auditLogs.totalPages > 1">
        <button 
          @click="changePage(auditLogs.currentPage - 1)"
          :disabled="!auditLogs.hasPrevPage"
          class="page-btn"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <span class="page-info">
          Page {{ auditLogs.currentPage }} of {{ auditLogs.totalPages }}
        </span>
        
        <button 
          @click="changePage(auditLogs.currentPage + 1)"
          :disabled="!auditLogs.hasNextPage"
          class="page-btn"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading audit data...</p>
      </div>
    </div>

    <!-- Details Modal -->
    <div v-if="selectedLog" class="modal-overlay" @click="closeDetails">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Audit Log Details</h3>
          <button @click="closeDetails" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="detail-section">
            <h4>Basic Information</h4>
            <div class="detail-grid">
              <div><strong>ID:</strong> {{ selectedLog.id }}</div>
              <div><strong>Timestamp:</strong> {{ formatDate(selectedLog.created_at) }}</div>
              <div><strong>Action:</strong> {{ formatAction(selectedLog.action) }}</div>
              <div><strong>Status:</strong> {{ selectedLog.status }}</div>
              <div><strong>Resource Type:</strong> {{ selectedLog.resource_type }}</div>
              <div><strong>Resource ID:</strong> {{ selectedLog.resource_id || 'N/A' }}</div>
            </div>
          </div>
          
          <div class="detail-section" v-if="selectedLog.details">
            <h4>Additional Details</h4>
            <pre class="json-details">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
          </div>
          
          <div class="detail-section" v-if="selectedLog.error_message">
            <h4>Error Message</h4>
            <div class="error-message">{{ selectedLog.error_message }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'

export default {
  name: 'AuditDashboard',
  setup() {
    const authStore = useAuthStore()
    const loading = ref(false)
    const auditLogs = ref({ logs: [], totalLogs: 0, currentPage: 1, totalPages: 1 })
    const summary = ref({})
    const selectedLog = ref(null)
    
    const filters = ref({
      startDate: '',
      endDate: '',
      action: '',
      status: '',
      page: 1
    })

    // Check if user has audit access
    const hasAuditAccess = computed(() => {
      return authStore.user?.roles?.some(role => 
        ['ADMIN', 'COMPLIANCE'].includes(role.code)
      )
    })

    const loadSummary = async () => {
      if (!hasAuditAccess.value) {
        console.log('🔍 No audit access, skipping summary load')
        return
      }

      if (!authStore.isAuthenticated) {
        console.log('🔍 Not authenticated, skipping summary load')
        return
      }

      try {
        console.log('🔍 Loading audit summary...')
        console.log('🔍 User roles:', authStore.user?.roles?.map(r => r.code))
        console.log('🔍 Has audit access:', hasAuditAccess.value)
        console.log('🔍 Auth token exists:', !!localStorage.getItem('token'))

        // Only include date params if they have values
        const params = {}
        if (filters.value.startDate) {
          params.startDate = filters.value.startDate
        }
        if (filters.value.endDate) {
          params.endDate = filters.value.endDate
        }

        const response = await api.get('/audit/summary', { params })
        console.log('🔍 Audit summary response:', response.data)
        console.log('🔍 Summary data:', response.data.data)

        if (response.data.success && response.data.data) {
          summary.value = response.data.data
          console.log('🔍 Summary value after assignment:', summary.value)
        } else {
          console.error('🔍 Invalid response format:', response.data)
        }
      } catch (error) {
        console.error('Failed to load audit summary:', error)
        console.error('🔍 Error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        })

        if (error.response?.status === 403) {
          console.error('🔍 Access denied - user may not have proper roles')
          // Try again after a short delay in case it's a timing issue
          setTimeout(() => {
            console.log('🔍 Retrying audit summary load after 403...')
            loadSummary()
          }, 2000)
        }
      }
    }

    const loadAuditLogs = async (page = 1) => {
      if (!hasAuditAccess.value) return
      
      loading.value = true
      try {
        const params = {
          page,
          limit: 50,
          ...filters.value
        }
        
        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null) {
            delete params[key]
          }
        })

        const response = await api.get('/audit/logs', { params })
        auditLogs.value = response.data.data
      } catch (error) {
        console.error('Failed to load audit logs:', error)
      } finally {
        loading.value = false
      }
    }

    const changePage = (page) => {
      filters.value.page = page
      loadAuditLogs(page)
    }

    const exportLogs = async () => {
      try {
        const params = {
          format: 'csv',
          startDate: filters.value.startDate,
          endDate: filters.value.endDate
        }

        const response = await api.get('/audit/export', {
          params,
          responseType: 'blob'
        })

        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `audit-logs-${new Date().toISOString().split('T')[0]}.csv`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Failed to export logs:', error)
      }
    }

    const showDetails = (log) => {
      selectedLog.value = log
    }

    const closeDetails = () => {
      selectedLog.value = null
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString()
    }

    const formatAction = (action) => {
      return action.replace(/_/g, ' ').toLowerCase()
        .replace(/\b\w/g, l => l.toUpperCase())
    }

    const getRowClass = (status) => {
      return {
        'row-success': status === 'success',
        'row-warning': status === 'warning',
        'row-error': status === 'failure'
      }
    }

    const getActionClass = (action) => {
      if (action.includes('LOGIN')) return 'auth'
      if (action.includes('DOWNLOAD')) return 'download'
      if (action.includes('SEARCH')) return 'search'
      if (action.includes('PLAY')) return 'playback'
      return 'default'
    }

    // New helper methods for modern UI
    const formatNumber = (num) => {
      return new Intl.NumberFormat().format(num)
    }

    const formatTime = (dateString) => {
      return new Date(dateString).toLocaleTimeString()
    }

    const getUserInitials = (user) => {
      return `${user.first_name?.charAt(0) || ''}${user.last_name?.charAt(0) || ''}`.toUpperCase()
    }

    const truncateText = (text, maxLength) => {
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    }

    const capitalizeFirst = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1)
    }

    const getActionIcon = (action) => {
      const icons = {
        'LOGIN_SUCCESS': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
        'LOGIN_FAILURE': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path></svg>',
        'SINGLE_DOWNLOAD': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>',
        'BULK_DOWNLOAD': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>',
        'SEARCH_QUERY': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>',
        'AUDIO_PLAY': '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>'
      }
      return icons[action] || '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    }

    const clearFilters = () => {
      filters.value = {
        startDate: '',
        endDate: '',
        action: '',
        status: '',
        page: 1
      }
      loadAuditLogs()
    }

    const refreshData = async () => {
      console.log('🔍 Refreshing audit data...')
      await Promise.all([loadSummary(), loadAuditLogs()])
    }

    // Watch for authentication changes
    watch(() => authStore.isAuthenticated, (isAuthenticated) => {
      if (isAuthenticated && hasAuditAccess.value) {
        console.log('🔍 Authentication state changed, reloading audit data')
        loadSummary()
        loadAuditLogs()
      }
    })

    // Watch for user changes (role updates)
    watch(() => authStore.user, (user) => {
      if (user && hasAuditAccess.value) {
        console.log('🔍 User data changed, reloading audit data')
        loadSummary()
        loadAuditLogs()
      }
    })

    onMounted(() => {
      console.log('🔍 AuditDashboard mounted')
      console.log('🔍 Is authenticated:', authStore.isAuthenticated)
      console.log('🔍 Has audit access:', hasAuditAccess.value)
      console.log('🔍 User:', authStore.user)

      if (hasAuditAccess.value && authStore.isAuthenticated) {
        loadSummary()
        loadAuditLogs()
      } else {
        console.log('🔍 Waiting for authentication or proper roles...')
      }
    })

    return {
      loading,
      auditLogs,
      summary,
      selectedLog,
      filters,
      hasAuditAccess,
      loadAuditLogs,
      changePage,
      exportLogs,
      showDetails,
      closeDetails,
      formatDate,
      formatAction,
      getRowClass,
      getActionClass,
      formatNumber,
      formatTime,
      getUserInitials,
      truncateText,
      capitalizeFirst,
      getActionIcon,
      clearFilters,
      refreshData
    }
  }
}
</script>

<style scoped>
/* Modern Audit Dashboard Styles */
.audit-dashboard {
  padding: 1.5rem;
  max-width: 1600px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Modern Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon .icon-wrapper {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.header-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  line-height: 1.2;
}

.header-subtitle {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Modern Summary Cards */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  opacity: 0.1;
  overflow: hidden;
}

.card-pattern {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, currentColor 25%, transparent 25%),
              linear-gradient(-45deg, currentColor 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, currentColor 75%),
              linear-gradient(-45deg, transparent 75%, currentColor 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.summary-card.primary .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.summary-card.success .card-icon {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.3);
}

.summary-card.info .card-icon {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.3);
}

.summary-card.warning .card-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
}

.card-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.card-label {
  color: #64748b;
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modern Filters Section */
.filters-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filters-header {
  margin-bottom: 1.5rem;
}

.filters-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.filters-subtitle {
  color: #64748b;
  margin: 0;
  font-size: 0.95rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.date-range-inputs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-separator {
  color: #64748b;
  font-weight: 500;
  font-size: 0.9rem;
}

.modern-input,
.modern-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
  background: white;
  transition: all 0.3s ease;
  color: #374151;
}

.modern-input:focus,
.modern-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.modern-input::placeholder {
  color: #9ca3af;
}

.filter-actions {
  display: flex;
  gap: 0.75rem;
  align-items: end;
}

.export-button,
.clear-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.export-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.export-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.clear-button {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.clear-button:hover {
  background: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
}

/* Modern Logs Section */
.logs-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.logs-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logs-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.logs-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.count-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
}

.count-label {
  color: #64748b;
  font-size: 0.85rem;
  font-weight: 500;
}

.pagination-info {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
}

.table-wrapper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.table-container {
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.modern-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 700;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.th-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-table td {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: top;
}

.table-row {
  transition: all 0.2s ease;
  position: relative;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: scale(1.001);
}

.table-row.row-success {
  border-left: 4px solid #22c55e;
}

.table-row.row-warning {
  border-left: 4px solid #f59e0b;
}

.table-row.row-error {
  border-left: 4px solid #ef4444;
}

/* Modern Table Cell Styles */
.timestamp-cell {
  min-width: 160px;
}

.timestamp-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timestamp-date {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.timestamp-time {
  color: #64748b;
  font-size: 0.8rem;
  font-weight: 500;
}

.user-cell {
  min-width: 200px;
}

.user-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 0.85rem;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.user-name {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.user-email {
  color: #64748b;
  font-size: 0.8rem;
}

.system-user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-style: italic;
  font-weight: 500;
}

.system-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
}

.action-cell {
  min-width: 140px;
}

.modern-action-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-action-badge.auth {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
}

.modern-action-badge.download {
  background: linear-gradient(135deg, #fce7f3 0%, #f9a8d4 100%);
  color: #be185d;
}

.modern-action-badge.search {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.modern-action-badge.playback {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  color: #c2410c;
}

.modern-action-badge.default {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
}

.action-icon {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 0.75rem;
}

.resource-cell {
  min-width: 120px;
}

.resource-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.resource-type {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.resource-id {
  color: #64748b;
  font-size: 0.8rem;
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f8fafc;
  padding: 0.125rem 0.375rem;
  border-radius: 6px;
}

.status-cell {
  min-width: 100px;
}

.modern-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.modern-status-badge.success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.modern-status-badge.success .status-dot {
  background: #22c55e;
}

.modern-status-badge.warning {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
}

.modern-status-badge.warning .status-dot {
  background: #f59e0b;
}

.modern-status-badge.failure {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
}

.modern-status-badge.failure .status-dot {
  background: #ef4444;
}

.ip-cell {
  min-width: 120px;
}

.ip-content {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  color: #374151;
  background: #f8fafc;
  padding: 0.375rem 0.5rem;
  border-radius: 8px;
  display: inline-block;
}

.details-cell {
  width: 60px;
  text-align: center;
}

.modern-details-btn {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  color: #667eea;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modern-details-btn:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Modern Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #f1f5f9;
}

.page-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: white;
  border: 2px solid #e2e8f0;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
}

.page-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  background: #f8fafc;
  transform: translateY(-1px);
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.page-info {
  color: #64748b;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0 1rem;
}

/* Modern Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  text-align: center;
  color: #667eea;
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-spinner i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

/* Modern Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8fafc;
  color: #374151;
  transform: translateY(-1px);
}

.modal-body {
  padding: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  color: #1e293b;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 0.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.detail-grid div {
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.detail-grid div strong {
  color: #374151;
  font-weight: 700;
}

.json-details {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 12px;
  overflow-x: auto;
  font-size: 0.9rem;
  line-height: 1.6;
  font-family: 'Monaco', 'Menlo', monospace;
  border: 1px solid #334155;
}

.error-message {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #ef4444;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .audit-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .audit-dashboard {
    padding: 0.75rem;
  }

  .dashboard-header,
  .filters-container,
  .logs-container {
    padding: 1.5rem;
  }

  .header-title {
    font-size: 2rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .date-range-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-actions {
    flex-direction: column;
  }

  .logs-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .modern-table {
    font-size: 0.85rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.75rem 0.5rem;
  }

  .user-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 90vh;
  }

  .modal-header,
  .modal-body {
    padding: 1.5rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .card-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .table-container {
    font-size: 0.8rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.5rem 0.25rem;
  }
}
</style>
