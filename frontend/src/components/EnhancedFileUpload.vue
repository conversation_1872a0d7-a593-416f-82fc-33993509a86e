<template>
  <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
    <!-- Header -->
    <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Upload Audio Files
          </h3>
          <p class="text-sm text-gray-600 mt-1">Drag and drop your audio files or click to browse</p>
        </div>
        
        <!-- Upload Stats -->
        <div v-if="files.length > 0" class="text-right">
          <p class="text-sm font-medium text-gray-900">{{ files.length }} file{{ files.length > 1 ? 's' : '' }}</p>
          <p class="text-xs text-gray-500">{{ formatFileSize(totalSize) }} total</p>
        </div>
      </div>
    </div>

    <!-- Upload Area -->
    <div class="p-6">
      <div
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
        :class="[
          'relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300',
          isDragging 
            ? 'border-blue-400 bg-blue-50 scale-105' 
            : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50/50'
        ]"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac,.wma"
          @change="handleFileSelect"
          class="hidden"
        />

        <div class="space-y-4">
          <div class="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          
          <div>
            <p class="text-lg font-medium text-gray-900">
              {{ isDragging ? 'Drop files here' : 'Choose audio files or drag them here' }}
            </p>
            <p class="text-sm text-gray-500 mt-1">
              Supports MP3, WAV, OGG, M4A, AAC, FLAC, WMA (max 100MB each)
            </p>
          </div>

          <button
            type="button"
            class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Browse Files
          </button>
        </div>
      </div>

      <!-- File List -->
      <div v-if="files.length > 0" class="mt-8 space-y-4">
        <div class="flex items-center justify-between">
          <h4 class="text-lg font-semibold text-gray-900">Selected Files ({{ files.length }})</h4>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">{{ uploadedCount }}/{{ files.length }} uploaded</span>
            <div class="w-20 bg-gray-200 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: overallProgress + '%' }"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="space-y-3 max-h-80 overflow-y-auto">
          <div
            v-for="(file, index) in files"
            :key="index"
            class="relative overflow-hidden bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
          >
            <!-- File Header -->
            <div class="flex items-center p-4">
              <div class="flex items-center flex-1 min-w-0">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                
                <div class="ml-4 flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-semibold text-gray-900 truncate">{{ file.name }}</p>
                      <div class="flex items-center space-x-4 mt-1">
                        <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                        <p v-if="file.metadata?.duration" class="text-xs text-gray-500">{{ file.metadata.duration }}</p>
                        <p v-if="file.metadata?.format" class="text-xs text-gray-500 uppercase">{{ file.metadata.format }}</p>
                      </div>
                    </div>
                    
                    <!-- Status Badge -->
                    <div class="flex items-center ml-4">
                      <span v-if="file.uploaded" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Uploaded
                      </span>
                      
                      <span v-else-if="file.error" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Failed
                      </span>
                      
                      <span v-else-if="file.uploading" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Uploading
                      </span>
                      
                      <span v-else class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Pending
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Remove Button -->
              <button
                @click="removeFile(index)"
                :disabled="file.uploading"
                class="ml-4 p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                :title="file.uploading ? 'Cannot remove while uploading' : 'Remove file'"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>

            <!-- Progress Bar -->
            <div v-if="file.uploading" class="px-4 pb-4">
              <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
                <span>Uploading...</span>
                <span>{{ file.progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300 ease-out"
                  :style="{ width: file.progress + '%' }"
                ></div>
              </div>
              <div v-if="file.uploadSpeed" class="flex items-center justify-between text-xs text-gray-500 mt-1">
                <span>{{ file.uploadSpeed }}</span>
                <span v-if="file.timeRemaining">{{ file.timeRemaining }} remaining</span>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="file.error" class="px-4 pb-4">
              <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                <div class="flex items-start">
                  <svg class="w-4 h-4 text-red-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-red-800">Upload Failed</p>
                    <p class="text-xs text-red-600 mt-1">{{ file.error }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div v-if="files.length > 0" class="mt-6 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="clearFiles"
            :disabled="isUploading"
            class="text-sm text-gray-500 hover:text-gray-700 transition-colors disabled:opacity-50"
          >
            Clear All
          </button>
          
          <div v-if="isUploading" class="flex items-center text-sm text-gray-600">
            <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Uploading {{ uploadingCount }} of {{ files.length }} files...
          </div>
        </div>

        <div class="flex space-x-3">
          <button
            @click="$emit('cancel')"
            :disabled="isUploading"
            class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          
          <button
            @click="startUpload"
            :disabled="!canUpload"
            class="px-6 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:bg-gray-400 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
          >
            <svg v-if="isUploading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isUploading ? 'Uploading...' : `Upload ${files.length} File${files.length > 1 ? 's' : ''}` }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

const toast = useToast()

// Props & Emits
const emit = defineEmits(['upload-complete', 'cancel'])

// State
const files = ref([])
const isDragging = ref(false)
const fileInput = ref(null)

// Computed
const isUploading = computed(() => files.value.some(f => f.uploading))
const canUpload = computed(() => files.value.length > 0 && !isUploading.value)
const uploadedCount = computed(() => files.value.filter(f => f.uploaded).length)
const uploadingCount = computed(() => files.value.filter(f => f.uploading).length)
const totalSize = computed(() => files.value.reduce((sum, f) => sum + f.size, 0))
const overallProgress = computed(() => {
  if (files.value.length === 0) return 0
  const totalProgress = files.value.reduce((sum, f) => sum + (f.uploaded ? 100 : f.progress || 0), 0)
  return Math.round(totalProgress / files.value.length)
})

// File validation
const ALLOWED_TYPES = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/aac', 'audio/flac', 'audio/x-ms-wma']
const ALLOWED_EXTENSIONS = ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac', 'wma']
const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

// Drag and drop handlers
const handleDragEnter = (e) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e) => {
  e.preventDefault()
}

const handleDragLeave = (e) => {
  e.preventDefault()
  if (!e.currentTarget.contains(e.relatedTarget)) {
    isDragging.value = false
  }
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragging.value = false
  
  const droppedFiles = Array.from(e.dataTransfer.files)
  addFiles(droppedFiles)
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (e) => {
  const selectedFiles = Array.from(e.target.files)
  addFiles(selectedFiles)
  e.target.value = '' // Reset input
}

// File management
const validateFile = (file) => {
  const errors = []
  
  // Check file type
  const extension = file.name.split('.').pop()?.toLowerCase()
  if (!ALLOWED_TYPES.includes(file.type) && !ALLOWED_EXTENSIONS.includes(extension)) {
    errors.push('Invalid file type. Only audio files are allowed.')
  }
  
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File size exceeds ${formatFileSize(MAX_FILE_SIZE)} limit.`)
  }
  
  return errors
}

const addFiles = (newFiles) => {
  newFiles.forEach(file => {
    const errors = validateFile(file)
    
    if (errors.length > 0) {
      toast.error(`${file.name}: ${errors.join(' ')}`)
      return
    }
    
    // Check for duplicates
    const exists = files.value.some(f => f.name === file.name && f.size === file.size)
    if (!exists) {
      files.value.push({
        file,
        name: file.name,
        size: file.size,
        progress: 0,
        uploading: false,
        uploaded: false,
        error: null,
        metadata: extractBasicMetadata(file),
        uploadSpeed: null,
        timeRemaining: null,
        startTime: null
      })
    }
  })
}

const extractBasicMetadata = (file) => {
  const extension = file.name.split('.').pop()?.toLowerCase()
  return {
    format: extension,
    // Additional metadata would be extracted here in a real implementation
    // This would require a library like music-metadata-browser
  }
}

const removeFile = (index) => {
  files.value.splice(index, 1)
}

const clearFiles = () => {
  files.value = []
}

// Upload functionality
const startUpload = async () => {
  const filesToUpload = files.value.filter(f => !f.uploaded && !f.error)
  
  for (const fileItem of filesToUpload) {
    await uploadFile(fileItem)
  }

  // Check if all uploads completed successfully
  const successCount = files.value.filter(f => f.uploaded).length
  const errorCount = files.value.filter(f => f.error).length

  if (successCount > 0) {
    toast.success(`Successfully uploaded ${successCount} file${successCount > 1 ? 's' : ''}`)
    emit('upload-complete', { success: successCount, errors: errorCount })
  }

  if (errorCount > 0) {
    toast.error(`Failed to upload ${errorCount} file${errorCount > 1 ? 's' : ''}`)
  }
}

const uploadFile = async (fileItem) => {
  try {
    fileItem.uploading = true
    fileItem.error = null
    fileItem.startTime = Date.now()

    const formData = new FormData()
    formData.append('audioFile', fileItem.file)
    formData.append('title', fileItem.name)

    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        fileItem.progress = progress
        
        // Calculate upload speed and time remaining
        const elapsed = (Date.now() - fileItem.startTime) / 1000
        const speed = progressEvent.loaded / elapsed
        const remaining = (progressEvent.total - progressEvent.loaded) / speed
        
        fileItem.uploadSpeed = formatSpeed(speed)
        fileItem.timeRemaining = formatTime(remaining)
      }
    })

    if (response.data.success) {
      fileItem.uploaded = true
      fileItem.progress = 100
      fileItem.uploadSpeed = null
      fileItem.timeRemaining = null
    }
  } catch (error) {
    console.error('Upload error:', error)
    fileItem.error = error.response?.data?.message || 'Upload failed'
    toast.error(`Failed to upload ${fileItem.name}: ${fileItem.error}`)
  } finally {
    fileItem.uploading = false
  }
}

// Utility functions
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond) => {
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatTime = (seconds) => {
  if (seconds < 60) return `${Math.round(seconds)}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.round(seconds % 60)
  return `${minutes}m ${remainingSeconds}s`
}
</script>
