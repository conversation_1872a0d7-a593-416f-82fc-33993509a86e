<template>
  <!-- Mobile-Optimized File List -->
  <div class="lg:hidden space-y-3">
    <!-- Mobile File Card -->
    <div
      v-for="file in files"
      :key="file.key"
      class="bg-white rounded-2xl shadow-sm border border-gray-200/50 overflow-hidden transition-all duration-300"
      :class="[
        selectedFiles.includes(file.key)
          ? 'ring-2 ring-indigo-400 shadow-lg bg-indigo-50/30'
          : 'hover:shadow-md'
      ]"
    >
      <!-- Card Header -->
      <div class="p-4 pb-3">
        <div class="flex items-start justify-between">
          <!-- File Info -->
          <div class="flex items-start space-x-3 flex-1 min-w-0">
            <!-- Selection Checkbox -->
            <label class="inline-flex items-center cursor-pointer mt-1">
              <input
                type="checkbox"
                :checked="selectedFiles.includes(file.key)"
                @change="toggleSelection(file.key)"
                class="h-5 w-5 text-indigo-600 focus:ring-2 focus:ring-indigo-500 border-2 border-gray-300 rounded transition-all"
              />
            </label>

            <!-- File Icon & Details -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-3 mb-2">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 truncate text-sm">{{ file.name }}</h3>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</span>
                    <span class="text-xs text-gray-400">•</span>
                    <span class="text-xs text-gray-500">{{ formatDate(file.lastModified) }}</span>
                  </div>
                </div>
              </div>

              <!-- Metadata Tags -->
              <div class="flex flex-wrap gap-1 mb-3">
                <span v-if="file.metadata?.format" class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                  {{ typeof file.metadata.format === 'string' ? file.metadata.format.toUpperCase() : file.metadata.format.name?.toUpperCase() || 'AUDIO' }}
                </span>
                <span v-if="file.metadata?.duration" class="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                  {{ formatDuration(file.metadata.duration) }}
                </span>
                <span v-if="file.metadata?.bitrate" class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">
                  {{ file.metadata.bitrate }} kbps
                </span>
              </div>
            </div>
          </div>

          <!-- Action Menu -->
          <div class="relative ml-2">
            <button
              @click="toggleActionMenu(file.key)"
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              :class="{ 'bg-gray-100': activeActionMenu === file.key }"
            >
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>

            <!-- Action Menu Dropdown -->
            <Transition
              enter-active-class="transition-all duration-200 ease-out"
              enter-from-class="opacity-0 scale-95 translate-y-1"
              enter-to-class="opacity-100 scale-100 translate-y-0"
              leave-active-class="transition-all duration-150 ease-in"
              leave-from-class="opacity-100 scale-100 translate-y-0"
              leave-to-class="opacity-0 scale-95 translate-y-1"
            >
              <div
                v-if="activeActionMenu === file.key"
                class="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200/50 py-2 z-10"
              >
                <button
                  @click="playFile(file)"
                  class="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                >
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900">Play</span>
                </button>
                
                <button
                  @click="downloadFile(file)"
                  class="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                >
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900">Download</span>
                </button>
                
                <button
                  @click="showMetadata(file)"
                  class="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                >
                  <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900">Info</span>
                </button>
                
                <div class="border-t border-gray-100 my-1"></div>
                
                <button
                  @click="deleteFile(file)"
                  class="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors"
                >
                  <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  <span class="text-sm font-medium text-red-600">Delete</span>
                </button>
              </div>
            </Transition>
          </div>
        </div>
      </div>

      <!-- Audio Player (when playing) -->
      <div v-if="currentlyPlaying === file.key" class="px-4 pb-4">
        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 border border-indigo-200/50">
          <audio
            :ref="el => audioRefs[file.key] = el"
            :src="file.downloadUrl"
            controls
            class="w-full h-10"
            @ended="stopPlaying"
            @error="handleAudioError"
          ></audio>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="files.length === 0" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No audio files found</h3>
      <p class="text-gray-500 mb-4">Upload some audio files to get started</p>
      <router-link
        to="/upload"
        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white font-medium rounded-xl hover:bg-indigo-700 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Upload Files
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useToast } from 'vue-toastification'

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  selectedFiles: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['select', 'play', 'download', 'delete', 'show-metadata'])

// Composables
const toast = useToast()

// State
const activeActionMenu = ref(null)
const currentlyPlaying = ref(null)
const audioRefs = reactive({})

// Methods
const toggleSelection = (fileKey) => {
  emit('select', fileKey)
}

const toggleActionMenu = (fileKey) => {
  activeActionMenu.value = activeActionMenu.value === fileKey ? null : fileKey
}

const playFile = (file) => {
  if (currentlyPlaying.value === file.key) {
    stopPlaying()
  } else {
    currentlyPlaying.value = file.key
    emit('play', file)
  }
  activeActionMenu.value = null
}

const stopPlaying = () => {
  currentlyPlaying.value = null
}

const downloadFile = (file) => {
  emit('download', file)
  activeActionMenu.value = null
}

const showMetadata = (file) => {
  emit('show-metadata', file)
  activeActionMenu.value = null
}

const deleteFile = (file) => {
  emit('delete', file)
  activeActionMenu.value = null
}

const handleAudioError = (event) => {
  console.error('Audio playback error:', event)
  toast.error('Failed to play audio file')
  stopPlaying()
}

// Utility functions
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
  })
}

const formatDuration = (seconds) => {
  if (!seconds) return ''
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Close action menu when clicking outside
document.addEventListener('click', (event) => {
  if (!event.target.closest('.relative')) {
    activeActionMenu.value = null
  }
})
</script>

<style scoped>
/* Mobile-optimized styles */
audio {
  border-radius: 8px;
}

/* Touch-friendly interactions */
button {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling */
.space-y-3 {
  -webkit-overflow-scrolling: touch;
}
</style>
