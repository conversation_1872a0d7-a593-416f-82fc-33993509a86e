<template>
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-t-3xl p-6 text-white">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
            </svg>
          </div>
          <div>
            <h2 class="text-2xl font-bold">Audio File Upload</h2>
            <p class="text-white/80">Upload your audio files with real-time progress tracking</p>
          </div>
        </div>
        
        <!-- Overall Progress -->
        <div v-if="files.length > 0" class="text-right">
          <div class="text-sm text-white/80">Overall Progress</div>
          <div class="text-2xl font-bold">{{ Math.round(overallProgress) }}%</div>
          <div class="text-sm text-white/80">{{ completedFiles }}/{{ files.length }} files</div>
        </div>
      </div>
    </div>

    <!-- Upload Area -->
    <div class="bg-white rounded-b-3xl shadow-xl border border-gray-200/50">
      <!-- Drop Zone -->
      <div v-if="files.length === 0" class="p-12">
        <div
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter"
          @dragleave="handleDragLeave"
          @click="triggerFileInput"
          :class="[
            'relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-300',
            isDragging 
              ? 'border-indigo-400 bg-indigo-50 scale-105 shadow-lg' 
              : 'border-gray-300 hover:border-indigo-400 hover:bg-indigo-50/50'
          ]"
        >
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac,.wma"
            @change="handleFileSelect"
            class="hidden"
          />

          <div class="space-y-6">
            <!-- Upload Icon -->
            <div class="mx-auto w-20 h-20 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            
            <!-- Upload Text -->
            <div>
              <p class="text-xl font-semibold text-gray-900 mb-2">
                {{ isDragging ? 'Drop your audio files here' : 'Choose audio files or drag them here' }}
              </p>
              <p class="text-gray-500">
                Supports MP3, WAV, OGG, M4A, AAC, FLAC, WMA (max 100MB each)
              </p>
            </div>

            <!-- Features -->
            <div class="grid grid-cols-3 gap-4 max-w-md mx-auto text-sm text-gray-600">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Real-time progress</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Pause & Resume</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span>Auto retry</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- File List -->
      <div v-else class="p-6">
        <!-- Controls -->
        <div class="flex items-center justify-between mb-6 p-4 bg-gray-50/80 rounded-2xl">
          <div class="flex items-center space-x-4">
            <button
              @click="triggerFileInput"
              class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-xl text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add More Files
            </button>

            <div class="text-sm text-gray-600">
              {{ files.length }} file{{ files.length > 1 ? 's' : '' }} selected
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <!-- Pause All -->
            <button
              v-if="hasUploadingFiles"
              @click="pauseAll"
              class="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-700 rounded-xl text-sm font-medium hover:bg-orange-200 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Pause All
            </button>

            <!-- Resume All -->
            <button
              v-if="hasPausedFiles"
              @click="resumeAll"
              class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-xl text-sm font-medium hover:bg-green-200 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Resume All
            </button>

            <!-- Start Upload -->
            <button
              v-if="hasPendingFiles && !isUploading"
              @click="startUpload"
              :disabled="isUploading"
              class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
              </svg>
              Start Upload
            </button>

            <!-- Clear All -->
            <button
              @click="clearAll"
              class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Clear All
            </button>
          </div>
        </div>

        <!-- Overall Progress Bar -->
        <div v-if="files.length > 0 && (isUploading || hasCompletedFiles)" class="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl border border-indigo-200/50">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-indigo-900">Overall Progress</span>
            <span class="text-sm font-semibold text-indigo-900">{{ Math.round(overallProgress) }}%</span>
          </div>
          <div class="w-full bg-indigo-200 rounded-full h-3 overflow-hidden">
            <div 
              class="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-500 ease-out"
              :style="{ width: `${overallProgress}%` }"
            ></div>
          </div>
          <div class="flex justify-between items-center mt-2 text-sm text-indigo-700">
            <span>{{ completedFiles }} of {{ files.length }} files completed</span>
            <span v-if="totalUploadSpeed">{{ totalUploadSpeed }}</span>
          </div>
        </div>

        <!-- File Items -->
        <div class="space-y-4">
          <UploadProgressItem
            v-for="file in files"
            :key="file.id"
            :file="file"
            @pause="pauseFile"
            @resume="resumeFile"
            @retry="retryFile"
            @remove="removeFile"
          />
        </div>

        <!-- Hidden file input -->
        <input
          ref="fileInput"
          type="file"
          multiple
          accept="audio/*,.mp3,.wav,.ogg,.m4a,.aac,.flac,.wma"
          @change="handleFileSelect"
          class="hidden"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useToast } from 'vue-toastification'
import UploadProgressItem from './UploadProgressItem.vue'
import api from '@/services/api'

const toast = useToast()
const emit = defineEmits(['upload-complete', 'cancel'])

// State
const files = ref([])
const isDragging = ref(false)
const fileInput = ref(null)
const isUploading = ref(false)

// File validation constants
const ALLOWED_TYPES = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/aac', 'audio/flac', 'audio/x-ms-wma']
const ALLOWED_EXTENSIONS = ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac', 'wma']
const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

// Computed properties
const overallProgress = computed(() => {
  if (files.value.length === 0) return 0
  const totalProgress = files.value.reduce((sum, file) => sum + file.progress, 0)
  return totalProgress / files.value.length
})

const completedFiles = computed(() => {
  return files.value.filter(f => f.status === 'completed').length
})

const hasUploadingFiles = computed(() => {
  return files.value.some(f => f.status === 'uploading')
})

const hasPausedFiles = computed(() => {
  return files.value.some(f => f.status === 'paused')
})

const hasPendingFiles = computed(() => {
  return files.value.some(f => f.status === 'pending')
})

const hasCompletedFiles = computed(() => {
  return files.value.some(f => f.status === 'completed')
})

const totalUploadSpeed = computed(() => {
  const uploadingFiles = files.value.filter(f => f.status === 'uploading' && f.uploadSpeed)
  if (uploadingFiles.length === 0) return null

  // Sum up all upload speeds (convert to bytes/sec first)
  const totalBytesPerSec = uploadingFiles.reduce((sum, file) => {
    const speed = parseFloat(file.uploadSpeed.replace(/[^\d.]/g, ''))
    const unit = file.uploadSpeed.includes('MB') ? 1024 * 1024 : 1024
    return sum + (speed * unit)
  }, 0)

  return formatSpeed(totalBytesPerSec)
})

// Drag & Drop handlers
const handleDragEnter = (e) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e) => {
  e.preventDefault()
}

const handleDragLeave = (e) => {
  e.preventDefault()
  if (!e.currentTarget.contains(e.relatedTarget)) {
    isDragging.value = false
  }
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragging.value = false

  const droppedFiles = Array.from(e.dataTransfer.files)
  addFiles(droppedFiles)
}

// File selection
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (e) => {
  const selectedFiles = Array.from(e.target.files)
  addFiles(selectedFiles)
  e.target.value = '' // Reset input
}

// File validation
const validateFile = (file) => {
  const errors = []

  // Check file type
  const fileExtension = file.name.split('.').pop().toLowerCase()
  if (!ALLOWED_EXTENSIONS.includes(fileExtension) && !ALLOWED_TYPES.includes(file.type)) {
    errors.push('Invalid file type. Only audio files are allowed.')
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`)
  }

  return errors
}

const addFiles = (newFiles) => {
  newFiles.forEach(file => {
    const errors = validateFile(file)

    if (errors.length > 0) {
      toast.error(`${file.name}: ${errors.join(' ')}`)
      return
    }

    // Check for duplicates
    const exists = files.value.some(f => f.name === file.name && f.size === file.size)
    if (!exists) {
      const fileId = Date.now() + Math.random()
      files.value.push({
        id: fileId,
        file,
        name: file.name,
        size: file.size,
        format: file.name.split('.').pop().toLowerCase(),
        progress: 0,
        status: 'pending', // pending, uploading, paused, completed, error
        uploadedBytes: 0,
        uploadSpeed: null,
        timeRemaining: null,
        startTime: null,
        pausedTime: null,
        error: null,
        uploadDuration: null,
        abortController: null
      })
    } else {
      toast.warning(`${file.name} is already in the upload queue`)
    }
  })
}

// Upload management
const startUpload = async () => {
  isUploading.value = true
  const filesToUpload = files.value.filter(f => f.status === 'pending')

  // Upload files concurrently (max 3 at a time)
  const maxConcurrent = 3
  const uploadPromises = []

  for (let i = 0; i < filesToUpload.length; i += maxConcurrent) {
    const batch = filesToUpload.slice(i, i + maxConcurrent)
    const batchPromises = batch.map(file => uploadFile(file))
    uploadPromises.push(...batchPromises)

    // Wait for current batch to complete before starting next batch
    if (i + maxConcurrent < filesToUpload.length) {
      await Promise.allSettled(batchPromises)
    }
  }

  // Wait for all uploads to complete
  await Promise.allSettled(uploadPromises)

  isUploading.value = false

  // Show completion summary
  const successCount = files.value.filter(f => f.status === 'completed').length
  const errorCount = files.value.filter(f => f.status === 'error').length

  if (successCount > 0) {
    toast.success(`Successfully uploaded ${successCount} file${successCount > 1 ? 's' : ''}`)
    emit('upload-complete', { success: successCount, errors: errorCount })
  }

  if (errorCount > 0) {
    toast.error(`Failed to upload ${errorCount} file${errorCount > 1 ? 's' : ''}`)
  }
}

const uploadFile = async (fileItem) => {
  try {
    fileItem.status = 'uploading'
    fileItem.startTime = Date.now()
    fileItem.error = null

    // Create abort controller for pause/cancel functionality
    fileItem.abortController = new AbortController()

    const formData = new FormData()
    formData.append('audioFile', fileItem.file)
    formData.append('title', fileItem.name)

    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      signal: fileItem.abortController.signal,
      onUploadProgress: (progressEvent) => {
        if (fileItem.status === 'paused') return

        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        fileItem.progress = progress
        fileItem.uploadedBytes = progressEvent.loaded

        // Calculate upload speed and time remaining
        const elapsed = (Date.now() - fileItem.startTime) / 1000
        if (elapsed > 0) {
          const speed = progressEvent.loaded / elapsed
          const remaining = (progressEvent.total - progressEvent.loaded) / speed

          fileItem.uploadSpeed = formatSpeed(speed)
          fileItem.timeRemaining = formatTime(remaining)
        }
      }
    })

    if (response.data.success) {
      fileItem.status = 'completed'
      fileItem.progress = 100
      fileItem.uploadSpeed = null
      fileItem.timeRemaining = null
      fileItem.uploadDuration = formatDuration((Date.now() - fileItem.startTime) / 1000)
    }
  } catch (error) {
    if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
      // Upload was paused/cancelled, don't treat as error
      return
    }

    console.error('Upload error:', error)
    fileItem.status = 'error'
    fileItem.error = error.response?.data?.message || error.message || 'Upload failed'
    fileItem.uploadSpeed = null
    fileItem.timeRemaining = null
  }
}

// File control methods
const pauseFile = (fileId) => {
  const file = files.value.find(f => f.id === fileId)
  if (file && file.status === 'uploading') {
    file.status = 'paused'
    file.pausedTime = Date.now()
    file.abortController?.abort()
    file.uploadSpeed = null
    file.timeRemaining = null
  }
}

const resumeFile = async (fileId) => {
  const file = files.value.find(f => f.id === fileId)
  if (file && file.status === 'paused') {
    // Adjust start time to account for paused duration
    const pausedDuration = Date.now() - file.pausedTime
    file.startTime += pausedDuration
    file.pausedTime = null

    await uploadFile(file)
  }
}

const retryFile = async (fileId) => {
  const file = files.value.find(f => f.id === fileId)
  if (file && file.status === 'error') {
    file.progress = 0
    file.uploadedBytes = 0
    file.error = null
    file.uploadSpeed = null
    file.timeRemaining = null
    file.abortController = null

    await uploadFile(file)
  }
}

const removeFile = (fileId) => {
  const fileIndex = files.value.findIndex(f => f.id === fileId)
  if (fileIndex !== -1) {
    const file = files.value[fileIndex]

    // Cancel upload if in progress
    if (file.status === 'uploading' || file.status === 'paused') {
      file.abortController?.abort()
    }

    files.value.splice(fileIndex, 1)
  }
}

// Batch operations
const pauseAll = () => {
  files.value.forEach(file => {
    if (file.status === 'uploading') {
      pauseFile(file.id)
    }
  })
}

const resumeAll = async () => {
  const pausedFiles = files.value.filter(f => f.status === 'paused')
  for (const file of pausedFiles) {
    await resumeFile(file.id)
  }
}

const clearAll = () => {
  // Cancel all active uploads
  files.value.forEach(file => {
    if (file.status === 'uploading' || file.status === 'paused') {
      file.abortController?.abort()
    }
  })

  files.value = []
  isUploading.value = false
}

// Utility functions
const formatSpeed = (bytesPerSecond) => {
  if (bytesPerSecond === 0) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (seconds) => {
  if (!seconds || seconds === Infinity) return 'Calculating...'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}
</script>

<style scoped>
/* Enhanced animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
