<template>
  <!-- Modern Glassmorphism Modal -->
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center p-4" @click="$emit('close')">
    <!-- Enhanced Backdrop with Blur -->
    <div class="absolute inset-0 bg-gradient-to-br from-black/40 via-gray-900/30 to-black/50 backdrop-blur-md"></div>

    <!-- Modal Container -->
    <div
      class="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 max-w-lg w-full transform transition-all duration-300 hover:shadow-3xl"
      @click.stop
    >
      <!-- Gradient Border Effect -->
      <div class="absolute inset-0 rounded-3xl bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 p-[1px]">
        <div class="h-full w-full rounded-3xl bg-white/95 backdrop-blur-xl"></div>
      </div>

      <!-- Content Container -->
      <div class="relative z-10">
        <!-- Header with Icon -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                Audio Metadata
              </h3>
              <p class="text-sm text-gray-500">Detailed file information</p>
            </div>
          </div>

          <!-- Close Button -->
          <button
            @click="$emit('close')"
            class="p-2 rounded-xl bg-gray-100/80 hover:bg-gray-200/80 text-gray-500 hover:text-gray-700 transition-all duration-200 hover:scale-105"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- File Name Display -->
        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-2xl border border-blue-100/50">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="font-semibold text-gray-900 truncate">{{ file?.name || 'Unknown File' }}</p>
              <p class="text-sm text-gray-500 truncate">{{ file?.key || 'No key available' }}</p>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-12">
          <div class="relative">
            <div class="w-16 h-16 mx-auto mb-4">
              <div class="absolute inset-0 rounded-full border-4 border-indigo-100"></div>
              <div class="absolute inset-0 rounded-full border-4 border-indigo-500 border-t-transparent animate-spin"></div>
            </div>
            <div class="space-y-2">
              <p class="text-lg font-medium text-gray-900">Loading metadata...</p>
              <p class="text-sm text-gray-500">Analyzing audio file properties</p>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="space-y-3">
            <h4 class="text-lg font-semibold text-gray-900">Unable to load metadata</h4>
            <p class="text-sm text-gray-600 max-w-sm mx-auto">{{ error }}</p>
            <button
              @click="loadMetadata"
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium rounded-xl hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Try Again
            </button>
          </div>
        </div>

        <!-- Success - Metadata Display -->
        <div v-else-if="metadata" class="space-y-6">
          <!-- Success Header -->
          <div class="flex items-center justify-center space-x-2 text-green-600 mb-6">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="font-medium">Metadata loaded successfully</span>
          </div>

          <!-- Metadata Grid -->
          <div class="grid grid-cols-2 gap-4">
            <!-- File Size -->
            <div v-if="metadata.formattedSize" class="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-4 border border-blue-200/50">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-blue-600 uppercase tracking-wide">Size</p>
                  <p class="text-lg font-bold text-gray-900">{{ metadata.formattedSize }}</p>
                </div>
              </div>
            </div>

            <!-- Duration -->
            <div v-if="metadata.formattedDuration" class="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-2xl p-4 border border-purple-200/50">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-purple-600 uppercase tracking-wide">Duration</p>
                  <p class="text-lg font-bold text-gray-900">{{ metadata.formattedDuration }}</p>
                </div>
              </div>
            </div>

            <!-- Bitrate -->
            <div v-if="metadata.formattedBitrate" class="bg-gradient-to-br from-green-50 to-green-100/50 rounded-2xl p-4 border border-green-200/50">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-green-600 uppercase tracking-wide">Bitrate</p>
                  <p class="text-lg font-bold text-gray-900">{{ metadata.formattedBitrate }}</p>
                </div>
              </div>
            </div>

            <!-- Format -->
            <div v-if="metadata.format?.name" class="bg-gradient-to-br from-orange-50 to-orange-100/50 rounded-2xl p-4 border border-orange-200/50">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-orange-600 uppercase tracking-wide">Format</p>
                  <p class="text-lg font-bold text-gray-900">{{ metadata.format.name }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quality Score -->
          <div v-if="metadata.qualityScore" class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-200/50">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-indigo-600 uppercase tracking-wide">Quality Score</p>
                  <p class="text-2xl font-bold text-gray-900">{{ metadata.qualityScore }}<span class="text-lg text-gray-500">/100</span></p>
                </div>
              </div>
            </div>

            <!-- Quality Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                class="h-full rounded-full transition-all duration-1000 ease-out"
                :class="getQualityBarClass(metadata.qualityScore)"
                :style="{ width: `${metadata.qualityScore}%` }"
              ></div>
            </div>

            <!-- Quality Badges -->
            <div class="flex flex-wrap gap-2 mt-4">
              <span v-if="metadata.isLossless" class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Lossless
              </span>
              <span v-else-if="metadata.isHighQuality" class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                High Quality
              </span>
              <span v-if="metadata.format?.type" class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                {{ metadata.format.type === 'lossless' ? 'Lossless' : 'Lossy' }} Compression
              </span>
            </div>
          </div>

          <!-- Advanced Details (Collapsible) -->
          <details class="group">
            <summary class="cursor-pointer flex items-center justify-between p-4 bg-gray-50/80 rounded-2xl hover:bg-gray-100/80 transition-colors">
              <span class="font-medium text-gray-700">Advanced Details</span>
              <svg class="w-5 h-5 text-gray-500 group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </summary>
            <div class="mt-4 p-4 bg-gray-50/50 rounded-2xl">
              <pre class="text-xs text-gray-600 overflow-auto whitespace-pre-wrap">{{ JSON.stringify(metadata, null, 2) }}</pre>
            </div>
          </details>
        </div>

        <!-- No Data State -->
        <div v-else class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div class="space-y-3">
            <h4 class="text-lg font-semibold text-gray-900">No metadata available</h4>
            <p class="text-sm text-gray-600 max-w-sm mx-auto">Unable to extract metadata from this file</p>
            <button
              @click="loadMetadata"
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Load Metadata
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200/50">
          <button
            @click="$emit('close')"
            class="px-6 py-3 bg-gray-100/80 hover:bg-gray-200/80 text-gray-700 font-medium rounded-xl transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useFilesStore } from '../stores/files'

const filesStore = useFilesStore()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close'])

const loading = ref(false)
const error = ref(null)
const metadata = ref(null)

// Watch for changes
watch(() => props.show, (show) => {
  if (show && props.file) {
    loadMetadata()
  }
})

watch(() => props.file, (file) => {
  if (file && props.show) {
    loadMetadata()
  }
})

const loadMetadata = async () => {
  if (!props.file) {
    error.value = 'No file provided'
    return
  }

  console.log('🔍 Loading metadata for:', props.file)

  loading.value = true
  error.value = null
  metadata.value = null

  try {
    const result = await filesStore.getFileMetadata(props.file.key)
    console.log('📊 Metadata result:', result)
    metadata.value = result
  } catch (err) {
    console.error('❌ Metadata error:', err)
    error.value = err.response?.data?.message || err.message || 'Failed to load metadata'
  } finally {
    loading.value = false
  }
}

const getQualityBarClass = (score) => {
  if (score >= 80) return 'bg-gradient-to-r from-green-400 to-green-500'
  if (score >= 60) return 'bg-gradient-to-r from-yellow-400 to-yellow-500'
  if (score >= 40) return 'bg-gradient-to-r from-orange-400 to-orange-500'
  return 'bg-gradient-to-r from-red-400 to-red-500'
}
</script>

<style scoped>
/* Enhanced backdrop blur effect */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
}

/* Custom shadow for modal */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Smooth animations */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom gradient border animation */
@keyframes gradient-border {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-border {
  background-size: 200% 200%;
  animation: gradient-border 3s ease infinite;
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
}
</style>
