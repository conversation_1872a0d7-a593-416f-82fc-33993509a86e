import axios from 'axios'
import { validateSecurityHeaders, rateLimiters } from '@/utils/security'

// Debug: Log the environment variable
console.log('VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL)
console.log('All env vars:', import.meta.env)

// Create axios instance
// Use environment variable if available, otherwise fall back to relative path
const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'
console.log('API Base URL:', baseURL)
console.log('Environment:', import.meta.env.MODE)

const api = axios.create({
  baseURL: baseURL, // Use direct URL in dev, proxy in prod
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

console.log('API baseURL:', api.defaults.baseURL)

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add security headers
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    config.headers['Cache-Control'] = 'no-cache'

    // Rate limiting check for sensitive endpoints
    const endpoint = config.url
    if (endpoint?.includes('/auth/login') && !rateLimiters.login.isAllowed('login')) {
      return Promise.reject(new Error('Too many login attempts. Please wait before trying again.'))
    }

    if (endpoint?.includes('/upload') && !rateLimiters.upload.isAllowed('upload')) {
      return Promise.reject(new Error('Upload rate limit exceeded. Please wait before uploading more files.'))
    }

    if (endpoint?.includes('/download') && !rateLimiters.download.isAllowed('download')) {
      return Promise.reject(new Error('Download rate limit exceeded. Please wait before downloading more files.'))
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Validate security headers in development
    if (process.env.NODE_ENV === 'development') {
      validateSecurityHeaders(response)
    }

    return response
  },
  (error) => {
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('token')
          delete api.defaults.headers.common['Authorization']

          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          // Forbidden - insufficient permissions
          console.warn('Access denied:', data?.message || 'Insufficient permissions')
          break
        case 429:
          // Rate limited
          console.warn('Rate limited:', data?.message || 'Too many requests')
          break
        case 500:
          // Server error
          console.error('Server error:', data?.message || 'Internal server error')
          break
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message)
    }

    return Promise.reject(error)
  }
)

// Dashboard API methods
export const dashboardAPI = {
  // Get dashboard statistics
  getStats: () => api.get('/files/stats'),

  // Get recent files
  getRecentFiles: (limit = 10) => api.get(`/files/recent?limit=${limit}`)
}

export default api
