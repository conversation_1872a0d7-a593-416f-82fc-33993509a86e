import api from './api'

class UserService {
  /**
   * Get all users with pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Users with pagination info
   */
  async getUsers(params = {}) {
    try {
      const response = await api.get('/users', { params })
      return response.data
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  }

  /**
   * Get user by ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User details
   */
  async getUserById(userId) {
    try {
      const response = await api.get(`/users/${userId}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      const response = await api.post('/users', userData)
      return response.data
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  /**
   * Update user
   * @param {number} userId - User ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, updateData) {
    try {
      const response = await api.put(`/users/${userId}`, updateData)
      return response.data
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Delete user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Success response
   */
  async deleteUser(userId) {
    try {
      const response = await api.delete(`/users/${userId}`)
      return response.data
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Assign roles to user
   * @param {number} userId - User ID
   * @param {Array} roleIds - Array of role IDs
   * @returns {Promise<Object>} Success response
   */
  async assignRoles(userId, roleIds) {
    try {
      const response = await api.post(`/users/${userId}/roles`, { roleIds })
      return response.data
    } catch (error) {
      console.error(`Error assigning roles to user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get user permissions
   * @param {number} userId - User ID
   * @returns {Promise<Array>} User permissions
   */
  async getUserPermissions(userId) {
    try {
      const response = await api.get(`/users/${userId}/permissions`)
      return response.data
    } catch (error) {
      console.error(`Error fetching permissions for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get all available roles
   * @returns {Promise<Array>} Available roles
   */
  async getRoles() {
    try {
      const response = await api.get('/users/roles')
      return response.data
    } catch (error) {
      console.error('Error fetching roles:', error)
      throw error
    }
  }

  /**
   * Get all subsidiaries
   * @returns {Promise<Array>} Available subsidiaries
   */
  async getSubsidiaries() {
    try {
      const response = await api.get('/users/subsidiaries')
      return response.data
    } catch (error) {
      console.error('Error fetching subsidiaries:', error)
      throw error
    }
  }

  /**
   * Get role hierarchy levels for display
   * @returns {Object} Role hierarchy mapping
   */
  getRoleHierarchy() {
    return {
      5: { name: 'Admin', color: 'red', icon: 'crown' },
      3: { name: 'QA / Compliance Officer', color: 'blue', icon: 'shield-check' },
      2: { name: 'User', color: 'green', icon: 'user' },
      1: { name: 'Guest', color: 'gray', icon: 'eye' }
    }
  }

  /**
   * Get permission categories for display
   * @returns {Object} Permission categories
   */
  getPermissionCategories() {
    return {
      'files': {
        name: 'File Management',
        icon: 'folder',
        color: 'blue',
        permissions: ['files:read', 'files:write', 'files:delete', 'files:download', 'files:upload', 'files:flag', 'files:tag', 'files:rate', 'files:comment']
      },
      'users': {
        name: 'User Management',
        icon: 'users',
        color: 'green',
        permissions: ['users:read', 'users:write', 'users:delete', 'users:assign_roles']
      },
      'system': {
        name: 'System Administration',
        icon: 'cog',
        color: 'purple',
        permissions: ['system:admin', 'system:health', 'settings:manage']
      },
      'analytics': {
        name: 'Analytics & Reports',
        icon: 'chart-bar',
        color: 'yellow',
        permissions: ['analytics:read', 'reports:qa']
      },
      'audit': {
        name: 'Audit & Compliance',
        icon: 'shield-check',
        color: 'red',
        permissions: ['audit:read', 'compliance:review', 'retention:view', 'access_logs:view']
      },
      'settings': {
        name: 'Settings',
        icon: 'settings',
        color: 'gray',
        permissions: ['settings:manage']
      }
    }
  }
}

export default new UserService()
