import api from './api'

export const subsidiaryService = {
  /**
   * Get all active subsidiaries for login selection
   */
  async getActiveSubsidiaries() {
    try {
      const response = await api.get('/subsidiaries/active')
      return response.data
    } catch (error) {
      console.error('Failed to fetch active subsidiaries:', error)
      throw error
    }
  },

  /**
   * Get all subsidiaries (admin only)
   */
  async getAllSubsidiaries() {
    try {
      const response = await api.get('/subsidiaries')
      return response.data
    } catch (error) {
      console.error('Failed to fetch subsidiaries:', error)
      throw error
    }
  },

  /**
   * Get subsidiary by ID
   */
  async getSubsidiaryById(id) {
    try {
      const response = await api.get(`/subsidiaries/${id}`)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch subsidiary ${id}:`, error)
      throw error
    }
  },

  /**
   * Create new subsidiary (super admin only)
   */
  async createSubsidiary(subsidiaryData) {
    try {
      const response = await api.post('/subsidiaries', subsidiaryData)
      return response.data
    } catch (error) {
      console.error('Failed to create subsidiary:', error)
      throw error
    }
  },

  /**
   * Update subsidiary (admin only)
   */
  async updateSubsidiary(id, subsidiaryData) {
    try {
      const response = await api.put(`/subsidiaries/${id}`, subsidiaryData)
      return response.data
    } catch (error) {
      console.error(`Failed to update subsidiary ${id}:`, error)
      throw error
    }
  },

  /**
   * Delete subsidiary (super admin only)
   */
  async deleteSubsidiary(id) {
    try {
      const response = await api.delete(`/subsidiaries/${id}`)
      return response.data
    } catch (error) {
      console.error(`Failed to delete subsidiary ${id}:`, error)
      throw error
    }
  },

  /**
   * Get subsidiary settings
   */
  async getSubsidiarySettings(id) {
    try {
      const response = await api.get(`/subsidiaries/${id}/settings`)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch subsidiary ${id} settings:`, error)
      throw error
    }
  },

  /**
   * Update subsidiary settings
   */
  async updateSubsidiarySettings(id, settings) {
    try {
      const response = await api.put(`/subsidiaries/${id}/settings`, settings)
      return response.data
    } catch (error) {
      console.error(`Failed to update subsidiary ${id} settings:`, error)
      throw error
    }
  }
}
