import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Views
import LoginView from '@/views/LoginView.vue'
import PasswordResetView from '@/views/PasswordResetView.vue'
import DashboardView from '@/views/DashboardView.vue'
import AudioFilesView from '@/views/AudioFilesView.vue'
import UploadView from '@/views/UploadView.vue'
import UserManagementView from '@/views/UserManagementView.vue'
import AuditView from '@/views/AuditView.vue'

const routes = [
  {
    path: '/',
    redirect: { name: 'Login' }
  },
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: { requiresGuest: true }
  },
  {
    path: '/password-reset',
    name: 'PasswordReset',
    component: PasswordResetView,
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: DashboardView,
    meta: { requiresAuth: true }
  },
  {
    path: '/files',
    name: 'AudioFiles',
    component: AudioFilesView,
    meta: { requiresAuth: true }
  },
  {
    path: '/upload',
    name: 'Upload',
    component: UploadView,
    meta: { requiresAuth: true }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: () => import('@/views/AnalyticsView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'UserManagement',
    component: UserManagementView,
    meta: {
      requiresAuth: true,
      requiresPermission: 'users:read'
    }
  },
  {
    path: '/audit',
    name: 'Audit',
    component: AuditView,
    meta: {
      requiresAuth: true,
      requiresRole: ['ADMIN', 'COMPLIANCE']
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue')
  }
]

const router = createRouter({
  history: createWebHistory('/audio-manager/'),
  routes
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'Login' })
    return
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }

  // Check if route requires specific permission
  if (to.meta.requiresPermission && authStore.isAuthenticated) {
    if (!authStore.permissions.includes(to.meta.requiresPermission)) {
      // Redirect to dashboard if user doesn't have permission
      next({ name: 'Dashboard' })
      return
    }
  }

  // Check if route requires specific role
  if (to.meta.requiresRole && authStore.isAuthenticated) {
    const userRoles = authStore.user?.roles?.map(role => role.code) || []
    const requiredRoles = Array.isArray(to.meta.requiresRole) ? to.meta.requiresRole : [to.meta.requiresRole]
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role))

    if (!hasRequiredRole) {
      // Redirect to dashboard if user doesn't have required role
      next({ name: 'Dashboard' })
      return
    }
  }

  next()
})

export default router
