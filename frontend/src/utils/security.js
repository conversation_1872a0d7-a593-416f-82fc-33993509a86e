/**
 * Frontend Security Utilities
 * Provides client-side security functions and validation
 */

/**
 * Sanitize user input to prevent XSS attacks
 * @param {string} input - User input to sanitize
 * @returns {string} - Sanitized input
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Validate file types for uploads
 * @param {File} file - File to validate
 * @returns {Object} - Validation result
 */
export const validateFileType = (file) => {
  const allowedTypes = [
    'audio/mpeg',
    'audio/wav',
    'audio/flac',
    'audio/aac',
    'audio/ogg',
    'audio/mp4',
    'audio/x-m4a'
  ];
  
  const allowedExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'];
  
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  
  return {
    isValid: allowedTypes.includes(file.type) && allowedExtensions.includes(fileExtension),
    type: file.type,
    extension: fileExtension,
    message: allowedTypes.includes(file.type) && allowedExtensions.includes(fileExtension) 
      ? 'Valid audio file' 
      : 'Invalid file type. Only audio files are allowed.'
  };
};

/**
 * Validate file size
 * @param {File} file - File to validate
 * @param {number} maxSizeMB - Maximum size in MB (default: 100MB)
 * @returns {Object} - Validation result
 */
export const validateFileSize = (file, maxSizeMB = 100) => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  return {
    isValid: file.size <= maxSizeBytes,
    size: file.size,
    maxSize: maxSizeBytes,
    message: file.size <= maxSizeBytes 
      ? 'File size is acceptable' 
      : `File size exceeds ${maxSizeMB}MB limit`
  };
};

/**
 * Validate filename for security
 * @param {string} filename - Filename to validate
 * @returns {Object} - Validation result
 */
export const validateFilename = (filename) => {
  // Check for path traversal attempts
  const hasPathTraversal = filename.includes('..') || filename.includes('/') || filename.includes('\\');
  
  // Check for dangerous characters
  const dangerousChars = /[<>:"|?*\x00-\x1f]/;
  const hasDangerousChars = dangerousChars.test(filename);
  
  // Check length
  const isValidLength = filename.length > 0 && filename.length <= 255;
  
  const isValid = !hasPathTraversal && !hasDangerousChars && isValidLength;
  
  return {
    isValid,
    filename,
    message: isValid 
      ? 'Filename is valid' 
      : 'Invalid filename. Contains dangerous characters or path traversal attempts.'
  };
};

/**
 * Generate secure random string
 * @param {number} length - Length of random string
 * @returns {string} - Random string
 */
export const generateSecureRandom = (length = 32) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  if (window.crypto && window.crypto.getRandomValues) {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    
    for (let i = 0; i < length; i++) {
      result += chars[array[i] % chars.length];
    }
  } else {
    // Fallback for older browsers
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
};

/**
 * Validate URL to prevent open redirect attacks
 * @param {string} url - URL to validate
 * @returns {boolean} - Whether URL is safe
 */
export const isValidRedirectUrl = (url) => {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url, window.location.origin);
    
    // Only allow same origin redirects
    return urlObj.origin === window.location.origin;
  } catch {
    return false;
  }
};

/**
 * Secure localStorage wrapper with encryption
 */
export const secureStorage = {
  /**
   * Set item in localStorage with basic obfuscation
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   */
  setItem(key, value) {
    try {
      const serialized = JSON.stringify(value);
      const encoded = btoa(serialized); // Basic encoding
      localStorage.setItem(key, encoded);
    } catch (error) {
      console.error('Failed to store item:', error);
    }
  },

  /**
   * Get item from localStorage with decoding
   * @param {string} key - Storage key
   * @returns {any} - Stored value or null
   */
  getItem(key) {
    try {
      const encoded = localStorage.getItem(key);
      if (!encoded) return null;
      
      const serialized = atob(encoded);
      return JSON.parse(serialized);
    } catch (error) {
      console.error('Failed to retrieve item:', error);
      return null;
    }
  },

  /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   */
  removeItem(key) {
    localStorage.removeItem(key);
  },

  /**
   * Clear all items from localStorage
   */
  clear() {
    localStorage.clear();
  }
};

/**
 * Rate limiting for client-side actions
 */
export class ClientRateLimit {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = new Map();
  }

  /**
   * Check if action is allowed
   * @param {string} key - Unique key for the action
   * @returns {boolean} - Whether action is allowed
   */
  isAllowed(key) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    if (!this.requests.has(key)) {
      this.requests.set(key, []);
    }
    
    const keyRequests = this.requests.get(key);
    
    // Remove old requests outside the window
    const validRequests = keyRequests.filter(timestamp => timestamp > windowStart);
    this.requests.set(key, validRequests);
    
    // Check if under limit
    if (validRequests.length < this.maxRequests) {
      validRequests.push(now);
      return true;
    }
    
    return false;
  }

  /**
   * Get remaining requests for a key
   * @param {string} key - Unique key for the action
   * @returns {number} - Remaining requests
   */
  getRemainingRequests(key) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    if (!this.requests.has(key)) {
      return this.maxRequests;
    }
    
    const keyRequests = this.requests.get(key);
    const validRequests = keyRequests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}

/**
 * Content Security Policy helpers
 */
export const csp = {
  /**
   * Check if inline scripts are allowed (for development)
   * @returns {boolean} - Whether inline scripts are allowed
   */
  allowsInlineScripts() {
    return process.env.NODE_ENV === 'development';
  },

  /**
   * Generate nonce for inline scripts
   * @returns {string} - Nonce value
   */
  generateNonce() {
    return generateSecureRandom(16);
  }
};

/**
 * Security headers validation
 */
export const validateSecurityHeaders = (response) => {
  const headers = response.headers;
  const warnings = [];

  if (!headers.get('x-content-type-options')) {
    warnings.push('Missing X-Content-Type-Options header');
  }

  if (!headers.get('x-frame-options')) {
    warnings.push('Missing X-Frame-Options header');
  }

  if (!headers.get('x-xss-protection')) {
    warnings.push('Missing X-XSS-Protection header');
  }

  if (!headers.get('strict-transport-security')) {
    warnings.push('Missing Strict-Transport-Security header');
  }

  if (warnings.length > 0 && process.env.NODE_ENV === 'development') {
    console.warn('Security headers missing:', warnings);
  }

  return warnings;
};

// Create default rate limiters for common actions
export const rateLimiters = {
  login: new ClientRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  upload: new ClientRateLimit(10, 60 * 1000), // 10 uploads per minute
  download: new ClientRateLimit(20, 60 * 1000), // 20 downloads per minute
  search: new ClientRateLimit(30, 60 * 1000) // 30 searches per minute
};
