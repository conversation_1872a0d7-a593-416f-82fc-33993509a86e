/**
 * Composable for centralized error handling and user feedback
 */
import { ref, computed } from 'vue'
import { useToast } from 'vue-toastification'

export function useErrorHandler() {
  const toast = useToast()
  
  // Error state management
  const errors = ref(new Map())
  const isLoading = ref(new Map())
  const retryAttempts = ref(new Map())
  
  // Maximum retry attempts for different error types
  const MAX_RETRIES = {
    network: 3,
    server: 2,
    auth: 1,
    validation: 0
  }
  
  // Error type classification
  const getErrorType = (error) => {
    if (!error.response) {
      return 'network'
    }
    
    const status = error.response.status
    if (status >= 500) return 'server'
    if (status === 401 || status === 403) return 'auth'
    if (status >= 400 && status < 500) return 'validation'
    return 'unknown'
  }
  
  // User-friendly error messages
  const getErrorMessage = (error, context = '') => {
    const type = getErrorType(error)
    const status = error.response?.status
    const serverMessage = error.response?.data?.message
    
    // Use server message if available and user-friendly
    if (serverMessage && !serverMessage.includes('Error:') && serverMessage.length < 100) {
      return serverMessage
    }
    
    // Default messages based on error type and context
    const messages = {
      network: {
        default: 'Unable to connect to the server. Please check your internet connection.',
        upload: 'Upload failed due to connection issues. Please try again.',
        download: 'Download failed due to connection issues. Please try again.',
        login: 'Unable to connect for login. Please check your connection.'
      },
      server: {
        default: 'Server is temporarily unavailable. Please try again in a moment.',
        upload: 'Server error during upload. Please try again later.',
        download: 'Server error during download. Please try again later.',
        login: 'Login service is temporarily unavailable.'
      },
      auth: {
        default: status === 401 ? 'Your session has expired. Please log in again.' : 'You don\'t have permission to perform this action.',
        upload: 'You don\'t have permission to upload files.',
        download: 'You don\'t have permission to download this file.',
        login: 'Invalid username or password.'
      },
      validation: {
        default: 'Please check your input and try again.',
        upload: 'Invalid file or upload data. Please check and try again.',
        download: 'Invalid download request.',
        login: 'Please enter valid login credentials.'
      }
    }
    
    return messages[type]?.[context] || messages[type]?.default || 'An unexpected error occurred.'
  }
  
  // Handle error with automatic retry logic
  const handleError = async (error, context = '', retryFn = null) => {
    const errorKey = `${context}_${Date.now()}`
    const type = getErrorType(error)
    
    console.error(`Error in ${context}:`, error)
    
    // Store error
    errors.value.set(errorKey, {
      error,
      context,
      type,
      timestamp: new Date(),
      message: getErrorMessage(error, context)
    })
    
    // Check if we should retry
    const currentRetries = retryAttempts.value.get(context) || 0
    const maxRetries = MAX_RETRIES[type] || 0
    
    if (retryFn && currentRetries < maxRetries) {
      // Increment retry count
      retryAttempts.value.set(context, currentRetries + 1)
      
      // Show retry toast
      toast.warning(`Retrying... (${currentRetries + 1}/${maxRetries})`)
      
      // Exponential backoff delay
      const delay = Math.min(1000 * Math.pow(2, currentRetries), 5000)
      
      setTimeout(async () => {
        try {
          await retryFn()
          // Success - clear retry count
          retryAttempts.value.delete(context)
          clearError(errorKey)
        } catch (retryError) {
          // Retry failed - handle recursively
          await handleError(retryError, context, currentRetries + 1 < maxRetries ? retryFn : null)
        }
      }, delay)
      
      return
    }
    
    // No more retries - show final error
    const message = getErrorMessage(error, context)
    
    // Show appropriate toast based on error severity
    if (type === 'auth') {
      toast.error(message, { timeout: 8000 })
    } else if (type === 'server') {
      toast.error(message, { timeout: 6000 })
    } else if (type === 'network') {
      toast.warning(message, { timeout: 5000 })
    } else {
      toast.error(message, { timeout: 4000 })
    }
    
    // Clear retry count
    retryAttempts.value.delete(context)
    
    return errorKey
  }
  
  // Clear specific error
  const clearError = (errorKey) => {
    errors.value.delete(errorKey)
  }
  
  // Clear all errors for a context
  const clearContextErrors = (context) => {
    for (const [key, error] of errors.value.entries()) {
      if (error.context === context) {
        errors.value.delete(key)
      }
    }
  }
  
  // Set loading state
  const setLoading = (context, loading) => {
    if (loading) {
      isLoading.value.set(context, true)
    } else {
      isLoading.value.delete(context)
    }
  }
  
  // Get loading state
  const getLoading = (context) => {
    return isLoading.value.get(context) || false
  }
  
  // Get errors for a context
  const getContextErrors = (context) => {
    return Array.from(errors.value.values()).filter(error => error.context === context)
  }
  
  // Check if context has errors
  const hasErrors = (context) => {
    return getContextErrors(context).length > 0
  }
  
  // Computed properties
  const hasAnyErrors = computed(() => errors.value.size > 0)
  const isAnyLoading = computed(() => isLoading.value.size > 0)
  const errorCount = computed(() => errors.value.size)
  
  // Success handler
  const handleSuccess = (message, context = '') => {
    // Clear any existing errors for this context
    clearContextErrors(context)
    
    // Show success toast
    if (message) {
      toast.success(message)
    }
  }
  
  // Warning handler
  const handleWarning = (message, context = '') => {
    toast.warning(message)
  }
  
  // Info handler
  const handleInfo = (message, context = '') => {
    toast.info(message)
  }
  
  // Async operation wrapper with error handling
  const withErrorHandling = async (operation, context = '', options = {}) => {
    const {
      loadingMessage = '',
      successMessage = '',
      retryFn = null,
      showLoading = true
    } = options
    
    try {
      if (showLoading) {
        setLoading(context, true)
        if (loadingMessage) {
          toast.info(loadingMessage)
        }
      }
      
      clearContextErrors(context)
      
      const result = await operation()
      
      if (successMessage) {
        handleSuccess(successMessage, context)
      }
      
      return result
    } catch (error) {
      await handleError(error, context, retryFn)
      throw error
    } finally {
      if (showLoading) {
        setLoading(context, false)
      }
    }
  }
  
  // Network status monitoring
  const isOnline = ref(navigator.onLine)
  
  const handleOnline = () => {
    isOnline.value = true
    toast.success('Connection restored')
  }
  
  const handleOffline = () => {
    isOnline.value = false
    toast.warning('Connection lost. Some features may not work.')
  }
  
  // Set up network listeners
  if (typeof window !== 'undefined') {
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
  }
  
  return {
    // State
    errors: computed(() => Array.from(errors.value.values())),
    hasAnyErrors,
    isAnyLoading,
    errorCount,
    isOnline,
    
    // Methods
    handleError,
    handleSuccess,
    handleWarning,
    handleInfo,
    clearError,
    clearContextErrors,
    setLoading,
    getLoading,
    getContextErrors,
    hasErrors,
    withErrorHandling,
    
    // Utilities
    getErrorType,
    getErrorMessage
  }
}
