# Security Configuration

## Environment Variables

This project has been configured to use environment variables for all sensitive information to improve security and avoid hardcoding credentials in the codebase.

### Setup Instructions

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your actual values:**
   - Replace all placeholder values (e.g., `your_postgres_password`) with your actual credentials
   - Update AWS credentials for each subsidiary
   - Set appropriate JWT secrets and other configuration values

3. **Never commit the `.env` file:**
   - The `.env` file is already included in `.gitignore`
   - Only commit `.env.example` with placeholder values

### Environment Variables Used

#### Database Configuration
- `POSTGRES_USER` - PostgreSQL username
- `POSTGRES_PASSWORD` - PostgreSQL password
- `POSTGRES_DB` - Database name
- `DB_HOST` - Database host (for app connection)
- `DB_PORT` - Database port
- `DB_USERNAME` - App database username
- `DB_PASSWORD` - App database password
- `DB_DBNAME` - App database name
- `DB_DIALECT` - Database dialect (postgres)

#### Redis Configuration
- `REDIS_PASSWORD` - Redis password
- `REDIS_HOST` - Redis host
- `REDIS_PORT` - Redis port

#### Application Configuration
- `NODE_ENV` - Node environment (production/development)
- `PORT` - Application port
- `JWT_SECRET` - JWT signing secret
- `JWT_EXPIRES_IN` - JWT expiration time

#### AWS Configuration
- `AWS_ACCESS_KEY_ID` - Default AWS access key
- `AWS_SECRET_ACCESS_KEY` - Default AWS secret key
- `AWS_REGION` - AWS region
- `S3_BUCKET_NAME` - Default S3 bucket

#### Subsidiary-specific AWS Credentials
Each subsidiary has its own set of AWS credentials:
- Spectrum Zambia: `SPECTRUM_*`
- Premier Fanikiwa: `FANIKIWA_*`
- Premier Uganda: `PREMIER_UG_*`
- Platinum Uganda: `PLATINUM_UG_*`
- Premier Kenya: `PREMIER_KE_*`
- Momentum Credit: `MOMENTUM_*`
- Platinum Kenya: `PLATINUM_KE_*`
- Platinum Tanzania: `PLATINUM_TZ_*`

#### External Port Mappings
- `POSTGRES_EXTERNAL_PORT` - External port for PostgreSQL (default: 5433)
- `REDIS_EXTERNAL_PORT` - External port for Redis (default: 6380)

### Security Best Practices

1. **Environment Variables**: All sensitive data is now stored in environment variables
2. **Git Ignore**: The `.env` file is excluded from version control
3. **Example File**: `.env.example` provides a template without exposing real credentials
4. **Docker Compose**: Uses environment variable substitution instead of hardcoded values
5. **Separation of Concerns**: Different credentials for different subsidiaries

### Running the Application

After setting up your `.env` file:

```bash
# Build and start the services
docker compose -f docker-compose.yml up -d

# Check if services are running
docker compose -f docker-compose.yml ps

# View logs
docker compose -f docker-compose.yml logs -f app
```

### Troubleshooting

If you encounter issues:

1. **Check environment variables are loaded:**
   ```bash
   docker compose -f docker-compose.yml config
   ```

2. **Verify container environment:**
   ```bash
   docker exec audio_vault_app env | grep -E "(DB_|REDIS_|AWS_)"
   ```

3. **Check logs for configuration errors:**
   ```bash
   docker compose -f docker-compose.yml logs app
   ```

### Production Deployment

For production deployment:

1. Use strong, unique passwords for all services
2. Rotate JWT secrets regularly
3. Use AWS IAM roles instead of access keys where possible
4. Enable SSL/TLS for all connections
5. Regularly audit and rotate credentials
6. Use secrets management services (AWS Secrets Manager, HashiCorp Vault, etc.)
